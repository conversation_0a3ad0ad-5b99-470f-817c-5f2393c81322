﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PublishAot>true</PublishAot>
    <InvariantGlobalization>true</InvariantGlobalization>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <SelfContained>true</SelfContained>
    <IsAotCompatible>true</IsAotCompatible>
  </PropertyGroup>

  <ItemGroup>
  </ItemGroup>

  <ItemGroup>
    <TrimmerRootAssembly Include="AutoGen.Core" />
    <TrimmerRootAssembly Update="@(TrimmerRootAssembly)" Path="$(RepoRoot)\src\%(Identity)\%(Identity).csproj" />
    <ProjectReference Include="@(TrimmerRootAssembly->'%(Path)')" />
  </ItemGroup>

</Project>
