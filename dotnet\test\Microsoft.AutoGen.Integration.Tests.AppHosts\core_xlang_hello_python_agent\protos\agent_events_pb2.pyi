"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class TextMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEXTMESSAGE_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    textMessage: builtins.str
    source: builtins.str
    def __init__(
        self,
        *,
        textMessage: builtins.str = ...,
        source: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["source", b"source", "textMessage", b"textMessage"]) -> None: ...

global___TextMessage = TextMessage

@typing.final
class Input(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___Input = Input

@typing.final
class InputProcessed(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROUTE_FIELD_NUMBER: builtins.int
    route: builtins.str
    def __init__(
        self,
        *,
        route: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["route", b"route"]) -> None: ...

global___InputProcessed = InputProcessed

@typing.final
class Output(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___Output = Output

@typing.final
class OutputWritten(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROUTE_FIELD_NUMBER: builtins.int
    route: builtins.str
    def __init__(
        self,
        *,
        route: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["route", b"route"]) -> None: ...

global___OutputWritten = OutputWritten

@typing.final
class IOError(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___IOError = IOError

@typing.final
class NewMessageReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___NewMessageReceived = NewMessageReceived

@typing.final
class ResponseGenerated(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESPONSE_FIELD_NUMBER: builtins.int
    response: builtins.str
    def __init__(
        self,
        *,
        response: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["response", b"response"]) -> None: ...

global___ResponseGenerated = ResponseGenerated

@typing.final
class GoodBye(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___GoodBye = GoodBye

@typing.final
class MessageStored(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___MessageStored = MessageStored

@typing.final
class ConversationClosed(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    USER_MESSAGE_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    user_message: builtins.str
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
        user_message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["user_id", b"user_id", "user_message", b"user_message"]) -> None: ...

global___ConversationClosed = ConversationClosed

@typing.final
class Shutdown(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___Shutdown = Shutdown
