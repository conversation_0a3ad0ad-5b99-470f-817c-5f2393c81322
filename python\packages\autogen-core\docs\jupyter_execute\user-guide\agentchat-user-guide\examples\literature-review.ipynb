{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Literature Review\n", "\n", "A common task while exploring a new topic is to conduct a literature review. In this example we will explore how a multi-agent team can be configured to conduct a _simple_ literature review.\n", "\n", "- **Arxiv Search Agent**: Use the Arxiv API to search for papers related to a given topic and return results.\n", "- **Google Search Agent**: Use the Google Search api to find papers related to a given topic and return results.\n", "- **Report Agent**: Generate a report based on the information collected by the arxviv search and Google search agents.\n", "\n", "\n", "First, let us import the necessary modules. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.conditions import TextMentionTermination\n", "from autogen_agentchat.teams import RoundRobinGroupChat\n", "from autogen_agentchat.ui import Console\n", "from autogen_core.tools import FunctionTool\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining Tools \n", "\n", "Next, we will define the tools that the agents will use to perform their tasks. In this case we will define a simple function `search_arxiv` that will use the `arxiv` library to search for papers related to a given topic.  \n", "\n", "Finally, we will wrap the functions into a `FunctionTool` class that will allow us to use it as a tool in the agents. \n", "\n", "Note: You will need to set the appropriate environment variables for tools as needed.\n", "\n", "Also install required libraries: \n", "\n", "```bash\n", "!pip install arxiv\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def google_search(query: str, num_results: int = 2, max_chars: int = 500) -> list:  # type: ignore[type-arg]\n", "    import os\n", "    import time\n", "\n", "    import requests\n", "    from bs4 import BeautifulSoup\n", "    from dotenv import load_dotenv\n", "\n", "    load_dotenv()\n", "\n", "    api_key = os.getenv(\"GOOGLE_API_KEY\")\n", "    search_engine_id = os.getenv(\"GOOGLE_SEARCH_ENGINE_ID\")\n", "\n", "    if not api_key or not search_engine_id:\n", "        raise ValueError(\"API key or Search Engine ID not found in environment variables\")\n", "\n", "    url = \"https://www.googleapis.com/customsearch/v1\"\n", "    params = {\"key\": api_key, \"cx\": search_engine_id, \"q\": query, \"num\": num_results}\n", "\n", "    response = requests.get(url, params=params)  # type: ignore[arg-type]\n", "\n", "    if response.status_code != 200:\n", "        print(response.json())\n", "        raise Exception(f\"Error in API request: {response.status_code}\")\n", "\n", "    results = response.json().get(\"items\", [])\n", "\n", "    def get_page_content(url: str) -> str:\n", "        try:\n", "            response = requests.get(url, timeout=10)\n", "            soup = BeautifulSoup(response.content, \"html.parser\")\n", "            text = soup.get_text(separator=\" \", strip=True)\n", "            words = text.split()\n", "            content = \"\"\n", "            for word in words:\n", "                if len(content) + len(word) + 1 > max_chars:\n", "                    break\n", "                content += \" \" + word\n", "            return content.strip()\n", "        except Exception as e:\n", "            print(f\"Error fetching {url}: {str(e)}\")\n", "            return \"\"\n", "\n", "    enriched_results = []\n", "    for item in results:\n", "        body = get_page_content(item[\"link\"])\n", "        enriched_results.append(\n", "            {\"title\": item[\"title\"], \"link\": item[\"link\"], \"snippet\": item[\"snippet\"], \"body\": body}\n", "        )\n", "        time.sleep(1)  # Be respectful to the servers\n", "\n", "    return enriched_results\n", "\n", "\n", "def arxiv_search(query: str, max_results: int = 2) -> list:  # type: ignore[type-arg]\n", "    \"\"\"\n", "    Search Arxiv for papers and return the results including abstracts.\n", "    \"\"\"\n", "    import arxiv\n", "\n", "    client = arxiv.Client()\n", "    search = arxiv.Search(query=query, max_results=max_results, sort_by=arxiv.SortCriterion.Relevance)\n", "\n", "    results = []\n", "    for paper in client.results(search):\n", "        results.append(\n", "            {\n", "                \"title\": paper.title,\n", "                \"authors\": [author.name for author in paper.authors],\n", "                \"published\": paper.published.strftime(\"%Y-%m-%d\"),\n", "                \"abstract\": paper.summary,\n", "                \"pdf_url\": paper.pdf_url,\n", "            }\n", "        )\n", "\n", "    # # Write results to a file\n", "    # with open('arxiv_search_results.json', 'w') as f:\n", "    #     json.dump(results, f, indent=2)\n", "\n", "    return results"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["google_search_tool = FunctionTool(\n", "    google_search, description=\"Search Google for information, returns results with a snippet and body content\"\n", ")\n", "arxiv_search_tool = FunctionTool(\n", "    arxiv_search, description=\"Search Arxiv for papers related to a given topic, including abstracts\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining Agents \n", "\n", "Next, we will define the agents that will perform the tasks. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_client = OpenAIChatCompletionClient(model=\"gpt-4o-mini\")\n", "\n", "google_search_agent = AssistantAgent(\n", "    name=\"Google_Search_Agent\",\n", "    tools=[google_search_tool],\n", "    model_client=model_client,\n", "    description=\"An agent that can search Google for information, returns results with a snippet and body content\",\n", "    system_message=\"You are a helpful AI assistant. Solve tasks using your tools.\",\n", ")\n", "\n", "arxiv_search_agent = AssistantAgent(\n", "    name=\"Arxiv_Search_Agent\",\n", "    tools=[arxiv_search_tool],\n", "    model_client=model_client,\n", "    description=\"An agent that can search Arxiv for papers related to a given topic, including abstracts\",\n", "    system_message=\"You are a helpful AI assistant. Solve tasks using your tools. Specifically, you can take into consideration the user's request and craft a search query that is most likely to return relevant academi papers.\",\n", ")\n", "\n", "\n", "report_agent = AssistantAgent(\n", "    name=\"Report_Agent\",\n", "    model_client=model_client,\n", "    description=\"Generate a report based on a given topic\",\n", "    system_message=\"You are a helpful assistant. Your task is to synthesize data extracted into a high quality literature review including CORRECT references. You MUST write a final report that is formatted as a literature review with CORRECT references.  Your response should end with the word 'TERMINATE'\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Team \n", "\n", "Finally, we will create a team of agents and configure them to perform the tasks."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["termination = TextMentionTermination(\"TERMINATE\")\n", "team = RoundRobinGroupChat(\n", "    participants=[google_search_agent, arxiv_search_agent, report_agent], termination_condition=termination\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "Write a literature review on no code tools for building multi agent ai systems\n", "---------- Google_Search_Agent ----------\n", "[FunctionCall(id='call_bNGwWFsfeTwDhtIpsI6GYISR', arguments='{\"query\":\"no code tools for building multi agent AI systems literature review\",\"num_results\":3}', name='google_search')]\n", "[Prompt tokens: 123, Completion tokens: 29]\n", "---------- Google_Search_Agent ----------\n", "[FunctionExecutionResult(content='[{\\'title\\': \\'Literature Review — AutoGen\\', \\'link\\': \\'https://microsoft.github.io/autogen/dev//user-guide/agentchat-user-guide/examples/literature-review.html\\', \\'snippet\\': \\'run( task=\"Write a literature review on no code tools for building multi agent ai systems\", ) ... ### Conclusion No-code tools for building multi-agent AI systems\\\\xa0...\\', \\'body\\': \\'Literature Review — AutoGen Skip to main content Back to top Ctrl + K AutoGen 0.4 is a work in progress. Go here to find the 0.2 documentation. User Guide Packages API Reference Twitter GitHub PyPI User Guide Packages API Reference Twitter GitHub PyPI AgentChat Installation Quickstart Tutorial Models Messages Agents Teams Selector Group Chat Swarm Termination Custom Agents Managing State Examples Travel Planning Company Research Literature Review Core Quick Start Core Concepts Agent and\\'}, {\\'title\\': \\'Vertex AI Agent Builder | Google Cloud\\', \\'link\\': \\'https://cloud.google.com/products/agent-builder\\', \\'snippet\\': \\'Build and deploy enterprise ready generative AI experiences · Product highlights · Easily build no code conversational AI agents · Ground in Google search and/or\\\\xa0...\\', \\'body\\': \\'Vertex AI Agent Builder | Google Cloud Page Contents Vertex AI Agent Builder is making generative AI more reliable for the enterprise. Read the blog. Vertex AI Agent Builder Build and deploy enterprise ready generative AI experiences Create AI agents and applications using natural language or a code-first approach. Easily ground your agents or apps in enterprise data with a range of options. Vertex AI Agent Builder gathers all the surfaces and tools that developers need to build their AI agents\\'}, {\\'title\\': \\'AI tools I have found useful w/ research. What do you guys think ...\\', \\'link\\': \\'https://www.reddit.com/r/PhD/comments/14d6g09/ai_tools_i_have_found_useful_w_research_what_do/\\', \\'snippet\\': \"Jun 19, 2023 ... Need help deciding on the best ones, and to identify ones I\\'ve missed: ASSISTANTS (chatbots, multi-purpose) Chat with Open Large Language Models.\", \\'body\\': \\'Reddit - Dive into anything Skip to main content Open menu Open navigation Go to Reddit Home r/PhD A chip A close button Get app Get the Reddit app Log In Log in to Reddit Expand user menu Open settings menu Log In / Sign Up Advertise on Reddit Shop Collectible Avatars Get the Reddit app Scan this QR code to download the app now Or check it out in the app stores Go to PhD r/PhD r/PhD A subreddit dedicated to PhDs. Members Online • [deleted] ADMIN MOD AI tools I have found useful w/ research.\\'}]', call_id='call_bNGwWFsfeTwDhtIpsI6GYISR')]\n", "---------- Google_Search_Agent ----------\n", "Tool calls:\n", "google_search({\"query\":\"no code tools for building multi agent AI systems literature review\",\"num_results\":3}) = [{'title': 'Literature Review — AutoGen', 'link': 'https://microsoft.github.io/autogen/dev//user-guide/agentchat-user-guide/examples/literature-review.html', 'snippet': 'run( task=\"Write a literature review on no code tools for building multi agent ai systems\", ) ... ### Conclusion No-code tools for building multi-agent AI systems\\xa0...', 'body': 'Literature Review — AutoGen Skip to main content Back to top Ctrl + K AutoGen 0.4 is a work in progress. Go here to find the 0.2 documentation. User Guide Packages API Reference Twitter GitHub PyPI User Guide Packages API Reference Twitter GitHub PyPI AgentChat Installation Quickstart Tutorial Models Messages Agents Teams Selector Group Chat Swarm Termination Custom Agents Managing State Examples Travel Planning Company Research Literature Review Core Quick Start Core Concepts Agent and'}, {'title': 'Vertex AI Agent Builder | Google Cloud', 'link': 'https://cloud.google.com/products/agent-builder', 'snippet': 'Build and deploy enterprise ready generative AI experiences · Product highlights · Easily build no code conversational AI agents · Ground in Google search and/or\\xa0...', 'body': 'Vertex AI Agent Builder | Google Cloud Page Contents Vertex AI Agent Builder is making generative AI more reliable for the enterprise. Read the blog. Vertex AI Agent Builder Build and deploy enterprise ready generative AI experiences Create AI agents and applications using natural language or a code-first approach. Easily ground your agents or apps in enterprise data with a range of options. Vertex AI Agent Builder gathers all the surfaces and tools that developers need to build their AI agents'}, {'title': 'AI tools I have found useful w/ research. What do you guys think ...', 'link': 'https://www.reddit.com/r/PhD/comments/14d6g09/ai_tools_i_have_found_useful_w_research_what_do/', 'snippet': \"Jun 19, 2023 ... Need help deciding on the best ones, and to identify ones I've missed: ASSISTANTS (chatbots, multi-purpose) Chat with Open Large Language Models.\", 'body': 'Reddit - Dive into anything Skip to main content Open menu Open navigation Go to Reddit Home r/PhD A chip A close button Get app Get the Reddit app Log In Log in to Reddit Expand user menu Open settings menu Log In / Sign Up Advertise on Reddit Shop Collectible Avatars Get the Reddit app Scan this QR code to download the app now Or check it out in the app stores Go to PhD r/PhD r/PhD A subreddit dedicated to PhDs. Members Online • [deleted] ADMIN MOD AI tools I have found useful w/ research.'}]\n", "---------- Arxiv_Search_Agent ----------\n", "[FunctionCall(id='call_ZdmwQGTO03X23GeRn6fwDN8q', arguments='{\"query\":\"no code tools for building multi agent AI systems\",\"max_results\":5}', name='arxiv_search')]\n", "[Prompt tokens: 719, Completion tokens: 28]\n", "---------- Arxiv_Search_Agent ----------\n", "[FunctionExecutionResult(content='[{\\'title\\': \\'AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems\\', \\'authors\\': [\\'<PERSON>\\', \\'<PERSON><PERSON>\\', \\'<PERSON><PERSON>\\', \\'<PERSON><PERSON> <PERSON>\\', \\'<PERSON>\\', \\'<PERSON><PERSON><PERSON>\\', \\'<PERSON>\\', \\'Saleema Amershi\\'], \\'published\\': \\'2024-08-09\\', \\'abstract\\': \\'Multi-agent systems, where multiple agents (generative AI models + tools)\\\\ncollaborate, are emerging as an effective pattern for solving long-running,\\\\ncomplex tasks in numerous domains. However, specifying their parameters (such\\\\nas models, tools, and orchestration mechanisms etc,.) and debugging them\\\\nremains challenging for most developers. To address this challenge, we present\\\\nAUTOGEN STUDIO, a no-code developer tool for rapidly prototyping, debugging,\\\\nand evaluating multi-agent workflows built upon the AUTOGEN framework. AUTOGEN\\\\nSTUDIO offers a web interface and a Python API for representing LLM-enabled\\\\nagents using a declarative (JSON-based) specification. It provides an intuitive\\\\ndrag-and-drop UI for agent workflow specification, interactive evaluation and\\\\ndebugging of workflows, and a gallery of reusable agent components. We\\\\nhighlight four design principles for no-code multi-agent developer tools and\\\\ncontribute an open-source implementation at\\\\nhttps://github.com/microsoft/autogen/tree/main/samples/apps/autogen-studio\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2408.15247v1\\'}, {\\'title\\': \\'Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration\\', \\'authors\\': [\\'Cory Hymel\\', \\'Sida Peng\\', \\'Kevin Xu\\', \\'Charath Ranganathan\\'], \\'published\\': \\'2024-10-29\\', \\'abstract\\': \\'In recent years, with the rapid advancement of large language models (LLMs),\\\\nmulti-agent systems have become increasingly more capable of practical\\\\napplication. At the same time, the software development industry has had a\\\\nnumber of new AI-powered tools developed that improve the software development\\\\nlifecycle (SDLC). Academically, much attention has been paid to the role of\\\\nmulti-agent systems to the SDLC. And, while single-agent systems have\\\\nfrequently been examined in real-world applications, we have seen comparatively\\\\nfew real-world examples of publicly available commercial tools working together\\\\nin a multi-agent system with measurable improvements. In this experiment we\\\\ntest context sharing between Crowdbotics PRD AI, a tool for generating software\\\\nrequirements using AI, and GitHub Copilot, an AI pair-programming tool. By\\\\nsharing business requirements from PRD AI, we improve the code suggestion\\\\ncapabilities of GitHub Copilot by 13.8% and developer task success rate by\\\\n24.5% -- demonstrating a real-world example of commercially-available AI\\\\nsystems working together with improved outcomes.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.22129v1\\'}, {\\'title\\': \\'AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML\\', \\'authors\\': [\\'Patara Trirat\\', \\'Wonyong Jeong\\', \\'Sung Ju Hwang\\'], \\'published\\': \\'2024-10-03\\', \\'abstract\\': \"Automated machine learning (AutoML) accelerates AI development by automating\\\\ntasks in the development pipeline, such as optimal model search and\\\\nhyperparameter tuning. Existing AutoML systems often require technical\\\\nexpertise to set up complex tools, which is in general time-consuming and\\\\nrequires a large amount of human effort. Therefore, recent works have started\\\\nexploiting large language models (LLM) to lessen such burden and increase the\\\\nusability of AutoML frameworks via a natural language interface, allowing\\\\nnon-expert users to build their data-driven solutions. These methods, however,\\\\nare usually designed only for a particular process in the AI development\\\\npipeline and do not efficiently use the inherent capacity of the LLMs. This\\\\npaper proposes AutoML-Agent, a novel multi-agent framework tailored for\\\\nfull-pipeline AutoML, i.e., from data retrieval to model deployment.\\\\nAutoML-Agent takes user\\'s task descriptions, facilitates collaboration between\\\\nspecialized LLM agents, and delivers deployment-ready models. Unlike existing\\\\nwork, instead of devising a single plan, we introduce a retrieval-augmented\\\\nplanning strategy to enhance exploration to search for more optimal plans. We\\\\nalso decompose each plan into sub-tasks (e.g., data preprocessing and neural\\\\nnetwork design) each of which is solved by a specialized agent we build via\\\\nprompting executing in parallel, making the search process more efficient.\\\\nMoreover, we propose a multi-stage verification to verify executed results and\\\\nguide the code generation LLM in implementing successful solutions. Extensive\\\\nexperiments on seven downstream tasks using fourteen datasets show that\\\\nAutoML-Agent achieves a higher success rate in automating the full AutoML\\\\nprocess, yielding systems with good performance throughout the diverse domains.\", \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.02958v1\\'}, {\\'title\\': \\'Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges\\', \\'authors\\': [\\'Sivan Schwartz\\', \\'Avi Yaeli\\', \\'Segev Shlomov\\'], \\'published\\': \\'2023-08-10\\', \\'abstract\\': \\'Trust in AI agents has been extensively studied in the literature, resulting\\\\nin significant advancements in our understanding of this field. However, the\\\\nrapid advancements in Large Language Models (LLMs) and the emergence of\\\\nLLM-based AI agent frameworks pose new challenges and opportunities for further\\\\nresearch. In the field of process automation, a new generation of AI-based\\\\nagents has emerged, enabling the execution of complex tasks. At the same time,\\\\nthe process of building automation has become more accessible to business users\\\\nvia user-friendly no-code tools and training mechanisms. This paper explores\\\\nthese new challenges and opportunities, analyzes the main aspects of trust in\\\\nAI agents discussed in existing literature, and identifies specific\\\\nconsiderations and challenges relevant to this new generation of automation\\\\nagents. We also evaluate how nascent products in this category address these\\\\nconsiderations. Finally, we highlight several challenges that the research\\\\ncommunity should address in this evolving landscape.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2308.05391v1\\'}, {\\'title\\': \\'AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications\\', \\'authors\\': [\\'Xin Pang\\', \\'Zhucong Li\\', \\'Jiaxiang Chen\\', \\'Yuan Cheng\\', \\'Yinghui Xu\\', \\'Yuan Qi\\'], \\'published\\': \\'2024-04-07\\', \\'abstract\\': \\'We introduce AI2Apps, a Visual Integrated Development Environment (Visual\\\\nIDE) with full-cycle capabilities that accelerates developers to build\\\\ndeployable LLM-based AI agent Applications. This Visual IDE prioritizes both\\\\nthe Integrity of its development tools and the Visuality of its components,\\\\nensuring a smooth and efficient building experience.On one hand, AI2Apps\\\\nintegrates a comprehensive development toolkit ranging from a prototyping\\\\ncanvas and AI-assisted code editor to agent debugger, management system, and\\\\ndeployment tools all within a web-based graphical user interface. On the other\\\\nhand, AI2Apps visualizes reusable front-end and back-end code as intuitive\\\\ndrag-and-drop components. Furthermore, a plugin system named AI2Apps Extension\\\\n(AAE) is designed for Extensibility, showcasing how a new plugin with 20\\\\ncomponents enables web agent to mimic human-like browsing behavior. Our case\\\\nstudy demonstrates substantial efficiency improvements, with AI2Apps reducing\\\\ntoken consumption and API calls when debugging a specific sophisticated\\\\nmultimodal agent by approximately 90% and 80%, respectively. The AI2Apps,\\\\nincluding an online demo, open-source code, and a screencast video, is now\\\\npublicly accessible.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2404.04902v1\\'}]', call_id='call_ZdmwQGTO03X23GeRn6fwDN8q')]\n", "---------- Arxiv_Search_Agent ----------\n", "Tool calls:\n", "arxiv_search({\"query\":\"no code tools for building multi agent AI systems\",\"max_results\":5}) = [{'title': 'AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems', 'authors': ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Saleema Amershi'], 'published': '2024-08-09', 'abstract': 'Multi-agent systems, where multiple agents (generative AI models + tools)\\ncollaborate, are emerging as an effective pattern for solving long-running,\\ncomplex tasks in numerous domains. However, specifying their parameters (such\\nas models, tools, and orchestration mechanisms etc,.) and debugging them\\nremains challenging for most developers. To address this challenge, we present\\nAUTOGEN STUDIO, a no-code developer tool for rapidly prototyping, debugging,\\nand evaluating multi-agent workflows built upon the AUTOGEN framework. AUTOGEN\\nSTUDIO offers a web interface and a Python API for representing LLM-enabled\\nagents using a declarative (JSON-based) specification. It provides an intuitive\\ndrag-and-drop UI for agent workflow specification, interactive evaluation and\\ndebugging of workflows, and a gallery of reusable agent components. We\\nhighlight four design principles for no-code multi-agent developer tools and\\ncontribute an open-source implementation at\\nhttps://github.com/microsoft/autogen/tree/main/samples/apps/autogen-studio', 'pdf_url': 'http://arxiv.org/pdf/2408.15247v1'}, {'title': 'Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration', 'authors': ['Cory Hymel', 'Sida Peng', 'Kevin Xu', 'Charath Ranganathan'], 'published': '2024-10-29', 'abstract': 'In recent years, with the rapid advancement of large language models (LLMs),\\nmulti-agent systems have become increasingly more capable of practical\\napplication. At the same time, the software development industry has had a\\nnumber of new AI-powered tools developed that improve the software development\\nlifecycle (SDLC). Academically, much attention has been paid to the role of\\nmulti-agent systems to the SDLC. And, while single-agent systems have\\nfrequently been examined in real-world applications, we have seen comparatively\\nfew real-world examples of publicly available commercial tools working together\\nin a multi-agent system with measurable improvements. In this experiment we\\ntest context sharing between Crowdbotics PRD AI, a tool for generating software\\nrequirements using AI, and GitHub Copilot, an AI pair-programming tool. By\\nsharing business requirements from PRD AI, we improve the code suggestion\\ncapabilities of GitHub Copilot by 13.8% and developer task success rate by\\n24.5% -- demonstrating a real-world example of commercially-available AI\\nsystems working together with improved outcomes.', 'pdf_url': 'http://arxiv.org/pdf/2410.22129v1'}, {'title': 'AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML', 'authors': ['Patara Trirat', 'Wonyong Jeong', 'Sung Ju Hwang'], 'published': '2024-10-03', 'abstract': \"Automated machine learning (AutoML) accelerates AI development by automating\\ntasks in the development pipeline, such as optimal model search and\\nhyperparameter tuning. Existing AutoML systems often require technical\\nexpertise to set up complex tools, which is in general time-consuming and\\nrequires a large amount of human effort. Therefore, recent works have started\\nexploiting large language models (LLM) to lessen such burden and increase the\\nusability of AutoML frameworks via a natural language interface, allowing\\nnon-expert users to build their data-driven solutions. These methods, however,\\nare usually designed only for a particular process in the AI development\\npipeline and do not efficiently use the inherent capacity of the LLMs. This\\npaper proposes AutoML-Agent, a novel multi-agent framework tailored for\\nfull-pipeline AutoML, i.e., from data retrieval to model deployment.\\nAutoML-Agent takes user's task descriptions, facilitates collaboration between\\nspecialized LLM agents, and delivers deployment-ready models. Unlike existing\\nwork, instead of devising a single plan, we introduce a retrieval-augmented\\nplanning strategy to enhance exploration to search for more optimal plans. We\\nalso decompose each plan into sub-tasks (e.g., data preprocessing and neural\\nnetwork design) each of which is solved by a specialized agent we build via\\nprompting executing in parallel, making the search process more efficient.\\nMoreover, we propose a multi-stage verification to verify executed results and\\nguide the code generation LLM in implementing successful solutions. Extensive\\nexperiments on seven downstream tasks using fourteen datasets show that\\nAutoML-Agent achieves a higher success rate in automating the full AutoML\\nprocess, yielding systems with good performance throughout the diverse domains.\", 'pdf_url': 'http://arxiv.org/pdf/2410.02958v1'}, {'title': 'Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges', 'authors': ['Sivan Schwartz', 'Avi Yaeli', 'Segev Shlomov'], 'published': '2023-08-10', 'abstract': 'Trust in AI agents has been extensively studied in the literature, resulting\\nin significant advancements in our understanding of this field. However, the\\nrapid advancements in Large Language Models (LLMs) and the emergence of\\nLLM-based AI agent frameworks pose new challenges and opportunities for further\\nresearch. In the field of process automation, a new generation of AI-based\\nagents has emerged, enabling the execution of complex tasks. At the same time,\\nthe process of building automation has become more accessible to business users\\nvia user-friendly no-code tools and training mechanisms. This paper explores\\nthese new challenges and opportunities, analyzes the main aspects of trust in\\nAI agents discussed in existing literature, and identifies specific\\nconsiderations and challenges relevant to this new generation of automation\\nagents. We also evaluate how nascent products in this category address these\\nconsiderations. Finally, we highlight several challenges that the research\\ncommunity should address in this evolving landscape.', 'pdf_url': 'http://arxiv.org/pdf/2308.05391v1'}, {'title': 'AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications', 'authors': ['Xin Pang', 'Zhucong Li', 'Jiaxiang Chen', 'Yuan Cheng', 'Yinghui Xu', 'Yuan Qi'], 'published': '2024-04-07', 'abstract': 'We introduce AI2Apps, a Visual Integrated Development Environment (Visual\\nIDE) with full-cycle capabilities that accelerates developers to build\\ndeployable LLM-based AI agent Applications. This Visual IDE prioritizes both\\nthe Integrity of its development tools and the Visuality of its components,\\nensuring a smooth and efficient building experience.On one hand, AI2Apps\\nintegrates a comprehensive development toolkit ranging from a prototyping\\ncanvas and AI-assisted code editor to agent debugger, management system, and\\ndeployment tools all within a web-based graphical user interface. On the other\\nhand, AI2Apps visualizes reusable front-end and back-end code as intuitive\\ndrag-and-drop components. Furthermore, a plugin system named AI2Apps Extension\\n(AAE) is designed for Extensibility, showcasing how a new plugin with 20\\ncomponents enables web agent to mimic human-like browsing behavior. Our case\\nstudy demonstrates substantial efficiency improvements, with AI2Apps reducing\\ntoken consumption and API calls when debugging a specific sophisticated\\nmultimodal agent by approximately 90% and 80%, respectively. The AI2Apps,\\nincluding an online demo, open-source code, and a screencast video, is now\\npublicly accessible.', 'pdf_url': 'http://arxiv.org/pdf/2404.04902v1'}]\n", "---------- Report_Agent ----------\n", "## Literature Review on No-Code Tools for Building Multi-Agent AI Systems\n", "\n", "### Introduction\n", "\n", "The emergence of multi-agent systems (MAS) has transformed various domains by enabling collaboration among multiple agents—ranging from generative AI models to orchestrated tools—to solve complex, long-term tasks. However, the traditional development of these systems often requires substantial technical expertise, making it inaccessible for non-developers. The introduction of no-code platforms aims to shift this paradigm, allowing users without formal programming knowledge to design, debug, and deploy multi-agent systems. This review synthesizes current literature concerning no-code tools developed for building multi-agent AI systems, highlighting recent advancements and emerging trends.\n", "\n", "### No-Code Development Tools\n", "\n", "#### AutoGen Studio\n", "\n", "One of the prominent no-code tools is **AutoGen Studio**, developed by <PERSON><PERSON> et al. (2024). This tool provides a web interface and a declarative specification method utilizing JSON, enabling rapid prototyping, debugging, and evaluating multi-agent workflows. The drag-and-drop capabilities streamline the design process, making complex interactions between agents more manageable. The framework operates on four primary design principles that cater specifically to no-code development, contributing to an accessible pathway for users to harness multi-agent frameworks for various applications (<PERSON><PERSON> et al., 2024).\n", "\n", "#### AI2Apps Visual IDE\n", "\n", "Another notable tool is **AI2Apps**, described by <PERSON><PERSON> et al. (2024). It serves as a Visual Integrated Development Environment that incorporates a comprehensive set of tools from prototyping to deployment. The platform's user-friendly interface allows for the visualization of code through drag-and-drop components, facilitating smoother integration of different agents. An extension system enhances the platform's capabilities, showcasing the potential for customization and scalability in agent application development. The reported efficiency improvements in token consumption and API calls indicate substantial benefits in user-centric design (<PERSON><PERSON> et al., 2024).\n", "\n", "### Performance Enhancements in Multi-Agent Configurations\n", "\n", "<PERSON><PERSON><PERSON> et al. (2024) examined the collaborative performance of commercially available AI tools, demonstrating a measurable improvement when integrating multiple agents in a shared configuration. Their experiments showcased how cooperation between tools like Crowdbotics PRD AI and GitHub Copilot significantly improved task success rates, illustrating the practical benefits of employing no-code tools in multi-agent environments. This synergy reflects the critical need for frameworks that inherently support such integrations, especially through no-code mechanisms, to enhance user experience and productivity (<PERSON><PERSON><PERSON> et al., 2024).\n", "\n", "### Trust and Usability in AI Agents\n", "\n", "The concept of trust in AI, particularly in LLM-based automation agents, has gained attention. <PERSON> et al. (2023) addressed the challenges and considerations unique to this new generation of agents, highlighting how no-code platforms ease access and usability for non-technical users. The paper emphasizes the need for further research into the trust factors integral to effective multi-agent systems, advocating for a user-centric approach in the design and evaluation of these no-code tools (<PERSON> et al., 2023).\n", "\n", "### Full-Pipeline AutoML with Multi-Agent Systems\n", "\n", "The **AutoML-Agent** framework proposed by <PERSON><PERSON> et al. (2024) brings another layer of innovation to the no-code landscape. This framework enhances existing automated machine learning processes by using multiple specialized agents that collaboratively manage the full AI development pipeline from data retrieval to model deployment. The novelty lies in its retrieval-augmented planning strategy, which allows for efficient task decomposition and parallel execution, optimizing the overall development experience for non-experts (<PERSON><PERSON> et al., 2024).\n", "\n", "### Conclusion\n", "\n", "The literature presents a growing array of no-code tools designed to democratize the development of multi-agent systems. Innovations such as AutoGen Studio, AI2Apps, and collaborative frameworks like AutoML-Agent highlight a trend towards user-centric, efficient design that encourages participation beyond technical boundaries. Future research should continue to explore aspects of trust, usability, and integration to further refine these tools and expand their applicability across various domains.\n", "\n", "### References\n", "\n", "- <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, S. (2024). AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems. *arXiv:2408.15247*.\n", "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, C. (2024). Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration. *arXiv:2410.22129*.\n", "- <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2024). AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications. *arXiv:2404.04902*.\n", "- <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, S<PERSON> (2023). Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges. *arXiv:2308.05391*.\n", "- <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, S. J. (2024). AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML. *arXiv:2410.02958*.\n", "\n", "TERMINATE\n", "[Prompt tokens: 2381, Completion tokens: 1090]\n", "---------- Summary ----------\n", "Number of messages: 8\n", "Finish reason: Text 'TERMINATE' mentioned\n", "Total prompt tokens: 3223\n", "Total completion tokens: 1147\n", "Duration: 17.06 seconds\n"]}, {"data": {"text/plain": ["TaskResult(messages=[TextMessage(source='user', models_usage=None, content='Write a literature review on no code tools for building multi agent ai systems', type='TextMessage'), ToolCallRequestEvent(source='Google_Search_Agent', models_usage=RequestUsage(prompt_tokens=123, completion_tokens=29), content=[FunctionCall(id='call_bNGwWFsfeTwDhtIpsI6GYISR', arguments='{\"query\":\"no code tools for building multi agent AI systems literature review\",\"num_results\":3}', name='google_search')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='Google_Search_Agent', models_usage=None, content=[FunctionExecutionResult(content='[{\\'title\\': \\'Literature Review — AutoGen\\', \\'link\\': \\'https://microsoft.github.io/autogen/dev//user-guide/agentchat-user-guide/examples/literature-review.html\\', \\'snippet\\': \\'run( task=\"Write a literature review on no code tools for building multi agent ai systems\", ) ... ### Conclusion No-code tools for building multi-agent AI systems\\\\xa0...\\', \\'body\\': \\'Literature Review — AutoGen Skip to main content Back to top Ctrl + K AutoGen 0.4 is a work in progress. Go here to find the 0.2 documentation. User Guide Packages API Reference Twitter GitHub PyPI User Guide Packages API Reference Twitter GitHub PyPI AgentChat Installation Quickstart Tutorial Models Messages Agents Teams Selector Group Chat Swarm Termination Custom Agents Managing State Examples Travel Planning Company Research Literature Review Core Quick Start Core Concepts Agent and\\'}, {\\'title\\': \\'Vertex AI Agent Builder | Google Cloud\\', \\'link\\': \\'https://cloud.google.com/products/agent-builder\\', \\'snippet\\': \\'Build and deploy enterprise ready generative AI experiences · Product highlights · Easily build no code conversational AI agents · Ground in Google search and/or\\\\xa0...\\', \\'body\\': \\'Vertex AI Agent Builder | Google Cloud Page Contents Vertex AI Agent Builder is making generative AI more reliable for the enterprise. Read the blog. Vertex AI Agent Builder Build and deploy enterprise ready generative AI experiences Create AI agents and applications using natural language or a code-first approach. Easily ground your agents or apps in enterprise data with a range of options. Vertex AI Agent Builder gathers all the surfaces and tools that developers need to build their AI agents\\'}, {\\'title\\': \\'AI tools I have found useful w/ research. What do you guys think ...\\', \\'link\\': \\'https://www.reddit.com/r/PhD/comments/14d6g09/ai_tools_i_have_found_useful_w_research_what_do/\\', \\'snippet\\': \"Jun 19, 2023 ... Need help deciding on the best ones, and to identify ones I\\'ve missed: ASSISTANTS (chatbots, multi-purpose) Chat with Open Large Language Models.\", \\'body\\': \\'Reddit - Dive into anything Skip to main content Open menu Open navigation Go to Reddit Home r/PhD A chip A close button Get app Get the Reddit app Log In Log in to Reddit Expand user menu Open settings menu Log In / Sign Up Advertise on Reddit Shop Collectible Avatars Get the Reddit app Scan this QR code to download the app now Or check it out in the app stores Go to PhD r/PhD r/PhD A subreddit dedicated to PhDs. Members Online • [deleted] ADMIN MOD AI tools I have found useful w/ research.\\'}]', call_id='call_bNGwWFsfeTwDhtIpsI6GYISR')], type='ToolCallExecutionEvent'), TextMessage(source='Google_Search_Agent', models_usage=None, content='Tool calls:\\ngoogle_search({\"query\":\"no code tools for building multi agent AI systems literature review\",\"num_results\":3}) = [{\\'title\\': \\'Literature Review — AutoGen\\', \\'link\\': \\'https://microsoft.github.io/autogen/dev//user-guide/agentchat-user-guide/examples/literature-review.html\\', \\'snippet\\': \\'run( task=\"Write a literature review on no code tools for building multi agent ai systems\", ) ... ### Conclusion No-code tools for building multi-agent AI systems\\\\xa0...\\', \\'body\\': \\'Literature Review — AutoGen Skip to main content Back to top Ctrl + K AutoGen 0.4 is a work in progress. Go here to find the 0.2 documentation. User Guide Packages API Reference Twitter GitHub PyPI User Guide Packages API Reference Twitter GitHub PyPI AgentChat Installation Quickstart Tutorial Models Messages Agents Teams Selector Group Chat Swarm Termination Custom Agents Managing State Examples Travel Planning Company Research Literature Review Core Quick Start Core Concepts Agent and\\'}, {\\'title\\': \\'Vertex AI Agent Builder | Google Cloud\\', \\'link\\': \\'https://cloud.google.com/products/agent-builder\\', \\'snippet\\': \\'Build and deploy enterprise ready generative AI experiences · Product highlights · Easily build no code conversational AI agents · Ground in Google search and/or\\\\xa0...\\', \\'body\\': \\'Vertex AI Agent Builder | Google Cloud Page Contents Vertex AI Agent Builder is making generative AI more reliable for the enterprise. Read the blog. Vertex AI Agent Builder Build and deploy enterprise ready generative AI experiences Create AI agents and applications using natural language or a code-first approach. Easily ground your agents or apps in enterprise data with a range of options. Vertex AI Agent Builder gathers all the surfaces and tools that developers need to build their AI agents\\'}, {\\'title\\': \\'AI tools I have found useful w/ research. What do you guys think ...\\', \\'link\\': \\'https://www.reddit.com/r/PhD/comments/14d6g09/ai_tools_i_have_found_useful_w_research_what_do/\\', \\'snippet\\': \"Jun 19, 2023 ... Need help deciding on the best ones, and to identify ones I\\'ve missed: ASSISTANTS (chatbots, multi-purpose) Chat with Open Large Language Models.\", \\'body\\': \\'Reddit - Dive into anything Skip to main content Open menu Open navigation Go to Reddit Home r/PhD A chip A close button Get app Get the Reddit app Log In Log in to Reddit Expand user menu Open settings menu Log In / Sign Up Advertise on Reddit Shop Collectible Avatars Get the Reddit app Scan this QR code to download the app now Or check it out in the app stores Go to PhD r/PhD r/PhD A subreddit dedicated to PhDs. Members Online • [deleted] ADMIN MOD AI tools I have found useful w/ research.\\'}]', type='TextMessage'), ToolCallRequestEvent(source='Arxiv_Search_Agent', models_usage=RequestUsage(prompt_tokens=719, completion_tokens=28), content=[FunctionCall(id='call_ZdmwQGTO03X23GeRn6fwDN8q', arguments='{\"query\":\"no code tools for building multi agent AI systems\",\"max_results\":5}', name='arxiv_search')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='Arxiv_Search_Agent', models_usage=None, content=[FunctionExecutionResult(content='[{\\'title\\': \\'AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems\\', \\'authors\\': [\\'Victor Dibia\\', \\'Jingya Chen\\', \\'Gagan Bansal\\', \\'Suff Syed\\', \\'Adam Fourney\\', \\'Erkang Zhu\\', \\'Chi Wang\\', \\'Saleema Amershi\\'], \\'published\\': \\'2024-08-09\\', \\'abstract\\': \\'Multi-agent systems, where multiple agents (generative AI models + tools)\\\\ncollaborate, are emerging as an effective pattern for solving long-running,\\\\ncomplex tasks in numerous domains. However, specifying their parameters (such\\\\nas models, tools, and orchestration mechanisms etc,.) and debugging them\\\\nremains challenging for most developers. To address this challenge, we present\\\\nAUTOGEN STUDIO, a no-code developer tool for rapidly prototyping, debugging,\\\\nand evaluating multi-agent workflows built upon the AUTOGEN framework. AUTOGEN\\\\nSTUDIO offers a web interface and a Python API for representing LLM-enabled\\\\nagents using a declarative (JSON-based) specification. It provides an intuitive\\\\ndrag-and-drop UI for agent workflow specification, interactive evaluation and\\\\ndebugging of workflows, and a gallery of reusable agent components. We\\\\nhighlight four design principles for no-code multi-agent developer tools and\\\\ncontribute an open-source implementation at\\\\nhttps://github.com/microsoft/autogen/tree/main/samples/apps/autogen-studio\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2408.15247v1\\'}, {\\'title\\': \\'Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration\\', \\'authors\\': [\\'Cory Hymel\\', \\'Sida Peng\\', \\'Kevin Xu\\', \\'Charath Ranganathan\\'], \\'published\\': \\'2024-10-29\\', \\'abstract\\': \\'In recent years, with the rapid advancement of large language models (LLMs),\\\\nmulti-agent systems have become increasingly more capable of practical\\\\napplication. At the same time, the software development industry has had a\\\\nnumber of new AI-powered tools developed that improve the software development\\\\nlifecycle (SDLC). Academically, much attention has been paid to the role of\\\\nmulti-agent systems to the SDLC. And, while single-agent systems have\\\\nfrequently been examined in real-world applications, we have seen comparatively\\\\nfew real-world examples of publicly available commercial tools working together\\\\nin a multi-agent system with measurable improvements. In this experiment we\\\\ntest context sharing between Crowdbotics PRD AI, a tool for generating software\\\\nrequirements using AI, and GitHub Copilot, an AI pair-programming tool. By\\\\nsharing business requirements from PRD AI, we improve the code suggestion\\\\ncapabilities of GitHub Copilot by 13.8% and developer task success rate by\\\\n24.5% -- demonstrating a real-world example of commercially-available AI\\\\nsystems working together with improved outcomes.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.22129v1\\'}, {\\'title\\': \\'AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML\\', \\'authors\\': [\\'Patara Trirat\\', \\'Wonyong Jeong\\', \\'Sung Ju Hwang\\'], \\'published\\': \\'2024-10-03\\', \\'abstract\\': \"Automated machine learning (AutoML) accelerates AI development by automating\\\\ntasks in the development pipeline, such as optimal model search and\\\\nhyperparameter tuning. Existing AutoML systems often require technical\\\\nexpertise to set up complex tools, which is in general time-consuming and\\\\nrequires a large amount of human effort. Therefore, recent works have started\\\\nexploiting large language models (LLM) to lessen such burden and increase the\\\\nusability of AutoML frameworks via a natural language interface, allowing\\\\nnon-expert users to build their data-driven solutions. These methods, however,\\\\nare usually designed only for a particular process in the AI development\\\\npipeline and do not efficiently use the inherent capacity of the LLMs. This\\\\npaper proposes AutoML-Agent, a novel multi-agent framework tailored for\\\\nfull-pipeline AutoML, i.e., from data retrieval to model deployment.\\\\nAutoML-Agent takes user\\'s task descriptions, facilitates collaboration between\\\\nspecialized LLM agents, and delivers deployment-ready models. Unlike existing\\\\nwork, instead of devising a single plan, we introduce a retrieval-augmented\\\\nplanning strategy to enhance exploration to search for more optimal plans. We\\\\nalso decompose each plan into sub-tasks (e.g., data preprocessing and neural\\\\nnetwork design) each of which is solved by a specialized agent we build via\\\\nprompting executing in parallel, making the search process more efficient.\\\\nMoreover, we propose a multi-stage verification to verify executed results and\\\\nguide the code generation LLM in implementing successful solutions. Extensive\\\\nexperiments on seven downstream tasks using fourteen datasets show that\\\\nAutoML-Agent achieves a higher success rate in automating the full AutoML\\\\nprocess, yielding systems with good performance throughout the diverse domains.\", \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.02958v1\\'}, {\\'title\\': \\'Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges\\', \\'authors\\': [\\'Sivan Schwartz\\', \\'Avi Yaeli\\', \\'Segev Shlomov\\'], \\'published\\': \\'2023-08-10\\', \\'abstract\\': \\'Trust in AI agents has been extensively studied in the literature, resulting\\\\nin significant advancements in our understanding of this field. However, the\\\\nrapid advancements in Large Language Models (LLMs) and the emergence of\\\\nLLM-based AI agent frameworks pose new challenges and opportunities for further\\\\nresearch. In the field of process automation, a new generation of AI-based\\\\nagents has emerged, enabling the execution of complex tasks. At the same time,\\\\nthe process of building automation has become more accessible to business users\\\\nvia user-friendly no-code tools and training mechanisms. This paper explores\\\\nthese new challenges and opportunities, analyzes the main aspects of trust in\\\\nAI agents discussed in existing literature, and identifies specific\\\\nconsiderations and challenges relevant to this new generation of automation\\\\nagents. We also evaluate how nascent products in this category address these\\\\nconsiderations. Finally, we highlight several challenges that the research\\\\ncommunity should address in this evolving landscape.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2308.05391v1\\'}, {\\'title\\': \\'AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications\\', \\'authors\\': [\\'Xin Pang\\', \\'Zhucong Li\\', \\'Jiaxiang Chen\\', \\'Yuan Cheng\\', \\'Yinghui Xu\\', \\'Yuan Qi\\'], \\'published\\': \\'2024-04-07\\', \\'abstract\\': \\'We introduce AI2Apps, a Visual Integrated Development Environment (Visual\\\\nIDE) with full-cycle capabilities that accelerates developers to build\\\\ndeployable LLM-based AI agent Applications. This Visual IDE prioritizes both\\\\nthe Integrity of its development tools and the Visuality of its components,\\\\nensuring a smooth and efficient building experience.On one hand, AI2Apps\\\\nintegrates a comprehensive development toolkit ranging from a prototyping\\\\ncanvas and AI-assisted code editor to agent debugger, management system, and\\\\ndeployment tools all within a web-based graphical user interface. On the other\\\\nhand, AI2Apps visualizes reusable front-end and back-end code as intuitive\\\\ndrag-and-drop components. Furthermore, a plugin system named AI2Apps Extension\\\\n(AAE) is designed for Extensibility, showcasing how a new plugin with 20\\\\ncomponents enables web agent to mimic human-like browsing behavior. Our case\\\\nstudy demonstrates substantial efficiency improvements, with AI2Apps reducing\\\\ntoken consumption and API calls when debugging a specific sophisticated\\\\nmultimodal agent by approximately 90% and 80%, respectively. The AI2Apps,\\\\nincluding an online demo, open-source code, and a screencast video, is now\\\\npublicly accessible.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2404.04902v1\\'}]', call_id='call_ZdmwQGTO03X23GeRn6fwDN8q')], type='ToolCallExecutionEvent'), TextMessage(source='Arxiv_Search_Agent', models_usage=None, content='Tool calls:\\narxiv_search({\"query\":\"no code tools for building multi agent AI systems\",\"max_results\":5}) = [{\\'title\\': \\'AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems\\', \\'authors\\': [\\'Victor Dibia\\', \\'Jingya Chen\\', \\'Gagan Bansal\\', \\'Suff Syed\\', \\'Adam Fourney\\', \\'Erkang Zhu\\', \\'Chi Wang\\', \\'Saleema Amershi\\'], \\'published\\': \\'2024-08-09\\', \\'abstract\\': \\'Multi-agent systems, where multiple agents (generative AI models + tools)\\\\ncollaborate, are emerging as an effective pattern for solving long-running,\\\\ncomplex tasks in numerous domains. However, specifying their parameters (such\\\\nas models, tools, and orchestration mechanisms etc,.) and debugging them\\\\nremains challenging for most developers. To address this challenge, we present\\\\nAUTOGEN STUDIO, a no-code developer tool for rapidly prototyping, debugging,\\\\nand evaluating multi-agent workflows built upon the AUTOGEN framework. AUTOGEN\\\\nSTUDIO offers a web interface and a Python API for representing LLM-enabled\\\\nagents using a declarative (JSON-based) specification. It provides an intuitive\\\\ndrag-and-drop UI for agent workflow specification, interactive evaluation and\\\\ndebugging of workflows, and a gallery of reusable agent components. We\\\\nhighlight four design principles for no-code multi-agent developer tools and\\\\ncontribute an open-source implementation at\\\\nhttps://github.com/microsoft/autogen/tree/main/samples/apps/autogen-studio\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2408.15247v1\\'}, {\\'title\\': \\'Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration\\', \\'authors\\': [\\'Cory Hymel\\', \\'Sida Peng\\', \\'Kevin Xu\\', \\'Charath Ranganathan\\'], \\'published\\': \\'2024-10-29\\', \\'abstract\\': \\'In recent years, with the rapid advancement of large language models (LLMs),\\\\nmulti-agent systems have become increasingly more capable of practical\\\\napplication. At the same time, the software development industry has had a\\\\nnumber of new AI-powered tools developed that improve the software development\\\\nlifecycle (SDLC). Academically, much attention has been paid to the role of\\\\nmulti-agent systems to the SDLC. And, while single-agent systems have\\\\nfrequently been examined in real-world applications, we have seen comparatively\\\\nfew real-world examples of publicly available commercial tools working together\\\\nin a multi-agent system with measurable improvements. In this experiment we\\\\ntest context sharing between Crowdbotics PRD AI, a tool for generating software\\\\nrequirements using AI, and GitHub Copilot, an AI pair-programming tool. By\\\\nsharing business requirements from PRD AI, we improve the code suggestion\\\\ncapabilities of GitHub Copilot by 13.8% and developer task success rate by\\\\n24.5% -- demonstrating a real-world example of commercially-available AI\\\\nsystems working together with improved outcomes.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.22129v1\\'}, {\\'title\\': \\'AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML\\', \\'authors\\': [\\'Patara Trirat\\', \\'Wonyong Jeong\\', \\'Sung Ju Hwang\\'], \\'published\\': \\'2024-10-03\\', \\'abstract\\': \"Automated machine learning (AutoML) accelerates AI development by automating\\\\ntasks in the development pipeline, such as optimal model search and\\\\nhyperparameter tuning. Existing AutoML systems often require technical\\\\nexpertise to set up complex tools, which is in general time-consuming and\\\\nrequires a large amount of human effort. Therefore, recent works have started\\\\nexploiting large language models (LLM) to lessen such burden and increase the\\\\nusability of AutoML frameworks via a natural language interface, allowing\\\\nnon-expert users to build their data-driven solutions. These methods, however,\\\\nare usually designed only for a particular process in the AI development\\\\npipeline and do not efficiently use the inherent capacity of the LLMs. This\\\\npaper proposes AutoML-Agent, a novel multi-agent framework tailored for\\\\nfull-pipeline AutoML, i.e., from data retrieval to model deployment.\\\\nAutoML-Agent takes user\\'s task descriptions, facilitates collaboration between\\\\nspecialized LLM agents, and delivers deployment-ready models. Unlike existing\\\\nwork, instead of devising a single plan, we introduce a retrieval-augmented\\\\nplanning strategy to enhance exploration to search for more optimal plans. We\\\\nalso decompose each plan into sub-tasks (e.g., data preprocessing and neural\\\\nnetwork design) each of which is solved by a specialized agent we build via\\\\nprompting executing in parallel, making the search process more efficient.\\\\nMoreover, we propose a multi-stage verification to verify executed results and\\\\nguide the code generation LLM in implementing successful solutions. Extensive\\\\nexperiments on seven downstream tasks using fourteen datasets show that\\\\nAutoML-Agent achieves a higher success rate in automating the full AutoML\\\\nprocess, yielding systems with good performance throughout the diverse domains.\", \\'pdf_url\\': \\'http://arxiv.org/pdf/2410.02958v1\\'}, {\\'title\\': \\'Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges\\', \\'authors\\': [\\'Sivan Schwartz\\', \\'Avi Yaeli\\', \\'Segev Shlomov\\'], \\'published\\': \\'2023-08-10\\', \\'abstract\\': \\'Trust in AI agents has been extensively studied in the literature, resulting\\\\nin significant advancements in our understanding of this field. However, the\\\\nrapid advancements in Large Language Models (LLMs) and the emergence of\\\\nLLM-based AI agent frameworks pose new challenges and opportunities for further\\\\nresearch. In the field of process automation, a new generation of AI-based\\\\nagents has emerged, enabling the execution of complex tasks. At the same time,\\\\nthe process of building automation has become more accessible to business users\\\\nvia user-friendly no-code tools and training mechanisms. This paper explores\\\\nthese new challenges and opportunities, analyzes the main aspects of trust in\\\\nAI agents discussed in existing literature, and identifies specific\\\\nconsiderations and challenges relevant to this new generation of automation\\\\nagents. We also evaluate how nascent products in this category address these\\\\nconsiderations. Finally, we highlight several challenges that the research\\\\ncommunity should address in this evolving landscape.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2308.05391v1\\'}, {\\'title\\': \\'AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications\\', \\'authors\\': [\\'Xin Pang\\', \\'Zhucong Li\\', \\'Jiaxiang Chen\\', \\'Yuan Cheng\\', \\'Yinghui Xu\\', \\'Yuan Qi\\'], \\'published\\': \\'2024-04-07\\', \\'abstract\\': \\'We introduce AI2Apps, a Visual Integrated Development Environment (Visual\\\\nIDE) with full-cycle capabilities that accelerates developers to build\\\\ndeployable LLM-based AI agent Applications. This Visual IDE prioritizes both\\\\nthe Integrity of its development tools and the Visuality of its components,\\\\nensuring a smooth and efficient building experience.On one hand, AI2Apps\\\\nintegrates a comprehensive development toolkit ranging from a prototyping\\\\ncanvas and AI-assisted code editor to agent debugger, management system, and\\\\ndeployment tools all within a web-based graphical user interface. On the other\\\\nhand, AI2Apps visualizes reusable front-end and back-end code as intuitive\\\\ndrag-and-drop components. Furthermore, a plugin system named AI2Apps Extension\\\\n(AAE) is designed for Extensibility, showcasing how a new plugin with 20\\\\ncomponents enables web agent to mimic human-like browsing behavior. Our case\\\\nstudy demonstrates substantial efficiency improvements, with AI2Apps reducing\\\\ntoken consumption and API calls when debugging a specific sophisticated\\\\nmultimodal agent by approximately 90% and 80%, respectively. The AI2Apps,\\\\nincluding an online demo, open-source code, and a screencast video, is now\\\\npublicly accessible.\\', \\'pdf_url\\': \\'http://arxiv.org/pdf/2404.04902v1\\'}]', type='TextMessage'), TextMessage(source='Report_Agent', models_usage=RequestUsage(prompt_tokens=2381, completion_tokens=1090), content=\"## Literature Review on No-Code Tools for Building Multi-Agent AI Systems\\n\\n### Introduction\\n\\nThe emergence of multi-agent systems (MAS) has transformed various domains by enabling collaboration among multiple agents—ranging from generative AI models to orchestrated tools—to solve complex, long-term tasks. However, the traditional development of these systems often requires substantial technical expertise, making it inaccessible for non-developers. The introduction of no-code platforms aims to shift this paradigm, allowing users without formal programming knowledge to design, debug, and deploy multi-agent systems. This review synthesizes current literature concerning no-code tools developed for building multi-agent AI systems, highlighting recent advancements and emerging trends.\\n\\n### No-Code Development Tools\\n\\n#### AutoGen Studio\\n\\nOne of the prominent no-code tools is **AutoGen Studio**, developed by Dibia et al. (2024). This tool provides a web interface and a declarative specification method utilizing JSON, enabling rapid prototyping, debugging, and evaluating multi-agent workflows. The drag-and-drop capabilities streamline the design process, making complex interactions between agents more manageable. The framework operates on four primary design principles that cater specifically to no-code development, contributing to an accessible pathway for users to harness multi-agent frameworks for various applications (Dibia et al., 2024).\\n\\n#### AI2Apps Visual IDE\\n\\nAnother notable tool is **AI2Apps**, described by Pang et al. (2024). It serves as a Visual Integrated Development Environment that incorporates a comprehensive set of tools from prototyping to deployment. The platform's user-friendly interface allows for the visualization of code through drag-and-drop components, facilitating smoother integration of different agents. An extension system enhances the platform's capabilities, showcasing the potential for customization and scalability in agent application development. The reported efficiency improvements in token consumption and API calls indicate substantial benefits in user-centric design (Pang et al., 2024).\\n\\n### Performance Enhancements in Multi-Agent Configurations\\n\\nHymel et al. (2024) examined the collaborative performance of commercially available AI tools, demonstrating a measurable improvement when integrating multiple agents in a shared configuration. Their experiments showcased how cooperation between tools like Crowdbotics PRD AI and GitHub Copilot significantly improved task success rates, illustrating the practical benefits of employing no-code tools in multi-agent environments. This synergy reflects the critical need for frameworks that inherently support such integrations, especially through no-code mechanisms, to enhance user experience and productivity (Hymel et al., 2024).\\n\\n### Trust and Usability in AI Agents\\n\\nThe concept of trust in AI, particularly in LLM-based automation agents, has gained attention. Schwartz et al. (2023) addressed the challenges and considerations unique to this new generation of agents, highlighting how no-code platforms ease access and usability for non-technical users. The paper emphasizes the need for further research into the trust factors integral to effective multi-agent systems, advocating for a user-centric approach in the design and evaluation of these no-code tools (Schwartz et al., 2023).\\n\\n### Full-Pipeline AutoML with Multi-Agent Systems\\n\\nThe **AutoML-Agent** framework proposed by Trirat et al. (2024) brings another layer of innovation to the no-code landscape. This framework enhances existing automated machine learning processes by using multiple specialized agents that collaboratively manage the full AI development pipeline from data retrieval to model deployment. The novelty lies in its retrieval-augmented planning strategy, which allows for efficient task decomposition and parallel execution, optimizing the overall development experience for non-experts (Trirat et al., 2024).\\n\\n### Conclusion\\n\\nThe literature presents a growing array of no-code tools designed to democratize the development of multi-agent systems. Innovations such as AutoGen Studio, AI2Apps, and collaborative frameworks like AutoML-Agent highlight a trend towards user-centric, efficient design that encourages participation beyond technical boundaries. Future research should continue to explore aspects of trust, usability, and integration to further refine these tools and expand their applicability across various domains.\\n\\n### References\\n\\n- Dibia, V., Chen, J., Bansal, G., Syed, S., Fourney, A., Zhu, E., Wang, C., & Amershi, S. (2024). AutoGen Studio: A No-Code Developer Tool for Building and Debugging Multi-Agent Systems. *arXiv:2408.15247*.\\n- Hymel, C., Peng, S., Xu, K., & Ranganathan, C. (2024). Improving Performance of Commercially Available AI Products in a Multi-Agent Configuration. *arXiv:2410.22129*.\\n- Pang, X., Li, Z., Chen, J., Cheng, Y., Xu, Y., & Qi, Y. (2024). AI2Apps: A Visual IDE for Building LLM-based AI Agent Applications. *arXiv:2404.04902*.\\n- Schwartz, S., Yaeli, A., & Shlomov, S. (2023). Enhancing Trust in LLM-Based AI Automation Agents: New Considerations and Future Challenges. *arXiv:2308.05391*.\\n- Trirat, P., Jeong, W., & Hwang, S. J. (2024). AutoML-Agent: A Multi-Agent LLM Framework for Full-Pipeline AutoML. *arXiv:2410.02958*.\\n\\nTERMINATE\", type='TextMessage')], stop_reason=\"Text 'TERMINATE' mentioned\")"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["await <PERSON><PERSON><PERSON>(\n", "    team.run_stream(\n", "        task=\"Write a literature review on no code tools for building multi agent ai systems\",\n", "    )\n", ")\n", "\n", "await model_client.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}