﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <ItemGroup>
    <ProjectReference Include="..\Core\Microsoft.AutoGen.Core.csproj" />
    <ProjectReference Include="..\Contracts\Microsoft.AutoGen.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="..\..\..\..\protos\agent_worker.proto" GrpcServices="Client;Server" Link="Protos\agent_worker.proto" />
    <Protobuf Include="..\..\..\..\protos\cloudevent.proto" GrpcServices="Client;Server" Link="Protos\cloudevent.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Grpc.AspNetCore" />
    <PackageReference Include="Grpc.Net.ClientFactory" />
    <PackageReference Include="Grpc.Tools" PrivateAssets="All" />
  </ItemGroup>


</Project>
