<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(PackageTargetFrameworks)</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>AutoGen.DotnetInteractive</RootNamespace>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.DotnetInteractive</Title>
    <Description>
      Dotnet interactive integration for AutoGen agents
    </Description>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.DotNet.Interactive" />
    <PackageReference Include="System.Formats.Asn1" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="dotnet-tools.json" />
	  <EmbeddedResource Include="RestoreInteractive.config" />
	</ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="Microsoft.DotNet.Interactive.Jupyter" />
    <PackageReference Include="Microsoft.DotNet.Interactive.PackageManagement" />
  </ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
    <PackageReference Include="System.IO.Packaging" />
    <ProjectReference Include="..\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
	</ItemGroup>

</Project>
