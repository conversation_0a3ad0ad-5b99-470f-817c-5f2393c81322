name: Integration

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to run tests'
        required: true
        type: string

jobs:
  test:
    runs-on: ubuntu-latest
    environment: integration
    strategy:
      matrix:
        package:
          [
            "./packages/autogen-core",
            "./packages/autogen-ext",
            "./packages/autogen-agentchat",
          ]
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}
      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          version: "0.5.18"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Run uv sync
        run: |
          uv sync --locked --all-extras
          echo "PKG_NAME=$(basename '${{ matrix.package }}')" >> $GITHUB_ENV

        working-directory: ./python
      - name: Run task
        run: |
          source ${{ github.workspace }}/python/.venv/bin/activate
          poe --directory ${{ matrix.package }} test
        working-directory: ./python

      - name: Move coverage file
        run: |
          mv ${{ matrix.package }}/coverage.xml coverage_${{ env.PKG_NAME }}.xml
        working-directory: ./python

      - name: Upload coverage artifact
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ env.PKG_NAME }}
          path: ./python/coverage_${{ env.PKG_NAME }}.xml
