{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agents\n", "\n", "AutoGen AgentChat provides a set of preset Agents, each with variations in how an agent might respond to messages.\n", "All agents share the following attributes and methods:\n", "\n", "- {py:attr}`~autogen_agentchat.agents.BaseChatAgent.name`: The unique name of the agent.\n", "- {py:attr}`~autogen_agentchat.agents.BaseChatAgent.description`: The description of the agent in text.\n", "- {py:attr}`~autogen_agentchat.agents.BaseChatAgent.run`: The method that runs the agent given a task as a string or a list of messages, and returns a {py:class}`~autogen_agentchat.base.TaskResult`. **Agents are expected to be stateful and this method is expected to be called with new messages, not complete history**.\n", "- {py:attr}`~autogen_agentchat.agents.BaseChatAgent.run_stream`: Same as {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run` but returns an iterator of messages that subclass {py:class}`~autogen_agentchat.messages.BaseAgentEvent` or {py:class}`~autogen_agentchat.messages.BaseChatMessage` followed by a {py:class}`~autogen_agentchat.base.TaskResult` as the last item.\n", "\n", "See {py:mod}`autogen_agentchat.messages` for more information on AgentChat message types."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Assistant Agent\n", "\n", "{py:class}`~autogen_agentchat.agents.AssistantAgent` is a built-in agent that\n", "uses a language model and has the ability to use tools.\n", "\n", "```{warning}\n", "{py:class}`~autogen_agentchat.agents.AssistantAgent` is a \"kitchen sink\" agent\n", "for prototyping and educational purpose -- it is very general.\n", "Make sure you read the documentation and implementation to understand the design choices.\n", "Once you fully understand the design, you may want to implement your own agent.\n", "See [Custom Agent](../custom-agents.ipynb).\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.messages import StructuredMessage\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Define a tool that searches the web for information.\n", "# For simplicity, we will use a mock function here that returns a static string.\n", "async def web_search(query: str) -> str:\n", "    \"\"\"Find information on the web\"\"\"\n", "    return \"AutoGen is a programming framework for building multi-agent applications.\"\n", "\n", "\n", "# Create an agent that uses the OpenAI GPT-4o model.\n", "model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4.1-nano\",\n", "    # api_key=\"YOUR_API_KEY\",\n", ")\n", "agent = AssistantAgent(\n", "    name=\"assistant\",\n", "    model_client=model_client,\n", "    tools=[web_search],\n", "    system_message=\"Use tools to solve tasks.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Result\n", "\n", "We can use the {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run` method to get the agent run on a given task."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[TextMessage(source='user', models_usage=None, metadata={}, content='Find information on AutoGen', type='TextMessage'), ToolCallRequestEvent(source='assistant', models_usage=RequestUsage(prompt_tokens=61, completion_tokens=16), metadata={}, content=[FunctionCall(id='call_703i17OLXfztkuioUbkESnea', arguments='{\"query\":\"AutoGen\"}', name='web_search')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='assistant', models_usage=None, metadata={}, content=[FunctionExecutionResult(content='AutoGen is a programming framework for building multi-agent applications.', name='web_search', call_id='call_703i17OLXfztkuioUbkESnea', is_error=False)], type='ToolCallExecutionEvent'), ToolCallSummaryMessage(source='assistant', models_usage=None, metadata={}, content='AutoGen is a programming framework for building multi-agent applications.', type='ToolCallSummaryMessage')]\n"]}], "source": ["# Use asyncio.run(agent.run(...)) when running in a script.\n", "result = await agent.run(task=\"Find information on AutoGen\")\n", "print(result.messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The call to the {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run` method\n", "returns a {py:class}`~autogen_agentchat.base.TaskResult`\n", "with the list of messages in the {py:attr}`~autogen_agentchat.base.TaskResult.messages` attribute,\n", "which stores the agent's \"thought process\" as well as the final response.\n", "\n", "```{note}\n", "It is important to note that {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run`\n", "will update the internal state of the agent -- it will add the messages to the agent's\n", "message history. You can also call {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run`\n", "without a task to get the agent to generate responses given its current state.\n", "```\n", "\n", "```{note}\n", "Unlike in v0.2 AgentChat, the tools are executed by the same agent directly within\n", "the same call to {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run`.\n", "By default, the agent will return the result of the tool call as the final response.\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi-Modal Input\n", "\n", "The {py:class}`~autogen_agentchat.agents.AssistantAgent` can handle multi-modal input\n", "by providing the input as a {py:class}`~autogen_agentchat.messages.MultiModalMessage`."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAADICAIAAADdvUsCAAEAAElEQVR4nKz9WbMtS5Iehn2fR+Qa9nDGO9ateegu9AACaAAUAANANAaT0YyiSQ/kO3+BnvWgH0HJaJJJD/gBMJrRJBImURRpIJqUodFjdaG65rpVdz733HPOntaQGe56cI/IWGvvfasaYN5TWWvnypVDhM/+uQfx9d8FABJ3bTQAEIs/lTACd557zxVg7Xv6XvsTb30mFu3k/sN9m6oB/lT1zP4nQt8AmJCk7g+ex4/7mXffYBoxDMxZRKTQzERERPZlUhgUkLgRSEjClOqtFVCwgAQUagBA/zaTJAYjiOwDwsPnN7MYk3Y8nrMO6OFGkaP38g+C+U//aZxg5p9JJkpKSURIMOuDB2dvvPb6er1++fLlp59+KsCDBw+Wy2VKaUhZRMxsmqb9fj9Nk3AxjuNms9lut+M4qqqPT7t4P4MKA6CHI231Se58/qz5zuOq2iiqbUZMVMU8RP23t6+jgEhSmKqaWYxGSuyn4PA6ZoqOmNuVj46IiJM6k1g9Hif4XIiQ9NnJ8Mmzu6f2f6ntl/JSbBY00ybmaIY+5wbtRvEmZiDbQMawKl0q8Pb592xychLD11GwqvrQIon1gsnakxjoR/2NWI8AdH4NXlWqi4q7n6eb4F9lhm6TmhG0+thEamKvUoPTrvlGTLvdy5fTNE2r1Wq32V5eXgIopZRShmFYLpfLYUGylLLf76dJF8Nq0lKmyQgRMQKGYpooChODEQIaATXwmAMBOMf2E91Pt7nkAefrGIzwxxYQQoIQwg5+3piqv9p9wv3ouBNeT363L3LnxW8fvPPiRwczksCMRjMLVgwZ3Amwo4H7CzLs3W/ea57uRczM/yZp1gb03oub6xaLWbGZZLW+Ag0089ewA0HYnY97CH2xWEzTVEYFMIiQhJmqEkigkSCNUOf3+uwgQQUICmhAahzozGlVKIIuF6w+v/ViOJQWrZuOdKclYhSgf/35vdj9qT0N+WwDANVliWqSYbffjy8vFostzCY1AJvdeHNzs1gsVmNZL4qIlFLGcZymabsbnbeVkCQwWNECI8zfxAiYKSB1lKx/hkb0qv0sxHHA2iQSZjAhnQmdhMwNGSqRXPOA6CyLz+dDtlloR/pHMjv6IUOw3qsD+6/8gA+z/yHdOdJdJ8eJbtRhpvc2Cscb7+bBI+7/nBN6ovfNujdtV2+i6HOurG3Ij55Bqsi99dt2jvLg/Ea7R9s0TdM4YrKU8jAMi2FQ1WmabCoFpmpKZxcIaSIW5ilQ7UMXAtDQjUQi3eChds9j/fPUTy4p+nNuv9Hxux/RGenDa5Xx4oNIM7dcdCgMZovFYoROqigqIpIGkkh5sVznnME0GVC0FB2LFjXoCFJFKOJiwypvWBXfLux8b5hZyDWY6zcajWCn8WgApLIlq3RDSGl2R7o9KbylV49I6IgUb/PS/Rx4B7P1J9zaji97J69mMgEwMTOjujC3OuFBlAdvYPe4hHcfDgZjNYe6F+uIvqf9Ti5Z23/+JgdjGneUQ+epnjOrAqARaPvq+BkAnQxKEVmv1w/Oz09Xa/eIri+vJi3jOO7LVFQhFBEzVbHKh019GSEWLCmuPEHCKG7wSHueJo9MhFrZw4/7nBrF7hpp62irMapLh/64Ve1HIRWo9Kr+RJDNOJqRktWoxcztCUVeLEkWQ9mNAFSdViSlZEQIFAPE3R2Y+nuFoQjWl6n8UY0I+hS771SfmQZIDEuVZfW9WIfr6LgTrrust3mj+ZDoBZ9/7oaoZxJ0zHx0/PbBO5lNJNSgzEfEdaD7hCl8wvlF6vVdDPQa5o5JP9467jr64q5z7tU8TXscj9edW7PYDq5cOa1xwOHjVGOjs0W1u8TR+e6mL1Jer9cPz87Pzs6GlM3sxeLFNE3b7Xaz2+6mcZomA4rBjSWw4+hZ87vvN8+Iezif/zwdi1L5S0bkiM5ccxYgtdH2qxmMUAHgbha0DuM4qmRmGUBThdIys5EFCVrMPDIhIpKQkJCIYgYzNVeByElAgupvYZUWnaKd/80AoYFGaIx5aLn4llXaMx44jE/GIzdtOXuJFqPEQ1+uZ56j8akK+Y6hu29UOxv/XqPUmS0evk4razCGPauCJLOGuAQiTkhq55LObkN9oPum/66DHlvV9iZ+ofChRSujH9C9ohkhn8+BCNJMdsvn6bUc5yfxrfpOuPv8A6lhIIWEGVRN1UhZLpdDzqvlcpqmzWZzc3NzdXN5c3Oz2W7LVGwhVeZHcMgitiAwj+YkhPgn/FxocwWVVp8nntmP+IMRAtJ65XBrwJt08+sYCbotfHBc27Q3GiRB5oUwgUiAikhiFiRANcxrp6REmjBDrJTRvTXXe6ys5fI/joQO9P9RCQHVR5XhMWYRZyT37qxpu9BIMMa79MZnsGvdzzqwCR03OqpveXt8GpkdERsPLdKeb46OtM+3D4J0N9b/dOHkW+YcQM64tTkTl1Lum+l/p62ZhXdzLVEfzg98Ph/G9Dct0WkASeEb6PFv7j4fPNaBfryoaikFRTaby2GxXC5Xw5BTevro8TRN29X6asgJplMZd/utTmIorLHZ7rVhvQ4M/wfVEz9+njpER4QSYdj7DfSjcVNWHhPOUWKLwTezFj5H/RcZCC0AcmbOg5mVYoDBowkUA8Q4WYGqWjFBIpGElbUKLAldps+hlOqOCqDVBj7aW2fI+MRFEFnIyjlWj8BCvs37ygCfn/bALbo6YK9bwfk7x/mA2e76ioyYjB3e9zbH5sWw8ruaRbakaCis9nq9W6aHkZv2IeU7+BnAVNT9yOb+sfru82bdp3QgsXq71EekH18hTQ+DnNV4m9nv0GNsMpL98bt1Jup7CdRUcXFxUcYRk77xxhvTNC2HxZAkEUPK6/XyxWLgq1fXZqVMIIfFgnnpURxAUsqGec5aiIVMzgsHGklYCSyev4ZVSBG9SxPeSWcJKEQ/6wDYBy2soyHSAHXzUZLfrShIkSSoZBDn+w+pCcmoYd8arNnSkqqOYoy2IXVR36MHvo9PDp62o+Cmpo7ON1Xetd03UHp4pG0tc3j0lXSZ5/Yk970FScDCeqm/Sq7HhfQYqSE7+/VvFZbU7Xnunua+iT/aDBE1NvOQfSI9bmEwAT3V3e1rSNejBonzGYwovtVQXnzb1Fd88JhqjUng1hP2gSJgVhHHr1x/N6qRoNBIVez3+8vrq+HFQNN8ntbrVUopZUrCuN/fjLvNzYaZwuwT6RwoMwcm5YHHB9A4By58uJxNTSwMViMFMPcb77VFb89IUxG8h8p5x69mCnM+dEb1LGILrDeFfKyLbu0Zdmg1DezuJ+k/HJHTbQ78HCK8zZx2j9poI3Dn+fH6t3zL+4a6P+3gge9Sm/2lSOapKdyKqAjRq4YGz+juft946d1ca2RSM1AAgmIwMBFmIgCBuifhAUTJs5U/h1Fg6ja/QeJb9cjbbDXXQfRftQxBp0mAiIHOfxr4OSAewFQNKYtAoEW3++ny8lKnIrBFHk7Xy9P1ckgcJOm435Xp5X50piKoqlBFSjnnon7zyMv3zomLJHOBYxajJMIQTH5EKcnF0918eOuylQKCaeaJMzjup/4Ih/RxcKQnU2COgc80JYK75QLv/CBdfvhOijz6IDXE9SsyYYPL1FhrlcbpVpTVjamj2H9lh5TSnZrQdcERT3bjebxJ98DJ08R+HCSYSJI5kqQAuuQ4SXOBfdfozslZwy/dm9XwE5IRrobrJKY4DkQOmj5q7KWpukPhGlXabM5azudqHt9KRjYf6+bpUKD0puxdoX9BoIvEDAYVopjuxv3V1dV6uVgtZL1ep5TW6+XZ2dnGprPd5noc9/sxEBsyUJIpSQl9DvrwBp+oGVO8EhPEUxZGJgvB5OPNYGCmOyflTlI2oVT/s2OkmYjlHlJuH+5kwvgqRlv65M59T3Wf7O4f+/a3AUtCIGN63MycS2T4nHG8w9+0rWeSW89z9/ntnCPkTc9s/WP3Jxz+2YSVyHwi5SAw0/AxZkqSB+BEVxFHR8yj0m4NwsA50Hx7X4OQ1h62x8rcoT674TgwFG9ZvIGlkO6rjhVb7tvamTVqyab66kDemXkDXEknSFYoiqUkQx4Wi2GZBxHZ7/eXl5fjOJ6crBaLxcnJyTnKk/1UXr3a7fammoZFTqkoSzHJ8JQsqCEWoUZAUoxxpBKMSEbPshEUJcEEsGXM7oxp8TAf2B2fLaIUuVkDkEQAyC3j0JG0B8ed95w9fBqdAeUgw3Y8O4dqpH3wqCDu4odu1CsrVpmZ3JkBjXQFQDU/ogZxg93CHe2v2dhJ7sHWAneff8SB82D2NkI9B4fM2W53cP6hDuyvcxxNcZ/rGC5QP6hrKs5pDRNaF1ae42CetzE5Ioj7xj3uHjxxFFoEzByg7M5rGw9UomFVZbc+3KaD4yfpU6K33EJCPN1Mo4kM6/X6bL1aDnm9XC2WCyX2+72IJ864Wq0ePjq/nnZXN9eTmogkGWzSSUtwoNCQjBoBfYggwfMQjuqmgyElJkLoKNPAVNT44e2Nh8R3RDfxLY7nQg5/BSBR9FAQyyElHNyCJK0jkF/peW5/e/uHqOJDImVTR4cEkAAjEuDCzFM3ISjukQv335d3nn+EaO145uDPg6vc2nCLOX3AZl71Vzsgd6shc8xZXVTekIZJPtQ8VqMp7cx5bwRoNt+DLpfbtBE4Eu39/FeO8R9aU9r91URQ54mHTNWC/oeY0o790CUMD+al+2xqZh4UYU6L1XK5Xi1TWq1WpyfrxZD2+/1utzOzlEVh6/V6tVotFguzPQBVRc34AxIeH5sZFT6V9CF1kuSk4bz2z+8vebfermqf3Qefb+my2O3lUkd8s18kFIG0k6w6Vu0cArBUTVGA98iEboIOP7Qg4dG3RyUL8/5QafCQGI4vRSi0l8tt74iZFqJpv6LewZ/sHnI+kySpWvojt0842noNzO43qbcObg9cMx1x6HGHeSlih2M6P0f7vznax9CsSlA99MJD8+/YuGIKH2MelOYIGsNpBCqozRFhbsI1tL1SLdLCyfy+Yn53c2xTTbtYFx2dN+veokwQcfdVRCQnpsESkSSv1svVwnBzeflqO20Ww6BZuF6m5SqvVrtipcBQTBKYrEbDjebPGeLAsV02T1QFmsQBq/mMmjiSO4x4f0ceI7PaRXqKYZeGjoM2fxAkQ6mx69SubH59JFBVhWJ+BFVAH8UCUpeUF4NJDfvV6Hcfsmy8Lrf4xCTRtH8FHmqY/n3RAcT7DYdu3u0Pt8/vNWHvkaqW+8bz9qXqCe3NCKgb1DHJFNIy09AbkG3zpO2BwxqioPPZDjRYHTWAIbl9WKp7wEhMVP+ij7w2U9ZavrG9khOrR6tUtVjU8LVXVYIq5qUzQTQUSUplizo6GQGoIM72fPONepugPjlEcqIwEVSUy5trMztZrRdrvNrc7EoZUl6eP9rv9ze7/WbcQxYjBy5Oy14nm8AkOYnkhgQ09pINxUhCUjKzAgPMhCJMeQCg3RQEcURF4vzAlYwJWLXDIwtCQNAAOlF/YW6ZiNtxh+afgeZSUAiJoEtKodFF3JVlSoAWo8AdVY9/OxAK9I9qFJJCh7QR5sEJyThkWqe9BBbY7M6IBxK9skdmmcvqncrBMFZa0iQpc+a3ahYgpTt9QikGRGChHvdreXYgrF8agnVSyncyW6uiRK11FBERKtW8AIuWPEtgPsp0WiSRq1uA2VLxt4r5OdBT7gr2Nmo72S9TA4AtjRSvp4AZKRDW5IH1RN/E9rFa1JZeMhoJoZiUSn1hTAb6yZHNAqKinEWbOm+8F/eKyE17TlIaAuPgZVvmw2wCOY43aVTCLmS52J+s7ORktcxLWbCMtt+Xi5cXV/vtZhwNxDA40SiKsmVEDzaLJCRU2Gofm96LnL47ySRAg/R1uj7ppKOWQ8E3eupnkGSLjrUjxPERURCSSJhDXlJQqRii7KMdgaHCsvwCHa5FHSpTY9rBEId4qaN9jYG3ckEn/btTFLhn8yk9Oo08xqAdj0B/vPpsqAEq1sqjBFKSdYL76JF4aGWQlCi2cmXuAXMfKXNXhJRsVbIeE8fhPaLmU42syquehkrXvQ/WX8lpm6TNaRI2LOKRbVUdP7ED9kMEYpFMIIEuoJn1EftKkvUxDian3WBWH/0Hx0bfPrlmHZMVU4WZ6nY/TdM0YjVktZQXwzCIQnZTudpsP7l6tQ8VmjISgOIjUCMrAIIV4y4Sw+LvGon5BpqhVQ70L4RSbcX6IzfMOinKDvrXIo2sYYA+mheCshGNYygOgab9dKMjCQ0BMjNET45ROS6C6hjHER6Lof63vdqvhHI7NML2/Hdsgibpel69Lz/ZmJDdIMxvccd9j48cMaTBS5EgweP0cLhQpGK4AQxuiZAk830v01RqGx1VVVqtTu3YD1BCq7mlkbY4eGdnxHlg/QqcqbJt5iRUj8+AGIuMY72CUec8xPzYDctRvziUCNKIsvdsj2r2+muamYoANMkwLWZWSjEbso3GSW03Gbbb/X5/eXV9eX29udlZJkRS8rCnxzSN0sKIcvhUdfLoUSOSZJrrcVp6hvRcRXIPJa5G3lGxcUB/cx1NI68jn5D9h/qnVuumMSFDklfhQS/cFeKOKqFg+466Wg3B8ZTXb++GieFuJjya97olzrLlYDv61cFbdOboMRPi+L5WmfDomo1TWINGIiJkgctPikjqBJN005LveZljk3d+Djt4xTmsYubMV+MxocjCc/cAQ1hXEfTvcE8yXy+ghqE3SMJEmyUlIQTMzIElR0DK9mwiyToR3uiUxpkN2vj2jjsjsREvrqpRQR8OkAFKFoqlrCltS9mN02azudnvRwPzYMkRngBo6iZzouSagOk1CwCBtEROjTn5u9Xv2+ALPDneghiqRI2u6TxijOCEWJBRsJOHDdWcz4EDpZfoPmFcvnJdi7157f88Mu4wS8Xg3smHt5lQPpcJ25//tkyIexTt3Y833/0vwIT9VaN5ADoNiRpUFqGIuBXn754pLZXfJ+4zIFYDGAd7Jjf3PEOm8FYoZhRAxURrwCOCH5ZQ45OO/HC0h7WHildHSyEEUfo4V8Ek4tFRejxNnWW7AZ8Lo0hadIWIujJ0VxaPQjaXItylFheRTs+0X81E5tEsiaBrnBbocAqwK2DBMJqimOp+sgkJeSkAkgmSmqkqzEejekhtFgmaOCs5taIVFjS7kXPK24Qu5Cu73rbJOs3WgmQEZ7xHG+JOfh8ZZtU2ifq9IyXJ0NMdEYtUF6obSDSqvUMT3qGl4tu/EBPetwm7iG6LybPFa+e9H/+La8J7n79jwqoJ6+un1tmpL+p1wwfMGpiX471Q3LBUhZhpiGEHWNWf+xS7okguhQQCj3aJCWiqs7EUAZwglFRNsp4JPcmTGFhzp6c5dVgldZ3jOXl4OCSEh+7Muq86BkVVGk0fmmqEDegj46VxQFrCeQnNGKMCezUbJ5XdEsiUQpkoo5ViNFCYIABKjEkSUA6kdEyxB/8cFgOzyOhBxDQq/ir/R4zAAn3qitYvqFUckd4LrPPgqklJeNymBp/YeXMRtg1ISuQnZ7O8kuM87l1aj2iq+OC+cIsL8cG/lVbPf2vrL9tLjaMPhyLgjk1EPJ6CSqIRX5DkPjVAOhlTSFEqGeM4C6N2Ozu+b7qnK1/qRqZ52tKMz3YZ1Gva/BO3kXjHXlrU0TOH3uwkkBxSa7H9ONzCDJONZFSRkTQU77Di9bE1XJaOQxQhgxOqvPermoTqpMFYu1HRg4QC0kyNig6p4xdsgIFGLDXl44OS1JMWYq7Xa34ywtEmZiYmDAznZICaiQnUBFBh2pdiY2EuslgYMEF202TTBCbNEXs1GkUwR2Xm5hTmiZNglPAJvQpEa2zWzU5UYRGCAwE5sMj5JmDu/3dEow6qDAKYG2fdnSeMFhWY8789rbPThLG/p55oJuJOOdzHNkc/vH2Ro6v9sus48DpAy56Xak19GMOFADDBIunS37F7htv3dVf/dp3h0fu2I26/NKXnQy2N1Q0gMu6Njla5xv5gNXUOgWU1pgzUDj8w51JFcjcUJnOCy8NOnleYr1C9kbZHs2TVmFLEAtxMhYBU+vVr2rBeX9kNU+i3+iaWYaZ+Xw8BSaIIZDbFzQSm6vhXISRhaAkG39NTDjbp/ur6AluKEQl5kOVizlXmVOP14nCwmkeJkVAaNYDe6lE1N++SeGxeEBayde0eDubbAT1IlJpSAaQi/YG5uY3TAJpOk84nrBcGVW6FFoM/VVF7peTq2ACwMjXGVtUWz3Nibc/brtOio0fXvw/bOeSEaGnTbIIDyPXR+e340QWP8un1TE8T3BEdjUCR9ePdhl174vetlNG/FZGUwvKkRalcIn0m6R8Mq+USANUK7O5K3F+6Ge/RPM2iq4I/zq8dbwgYpLYViqRQXAFtMqxduaXOzHVIUGRClJCGPQYmq/gY1zCBksER8oNBhDWG7MUlQd9eXBtU2vqFNoSKoP8WAJMCtR4lJK4LoBBRTKGKTbRyiTJ8XX8xd18DedxetgYwrQo4zkEXWs3HwmmaSm+VQQ0HrSOpPlJd+bYjuP54VbOsAvFAPxwxf/shWRsH05nw4Ns7Ptydr7vvfPJAi85Pfs9FatG5BDal4ZQoNcujtbjf43OuWe7QhOzaJcd2j0OIqiEZOfqZCXNliDov8MvutzdNWt2rCQ803Z3Hebhv/gMPT6tnzwBOz92SHQuiUrY395rj2KwC1Tz5EOwt843gLJogVBXXuSAhtWiY1ZV1VdxaPtTndAzAPS+LY1+ubU1ZM3xIVD2nbCFcmYdFmg7s6uiB6jfWXKgTqRw+UE+j8zgHa4YBS9KSyNxkpQYAbl1HZo1knW4k6cA3dWjd4X3lEKU0MyQTmRxHQipQXGs1Tdiu4DqkzxP2vNeHcPrjM7ndkgi3j8N9eyo8gBj7GN3anaMT/2SxW3f0SXJN2D2eMyEPJ69tSWYmbHsmE8xQhdSe3bAcllK3QwD3wXzde7ylJYzoOeGAFQ83CzEQgI5Z/eOAlwAP/CgilBgTb6jBuu54F0pzok9MrI/k1YkpVB8ADx6YBL81LDirvD3KLlWGCWznHQNRh6P+nC3m0MAfNXqv7tw2aHSX7YC4i+A0YwZjy812gnnGuDogOyLiRvF0djQ069Aptb1sTXa36eu1X+ybT8hoaNzg+/MP6/izGqVtatwQBdA0YT8m6G7n0ug2xvXoeQ4/HPw5v8U9V5CESjbzvgsDz5iVMEe7a97+cGusrK8a6Z+qRUf7ekIBEiFQb43l2lhC2Rhs0iKmlpHusUjv14S3v5lZq/22MiSj1Yu044wyHmfmoNNqu6o6VhAA2jS7IVpjG/VzfQ61w4HsxcT8IgFAE4Cm3aPKrMfu5rX7kBn+2BJAHpIgxCTyHNblIZFIUCRwlvXGACLPTtZWvLXeQUJN2W3i8Oaw4tFOL/PV5nt4gxCBNibsu4z11vaRLxQnQEmzDvtmFXHKQ43R80xjQpKLxSLnnHPebDYHj92YpIrd4+P31fuZ3saTfE7HCndn+kdtP/GnbvK2ireDK5MH4L5e0sZEkT1cqbuvD7bzeaUpU9DEkbMwF8We5pM5+4LPS9bfefwwadfVVHd9k3pkGLsX8Qwh0ffPrIZP7JLZhDpC/vLaXafGzTsKOBRX7crWHbTa4QKgWKP4QD/U6+CuSbl/HCq3N7FXH4bkDKZt5EB3IFGDI3HH5OwQb1oRuyJS6udeH8ZdxZlaqtJzfrY5Nd9pwtpdnm3qjmBZh+8YbWRun9+f2XtQc/pLJKW0WCxWq9UwDNvtdqaB+fqJ9+cV7zofbYDbkb4e4hYTKqvlcpsJgcPJimwe0aR+93ZSi577X7EaRj1bHr1FfwuhJChFPS5JQFLEJJY5mTl8xf7iTHiY6AnCrTmlXnjXxHd16lBtsECfpY79pEPezO9mklBJSgPfLUALeIaegZdZVWJlxQHzsI+gfzD1H4acI8MhbDxTJxM+ByJzsVOPgCs1vtEPOj0lhQ7UUtmvCos5OkqLFEVDzDiC20ijzDiEgxcxNWXU/Qa0P1KJBhF61Y+3mYqcIbvXr7KOISyO1QgpXnDc+4Q1MnTMwPWaIox/hGixcT+Z9/85ui98YQ69cwGpnpoPjutBd4mj4OftnyBCV6hD3nTm8V3g/cdqNKIdD6x2SjhkQhEvZ9K+bcydTDj/BJqj/o+sI5VAUHa7jSRmySLMhhrlPNrfc9ww/zVPRBhSwQCdsu8ALkBNNMfAzCUOfg3ngJRjuoVEaq9kWtD/vIdesJJLVR2RVbNmfMh8QsRTHeqawmnvqgpr5wvPskA6tmnSJyK3zh4doheARY+0CrWpz9OlB/thq8bqodXUrhYmZVsFxoyWxFW4RWEovb1ucKn7HrP4Y2c7HUdKm292GH40HPzJbgZn+m0PnDytgoIyTcWmHXdCQXL30hFUUcLDYIxahI1oaiVHec65/rB6bT2t+xD1TCizTwsECP6AOXtZ2fOMabi6c2iqMl6upV44+C3iPodX6yukD/gwSrKMQoHLqvDm82pYLpfnp6enp2v3CW+1HoRauI8He5t9mJ7sOtxJJfd+nvo/rRWzqAdCw7szwpfOQutX2YUiACBl1EKdjlwO6MLjLXUIxIyImgM6dgyg6QQrrgtDKFRccuhgn54apu9TQr2rSZEQN/1zmoRxaOLxjVnvSS49A7o6Y7aiZe4qJxSmg3KkFsPwiIuoqUDEXPOQjKalZu4nMkESah6vslklU0XT+abRVSPoJlpIKmoHRsRVUkegbA/WhkWo3lCfRkkS0ekeSO+/88RAVFGEcWggjJIA9aqCOA4FokWVqtqhMZlb+puh3pp5nIjJe62gvVdI+tzVqc76XFC69QZnVANgpfe0fH0NECaDAE381a1KAYlcfL1OEgkciKcczVDMCNP1cnG+Xjw8XaxWOTdOONqTnDNjbU8xKwAs4nUV74J4hTkAU2dAWqamUWqcLgcCCjBvSTovZsTjD7ePN13KthBaDcGYX8rBCQJJYAINVoSCBoUjHU0VeK6W2q4AalaMgR2mVFrAFv0HJiLVOZ7DCcauVMoRPxJmk8tIdBRTidztATZbrqZ9hUw1D+OsPEO306HsY/RzOmjcFJWfOpHCLq1f6YBN+fSlpvdtIlDWLpQBHFcgWv7Xth1W+ZCzFRH/F/nVeOBm/lXu8udr8Vh4INish4OT0SvZgEHyEbwzGKYxntWobx+mQmVCVFBLl2LpwNazhJrZz+9SHRxEZfPsRRe/i0AkD1mWeUiCl58+G7eb/c2VJOT7UmR3RgUJqAbQ2Tnd2KRUQ6JFnDhYsQb8rDe65tRAzVMD7qWirS72uRq1fbDwFsNSVQhd/bMuAtGCPtREQ0qepY3Xk5hi7+QNHlV1zInKUEz1+edD7ZO4bpX+/GZ4s3JTwzNU6dOFGYL8agy4kV28gtZGXYhQiE+8G2ZaQ9ROKzVA5w7qbSb0d+zdm2r6HoQ95uM2mx1NvRgpSVpf7fbis1FdNU8/9WiMHS9pZFTbeJ7TZyT5vPCgtwUQdJJ67GsXTBuGQSt+s83i/DwVITT/tnvC+r6dQOx8wuSxL07xKwBmqT5X6pqmpxD8UIM46FeNNJGUJOechyznDx6tlsN6uVjklL0ntFdF9Pv7jns8XsI69v/RiwUQ+bGInfRxzy5aM5vPDcYTLKoek0hhyAjDD52bXwS6/+C4m6GEMQECiDrihGzVG24JSC0q6H3asNkD0d6eN+RdeH1AXzWChshhQ5x67ciB7OhpDqzm62FDJ5BeoolO/LvjlPoGQRHYDZaLcFErC6KXzLKGyL1dOOcbzSx9eJDzT1wakETnlB4cP7hOzLACOSWNGuvYB4t3LRV7JlQflNrxhE3fOmN7Ck+qiBEmpvlJKkuh+myxeRcQM0NKKTUQNWtzELL2+TxMtCBECXpjJHXf9kwY7TYoKkWiriuRRiNpIgJ13IUKhOKUriKDodD1V8HIYjsbR4AJTJDEYZk1kld37O2uPUW8n4FGhD2w/2Ryg4AVisEwe+ZSPme83jEKue2ELIwygi6O4nZ0RLtwYNZ66U+9dDKI0VsqSV0Q16sf1MREzdHjE2kUeJlL3FoAMnWavEHbCIV3VfVlzGqmzSCS60rXnhpQIkH0qBN5sDpDVLbnb4SlZrEcAhnMH+CE2feoNKEEBEms6gHXEnWJM3iAoaoOVDvtFuMdEGJ/8tHxhJkDayEVeFhzILUIKwzC+jk8NxxrnqhrqVV1zuUk2dWFSA0Y+xp+YcnXPGedIuk/RucHs3jskHdWPyB5mOqwq6oGk2nqtN/skMxR0yjlbbAKqSIzgv20REiKBiWMljpGEmpCcb+XhjKVgrKzAth+Gm+2+5SvM5P4ao5He9Tw29EeAMTM0rygyryAM+ducwFgVzACP1b7THk1lylBM/fTvJ7Aai0iEBqGXosh9M9QMLE77pPj7GdCIIND6KKoK1PYBBqksPNja1FfDbxH9qdNa9OJUvHTfoIzLiOLQBJiNKk1MgDrSnDRyGgOzFSW4yHp930pqzx2HnRdd7BwhTDyNuGlRMlfk1FRRHkHy/VxviDcIPeO+A7M0e55qlysV0vdh3a0fejbZzQdeMCE3YCIQCpJt+OCOf5pXQl6Y8IejY3q3zsftrrEo+dJVXP2caZEMysmSTpztD2zmR0cJ4WF4gKVDS1MUiyJhHY3a1FLA1E0JkQk06DqYHRK4mhWpmKjZaYBXUqi7aNO4vYeMDfDvGSp9uSsnNMTt5AOE9FqnoahGzAXMTr836I4OOgeQCNrJ7MWX7l1PJ6LCcyu34EMIiokbIQJUJyr3SAzygE3ILkmx7xJZ7K2uNSxIepvZB1AnDNkvFJ9u2SYW87S1Qow9N3laqTbc33OJAcpNQIDWX9dY68CM5MOgpPQUWvguGYS58EFZ3PUCS7d7vLS+5O9RIh01NyOONi1M+HYBT/8xQ+MTwHd9xOjuVVVepeFJKGsmSanbq9UitRPx5zOelLNK1Rjso1te+x20MTXnOwwLjCveBBfK6XLUpIUL4nqPYgqDNz7qaZcCwtHbMIUxSOaFoGh3XZ0O45iGSnVzNCvtNdiYFJ3mluYQSMI0IHLvEeDY47iZCG94bRRmtVUkxZigFW4lcSCW+KaphUZHR0PMxZQGUABnAkrcBRRHgQms0ldf7o8YcWjUFo1rM6UFu8EkCkfAM06e0FRebiaA0ab4VeHTNiEKzo6ZkWcADO613VhpcL+500dz6eGN0C3+WpxRZ/3q0Wlt5jQjcADJqwkfkhh9ThwwEJVZFtDRLATNP1bz3UbAOty3/4AieKIDMYSn2LN/QAMRWpwqx+0PmORmjPfWRb+yk0lidXnP/KKY3eABwgF4iCNVKcpTA8TlKoDu9/cM8UkU0pFo8hLu7yH+eK27t+apNv5wPv2RgWbNqivUI33kHpz4CQStWimUq3XNkDaeobzlIEJSRZK9KEgX6tWmIpNhDClhDTqKJCUkk5h/Ax5MUGmYtACQtYrLSP23rjSLA8mTjgHsHMvRIIQOpepzw8E84aVVl38Ntqs8OyeOECWMkFsDi10ZtIBXXbXmpNLICycq8UwAA15GwNFQ5bUVA18YAygL6aXUoxuQiUHF3XVmAwWBZBzMrNW89yoqk+FW/FyhLB50BmlcTIBaMt/9lkyv07i3L02LisHLCSt/YaVsJ6k+ocurIO+pBcHBixyrVqoj1R9v16UBl5USFUjmattWyM5OgwJnYRCZVTh3AeEs5FiSQbrVgI7Iou+/MXMCqzsJwXNiqqWerqjc82gaqWUbBGE+NX2cBswBUC5rqMKr5SvQnbGasZaaG60+XoSgesytJxLNYJ9SikWi8mIkXBJR1E1SiZFcjI182x4yqnszYzTZKUkSSkPssyS8vXLTzHkvFgsFmcpSSllt9tN4w45gxrAAM+iuviXbDXe2jXFYYBd5ugSI+15K84WI5EDs9Iz4dGZ7H6ScMvnEUBtNqXmCT9YImDeKiaGDM3WaMJDVz3aDoeapHvTf5uNBqa5J9VtJpTbTIhZvzOa95GoJH84Mo6AmXWO8CDgWdmv4YoApHzQn5qEVNsfYX77NT03l4DJItlSDV4jaEniqJv97QrV4LxjMIOazeCoxqJKmRRmMxMiCC7GwOu8MyTPfs6vskdnabHlAzlbTg00M6vKaKJeBzap1GY7x74WDAFkqVjR0KswhVAkQcS0GCTlYbkcTpaDaJmmaRzHqexQwJIkpTcfn+zGcbu72e4ujUnyIGmQ9XqKztFWhUvUE1bcV6zQ1oTDnBLsTEM3z9kdQZhnTNIyzFVpHNJWU33+k7nGrLtcFXtuvfUzjhlM1caL7vP4e7jqiBCl24YHGhyHxmr3oQap0KyacGuOroI5qQ3HjDZBU2vuAEBt7gJYbYFOZTmUpzqEbgBaPHld5cVSp4vaiKHazI0JU3fZzIPF45oSq3FLVG/J4WQujF09KLyygYSDqoMNIwnhmlC1hfW6/FNwNrz+X1WLqi98PalrwlC87Vc556ZEM4RgcsfpV9rHPPT0F6n3iuJAUPO8keEiApLjOq7lgBmpQ8DEmxEbk+OTPdyXaJORNJME0wJSJC9X5yeLt06XS1Fo2W2ub64vN5ub3W63n8bxSpjyaliuFyvLwwTZlXHcWz5ZeVBGg0b92aAl1gVsTNM9/oEmrFbLHDSf6aMzzG+xVsTljnwVHgr1uInYDByvh/vEQB1TgMqaDJiP12p0j9XJDMqZ36kVrc6/qhaXq6xGIvES7N60vpy1dQIPmZmkWZcpacaniJWozvaAO31A5lVZXDzGk4Qz6SKvCgp2WCR2itdfMQlcfHpzMnq4j+rdhFACEUwxYXIX1KIDTZJ5FFNK8dSoxcBVXdZov6+1AfVwoynVCpTFiiomLVZQvPFXNUL90nak7YU52nrfBeG+cx9kYR0fRiCE6EscgiGjmNAsXCgDootZY1d014RHpxpji9uigEkSM1WDmQMR8rBYnqxXv/aVt946Xzw8P0sYp+3N5ubi8urV1dXVD3/6s82Ey3G3Gae97TmcDstVyYutGShQD+xV0pdoNW+oVb+hgR2GoB2Nh3pu5Ni4y4dCiFZQFZRnYbRIPZnzlxHEl5ny3NKn3IIs+T0OQpGicKxtNNJygV2ZwaAwEanrOlRZUlMIvU/YCYKZCVnPDJboAhuNCX2uWtiDPmcWJ4WO6m7R166J++fm+XoF4FgNhl0VN1FaNSnRDxqqovOrY2ZikypMGevKOnZj7owk9PXVLHX1tOz06m1IN70hsohRYilPk4L4flJ1O7MYitpkMLWCWB/FqiAnmdyaqTMuRLa6vNmvtG/E1te5R7SrsSK8Wb0TZUjruEudTM7SoJPGzmvhKM9y1KVRUIU5hfhiY9Qy6P7pavXVtx49ebA+GWwQ2++ur29unr/6rV88++zPf/LBn7/3yYevtjc7BSBJEOHZII0ZVF0nOcp84x0FGFu8tGMe5poHQ596osGrPeqksuEe1VBDoFK9I0GU6hy2jjUBkqQayq1DbtRKiPE8kXpWMkWHJXampktfUmvFYhtmoEYdYC3nVkXJgRnpbyHiad4DeFclAG32GDtQdQvwHGnUvr1Fi8okVM+qwiy6oTZhE0BRu+jnz2Nb31cBiimVaghEVrMgAJAS0Q2KBb12DakqyVa7piPM7o/kob5iNDdOrShsKpPPXYmGV1AzBdsqW012CVoQoNIS5S/W68nMmg6sUlcaT6KVlgOAQMzUyNxHma1qkt7aagUKjt1rf6DJURHvN916zo1l2tzs3v/JZw83j870Mj15kM+XJ2fDScbpcvrCV177yttPv/nVL//2Z9c//vDiBx98+tOPnn98cSEPnqqPo2MsAEe9Gq0v2HTFLjSNLiWzrojaglswqFR9YGJuKuHhGRebqOZvqrEYVELsa2HMQEOSVONgjRQ8J4SusnvWwymS/uEsATCoeMGipD5D7ZowpXs0Ic1FTAuoCCgUSwf6oT0/au4OTShUJvSyHdT4cApE6EzfqYtgleJXQJ4DVQQcID7roqgeFimlzM8c1IYCY9JUE38Ry0dDycfsSSS3CMyxRfIAYevZhIN0EwCImRVAa2u5ompmCiulaNVVIQBEaArYvE4Bq/KLFU4hknjn+oRtO4qq1cfotH98qIofQOqPH0cFY4KCr+LMg3JeAt4UWDsjOm5LV4Ke25imaTeOm/1uOB2SlbLb3lzYWhcnWC9Ww2Aq282DvFo+efDoweM3n+7feePp1z58/sHl9g9/+sGWZmAh1WAU75g2qbVhCq6BUSxXQ1ksKhpSMKl1Gar6AoYsyazUzqwiRI7ouhM0xJdZa/cxkwp5q2EqEmihBDHvu6jBhhLLL6FrPMHIj2kn3HxGaMXmIoBu4Uc39H2F6b4BkbV8QI37CSgirmmPNGE0bqKaeuIXQJCXRWdY+DwakAUkkiVfyM7HpzKSW+uWUFsk0RyBic7lJZkTRRJpVgLyMEdhiAyAAmRfBFKSkZKq3UUmmU0OMTNP7DT67KHtsVbUYfDYIJOpGh34UooVi8ZWk/UxavdnQdIrsSJLXotuxeYgllBy6vjQuW6uRq8fDgpzm1HaOyd3bWYGk5QTowFrfQbSiKhaQAhLJZI35M0BsL61UacJIirJSmHKTx4//tbT89fkYrHEVNJ2i0tMnDabvEtiT5480c3Wym4FvgU5O8/vyINn+4crsQ+u98+ev3xxebNV47BKwxppUAkZ5soj1ZDjCkG7zfGz2RmuT9ksOjK5rZ8TKZwBA7YYmm4prGCsFGZ2MZtUHXMfJY5t+g3mGTEhjabJFEUVSeGAVxrEHPuQJaFYURSFt4mTxZDb+qGMKxkQfS+kMiRBQBMZChguURuB2pBmMzLNvoVEdDkFugzVg6rmbt+GIRK+lQQrfhSkuKhqAHRWTRURxaAAEAYrgJh4S+qKvHUlCXKRlvsyJWJYJJqWcUdDHlIpBWZWinkwVCSJpJzANBYb8gBgs9mQXK1WqgqjqlKYUlK1/X5vZpLSfoJq8cx7mcwiIFrdbXj3NojIAJqAKfcLp/pb0FAtKbWimWTTeERd1aipVFSMVaiDCGDUH9TjPMw4NS8Ztaa2QZPptecNZZraahNei1aIfv3XmaUBJl/NmY7RKGbbqeSzk2FFLk+5oCaOlIkC2OXVTlWLeTtbLqZyovsTwzfffvNsp09Ozz96/uLTq+31ZJMkpXh1hRGkEOr9vTM4WEnWv1PXbK5P1AIMd99FNWsMT6gFYS5aNcyq+xk/pJqJwcQgvgoICKG6wUwSyY3YhL2N3oopp2EhAw02FS0l5Rz+LKx443JxJ8qxfrNUTZE+BDoYGmsQ5aBivTeG+zjwLHm1mtMOoNWmI6ttfihNaZkZkXYTj9vGXSKY6UxoDZ/U83C1U90GdQXZ6R+ClGlSK2YUK8h0Q0QzJCdrjXA8HOorUp+enV9eXpYymvB0ucg5m+nVbrNcLjeT7nY7AMOwSClN07TZbFSSGdVUFeoJ5h7wUEeJUVUYhqID3lDNftQi4/jMlHuz0z8e18vNXnglt+547FX7I2yeenWM/bjBGxtFBMuaTV8RNuZpw1m4tMI/AejyyxLNbFLb7EcdzmyV09lDGTjpdK3jfjvRpsVuMhSFmCSF7Eq53pWrEftJE/PJ8uTsZNqOLJgmDhyWo5pGVlCFxeleYKnVOB36fs1lal+FMkTmvMxAiEVQfaVh6aIUAjrvt2acSvROO9RQvJMas/e9Tsgqk5mpJpsIJibLEdiRMCvh7C7iccfAGQOQmp1ro1opw9Ua3d7rX7YRSpimXVjS3Y8UdWMkUVcRtkaOPRt7ND+l5DnxbsRImiJxBtDMDNZtqQZOneEMvmxB51sqkiEJs9BySgmQbAJN5DiNYpaARCrVPNSu5WpzKbTT9Xoyvbi82oLr9frBMl9vLld5yCvZbccybmkZqlPZq2WNrIODscJyTMkJ3ms1wn0FYFakYlMDR0UCGAZHfoNkVkmAduvIdh2sY22mqJfrOlsrLEEUgeUUE4WmOJ5MLFky0ahYhz9XzZC4mos6V4sskxElJl66ng5ANZnsVop5mqbLjf3i073oGZarPZZLQzbIpNCySKYw1TJh2qttd/ur7e564vtXFxsMu0mn3ZQgqzyMyApjZ24JzPNLJAoaTRwY3jONNuHnLArpShMcoRu8VX3IOdafIIBBtCKxw/qg9+6mSRIxL5dSMaNiQe8jnEopZZoKp5wXaTFM02TRrMBac00veFJIPGSr2OiyeIx+mO4UHcPrDtDbh0lRCmsR0Gxc9hwIqlhLFwfDCJrCCzklaH6fzk0771jVuHNuxcG/TbJI42dLgymFlmhASTQaEqb1yZJQMZhNChgsCylYCVbL5dn5mZk902k3TsMqS8qXL65ee+vt5enZi5cXzz77bLfdUiRRxjKqpQhEeeQfBFSkJh7qU4oIaaUUtqCuNDbkMg+NbHIJuieibMdrFLwfhJjRVVDFDtRvrZZ80WLJIVGI1806PhtI3WpKDVVQGRKqGkAfWCCimzI/WLY+zglNKwbxSKkWbPbjjz54cXV98vxqc36yOl/m81U+W+Qhr2y3V0BV92q7/XizH292+5vRNvu81XE3WVEb0mKdhFPZqw3Rr1zNvI2SJaqRe0jxMGVNGDhdNoziAdDeIFEW5W+czczLkUSl5kelEXREqBziIay6X2mmUxFBouSaMKSqTOV0wdVyNazWo5bLzf5mvzNRSUnVfAIjgGmW6DETSF0ALFS6Od2TjIWIIx2OKGBrGM0QP9ZecFaDdWpFQgsdBSo18DpAyxX7V8lmc8pN8Yhb0QiblcatoIBDHILoWjcB61OIAKWoEUymUE3UZc6DWCLXw6A6lWmvNhHMQx6GISd88Y3Hm6srtX1aLNavP9oVvbzZfPLZsy+++fTRk4eW8uWl0aZSlBA1Ue3iFeykgge1A/XdpjIQVN5nbahMCCDPtcrM1jd66vRh1OOh7qPDUFdR3vSkH0l1vQcxRq+xZDU429aiQKXn6qjUoqGa4m+4kDbfLSKHGv5mjWuPZaIsn2+nq49fDGKnQ3p8tn58froeKm6TmEx3Rfej7kaMhTej7ksZC0HJeSGSJNtQSglQQySUCSRCibFXFDUaKQ6psPAJ5rY/QisR0YnCJvcM4o2EtQqm2v1W80edmqWXihdRpiyZNiTJlmGWDGeDPDxZrs8ejMRyefP8ijfTNOoUoSQRUxOz6gtSKH3fRwnYhwZIi2AsGeMjrGFlBrvOTmCr9I/ogsx4HemoMUVVu3dG8tl3YdzFVwiBQExqXMcbv/g45zocLngESVFieUeStORR01ks+tAiMRkgaolIZAaWi+HB6fJ0OQxiiywso1kBdUh5sVgsl8shM+u4w4opQ/LNftyOhWKlnL/xhbc3o37y8vmrV68mVSYpZvtxlLxq3aarHeEhEa0i1VOvodvzkBIhTJKYmMShl7BBvMuekMgcFjPjdXuLIiEf4eQmbeg6aeEWb3spAdZqC4YiGWkmGvqV8/DXuU3Irblggz7RxBHqPRM66UuVfazJIhpMdXHyWG1/NU62213tp6vJPr3eEXq2PhGBiJikyXQyTAVm3Oz2ZskTzNBCUiQtc9pNI6D0NYHdyvLajS6wIVG0D3iPEzj1NNuSBC1Cz9Y1+CArkJLxfTgVwcZNz0R2DiJY5IU7N6uclsOwGvIyDwvYWbIhJ0cpZtpy4GSiZg7mcGNPCmmWARKW2KIvsz5ykGlUkwVok2Q0+KK2NH0AaAzzsjhscpU+o6lvaRNtTpSt3LGG2+PnrdmuN7R098lkDnLWJJ+bfJlUioPzfJSkeoaV6J084BWuoC6iyNrOFnxyunp4ulxmvPboPCdZJBkWHFLVqMDL51fy8EFeLjb76YNPnu3244PTs/XpwxeXl88vrj968fLyZjMhWYJCNDF5dXANXkcKhCbD4LXzlT7hi2OvsouPJILMLAleQ5vF05mJtIwsh5EoocEomErV8bXUvVk0gVbyqGEK0IzDzWFA8m5bilAeFsu8HKDmQrTOrRM1bq3WRydadljEK3E9GpCyJDNT5E2xIQ15kWVYiLAIX43b/W53OTGllFJiKuoCEAaTYVgKEyhTKbtpb+OYcpYhN3w/Y5XPSDVlsGnCINIacojKwtr32vVhBbW4m4QQagk1DBPiA6yh0taRIVGgoJolWFkv1zQdiOWQTpars/XqbLU+yekUuttcv7i5vt5td5MaLGVm4wQVBuhOzESLZ729KkWqgXRY+xsZC180tiYGDGByiFZzCGtsivX1Z73qs2rVvBQTg0UzyTDJqqca094md24qIU5AkeJOYX/5eLcQrjXVUf1PSsu+0ARIUGpJGN0clTwOZbEElsayvVguFsu8XKWFCKZJx91mN5bHDx9cXu8uLq9eXm1efHZxsd3eTHa52z97eXkzlZ2yME/KcTJJeVgvUBw2STNPFEcOYTnkqjys+X4JthDxDl3SLRQjETiQRIikjFuImUCKpn4lPcxNk1I6OD7/jLO4mze16KNVVTWjP8o07UVcV4mERofBVut1EzNu8QfPJjbcMyP8TwxLJgFUTWE2wdQ0LU6HYQ1KoU5mqlCDUrwfUaHXlikIDpmA0lRHM/OwRBbvRKBmZtDlsJxcENTyIvFMl0XKFTMyGwByDfBEAK+K5zJOSXJOWWo9TquqMzOoqU7uMKbELEPZT+vlcLpcJM9gFB2G4fzs9GEWPnowXF5uPvpwf3NpSSQngS2Hhd82G5OAiPVfJw3oKgOwEvk9oMX3WxYLCG06J5cbv+lUfEpYLdc6fagQML9PmOzSOZCNSUhqqexKk+rjAUg5Ve9RJRJkruLoIjGe2bmXlnPKedDJbm6uAKzXy0XKNo1ZNKNQJ9ORU9J90l3hIm8vr4bTNYZzFNXRdJq0TFnSJy8vNyMMeXn26KEtX3388fXVq12Rzd6KLJCSiZBe/8apgKVk1wpJBG0Jg0wDxXyRuHltemAxePK1HvGXskhR+AgftzwMDkQtI7mD13ib1dA1Vz1iwskmY+OcpvcwpFQVt1sXAodZ0JDmrqCogRB2nRH8CoksBKVGhAyxsrABUAWVCk+WM3J60tlGM2qHANQb7rty9tugrsA20NGGmpBEIEiJNu2LIz+EFusSV7xLtLpwy6xmVYfV0kMIWkYtqggYlxYdUhqWA/OCaqWM47Tf78ch5TLub8o+gZko416ncXvxav2FN1eLYX1ycnp6utjsJi0O0yFqx2/vjhGdOZjFFEzi/l5VZd6gzbQZdbGYW/jmnP+rGRqmOXzcPPMEcwu4zayh+O+8WW4K/U/AS4fQVkHxbGoKTSJA4MW9aND1Yv88gCWkKDI1u768WC6Xi8Xi7GTlwsNQBGWRCvY7Qh89OnnnC2++9drTDN1uLtfLs8ViGIZBVUspRQtIpGEcuVXbjWUs+09fXX3y4vKTzy62RUsalEmZFVQ39yCEZknOhNUHn0UbiUQIJYDNpJglQazN7Y5cI0ODIOIp2Q7jUDOur2O1NvpHiZt+S3dxpkVvttSeFQ1n2AzOWPnNHySJtNXBDnxCHCWpPPDgdO0Eb0iU5Kuriq9qHeAP83kkzArnUCfaZUEvoK+wumBTEwPh0SUVM1KTMkHDnghglomRUQYlUXBZ9UBVCJYkENUUJEky5PVimXO+vrxKKeWcmWBFJ6ppmqSs12uWCTqF+2O23e6m3fbZq+XD83PLkpaLYRhkp4lMKat531TzTuPCePiMqJKp9ogvjdl3ZANmvR3Zy5i9+FiziB1tsObBhIG8qAMXrnuSRPG20w2GNl9xnv0qlJtvDKjr4oiQsXmnrTwXQpysFiJMtCGlUsq43wFYZ1mIPHr4+PHD84cPzh8/PD1dL4Q4OVkNi0RSVTfb7cXV5ubmZjfuRyw+fjVejpgmLWpXN9vnrzaXey2UNGRFRhgG3gtXCF1l9QU/ciiQubKRYtkLx2oIVGiZELefxaNlTb1otE0wPV4Qxmp0+ADtXkch3c+Kfea6OwpfWjlJ8lgtABQ1aMq5Pn1neCDijV7xRaQsiPJfZaAxa52Ym9RaHX1PS0s0nVCp6W+xYpRoLYUsDqjtypTdzQyE/ox+8KyeLkQCjR3eMIhCa70tvD2AY0D8QtEgo+s8bxIxdXUspIgMi7RYpkXOg5yVUqZpmiZHIZblclitcyIlDaJebqwGTKoKe++TZxe7zWK5vtrtRgMpWQZI0lpB74/kMpEWIHQHpkYXGRTA+5pGjDG40fN+2X2tzhsk2zlHqxS5E1//QtPAyJqFCAaMTGBPaT7UbLFNr02GphYcaifOcVF3uqJe7uTkZNxtS5kmk1IKYefn5689On96Mjx5cPrwwXqRUBTbyYqONpXLly8BaMHNdvPi4tWLlxcXV5dXe1yX5W6EEikNCtsp8vJkkYe9FscbhfPvTimYmbLzlCCxoaMs0VfgmDOBPvtZLIWvmzxwV1mq1RQxu5y/A6sZY+q/iGFxdO9dPHgPEwK13YoM3szGpbFxmRegdsotyu0mLS3UJkKB1VUAgg+qkjSGGxN2pXjOsmk49zTgXXcbJFeTQ7u6f10200en2kBkUlvSKgxyJj6S0zT5b2rC0Mjo4Z04n+/PIYachCI0X0mz7Db7cbdNlMcPH0Jt0qmMYylFEpCySJrGaZFEhoyiZdT9NFJNBDu1m4uS82acdFKVNAjF1FdyQFSa0t/KGGhUTw2iooCTmT+266K+q6JkCqCtVQpngQKgJUXrBEUfrrbGXMR34zrUCKVE9LUjrsO2aynoyjOpB4vDdHFR0iukDGYFWmCx+KrktF6u3nrz9XfefvxgDRtxsSlWighKKa9evXr58uWHn3zsDcpGte1+utlsN/txPyGfnE6Eqoq7LSIiWXLOE1o9ED3yT9IwUJoh2vmEMuRMBy2mecFQGrPMC/D1npzZbINm6erifKsA3PCPekHo974zMNMvj3zAhLXhj+tpGkyEaosh0ZLnsnviThPV6+YEuT66zKtpUtisPfeDwsZhEJb3AWVrxkE6iN1LFdKRuAksTH1UL/NtSYMswLgVnZ+w+rA1SGyzsR5COrFGI/1gCC33V5uMNJBmtLK5vjIUKLJooLlLKaWcrleLxWKRMsym3X6/3ZkD8gdszWy7A5iYktOuWqoBM5JKJRmgEgu4dPeiQGQuHG7Sr7iKJJhXhmgcOPNknQYv5qJShVK7pDLyYwgztfViqnmzepe+R5bXTaaUaog/ggvJ895d/w8AqpMDAUzLcrn0JhFWRv+w3dqLT19Zmbbb7c3NzX6/L6Xsxv12N7662o2GsXCEKcRkxdU5JU0uRRQlqophpJmllEKQ1eGIANJc+RH85o5bdp5M8xR7lthBKHGkRplrZDVmJFfMW6XIFrEJl+bAB7OaRL29LRf3VEVZ87UIaqqRHX9HQ6k8WF2CHEn8AI5JeHO1Xk6iqjm8CY/Iu0ZretuLa6r6rGpNneYOaHFGZpBpNq0kiwiTZCILkxWPZkYFsCnAnKQy4Sy2e9mJMOPjBjoVVPUe2RmRTNnvdqRRUhYRiKoWUzMbloucck6LLMkW6/V62u/3o457TKrFSEESd7lN3MrxWTVAKNoEp7UoKOu683CicSgZSXbK8ADAPUN74djX2S4NT16oJoGeCi8xVpWq4XAArX9vu3t9Hr+v/woIfHBzAkNJtt86DMBghJVS1uv1apF3u93VzW673U773asXnz379OP1ek2ky+uri4uLUkperPJyhdV5KdhNNpopM9MCeciUcXtdcXMkmQQCGkqmgzxVgBRq0FybmOe4aspBqnJKJBn1MQDCKIj1F0BEUMraqoiV/HJIRFY+nK0+aVCVbgRppneWGqV7uDP37coDEoGYeMIKIbGecLAdQvQkeubC42ZufwoiqIgqcUmDB7Ej1VX9Xm0OvYVt5r0NRWefsBZ31oQlfYFMikRQeSCfPH3MMrnTNk1TKV7G4hgQccC/d9hHA743U6pbmFsqiMcVHaAiWYlhGMS8DxdJy1mMA8nN9c0e3Oe8Wq3Wi6VkmSaUsWisPJGTuyveVFyEVoBYpc5gybtCeFil8lKK8Il3d3WkWAI1dShNmdPBbq+HGZkEzXR3cvBZSGKeGWOqohaImHSlmb5kWaSaNeGOGuayKa1lwanR2AwmdaQjWczUsLm5GRZLUK5udq8ubwBs9iVtdpPlzdVuvx/3ZZLFesh5Kna9HSlWKIVk9kRxLoqdjavFIgdhexd9gxUUJomCI69sTB4GMM+Xdxm/qqoBY8tYErWZhb9hZFE1fHKg2gKRSH30f/h/ovElIgMGIOfcZmY25wWg9pXOjeZ8gY6+NjnkQcevkbKUFiRzG9NaKpM0K6WqkZn/W/PWOnOdz9Bu57cIv85T/3r7zGRKHARmYAeWUn1l+Mlny3S6zGcnpyerFU02m8311Waz2ezGPSRjSCacwCKBFk13SSgnIDl4kuhEHmvNG1DTJ1LLQ60tK9VFw0vfzy/0iQ9cpezgvDCu+l7RDQoPIEflUfJoZHswCex/TXEySt1jBrtGcs5dmRBf0yHgozH11dUOuozBRyqlMI4bBdWn6mnGy8iSW16I3pxFixkx1WraSUtt0CTFtEw6jmOxIB5DmQkm4hl1NWiJYhnvfg+dc9ExLwaw9QFoUP5439osulKmuGxVT0sA6D1YQL3kop8v3zzv6p9zqm1VJboqhDJJnRHPWnNYI4nGQyaUkJQ1Cl+NMQDS5S6SOywko82kIFyO1g6kOXIz0YRh0loECA9ZfV5qW+bEgwSE/JAD6WtN13773f5uM5u0adyNLGUYSkrLNJwshuGUp8vF9XZXVHdmeys+uCYUWmoNNA83O6Cz2e/qmTACuhRESzzVqYQhjCi4HGQ4LKGKsbL54o3Dvat58QEnIYGPdc0D9zYcPt44oRIxAIsEV9c8yuNkLTBDKwNT2GeV7Lo9OjvURYUODg6hQ3OikBNzULoNnvqqp8U0GljDinoFNCaaQkBxIMhkMpElQTW5pWAGL88kGb1Y3b6Lvuoe7nb0eh+MBWuTO08/VA9wTp9kX0+hehaMGJhP2lzVRcKBIYBZwODdD2Sz0duWhzRrMDqfmPtsXfONuQxCQSj1oGcJ5pQ0O5yEE1ZdiEPrfQNyNC8i05Wi+EPPjmgH3T5cx2+mFXaLSPTEn3oOmx81sQ+Hdls/E/NBQKdp0rId9znnlPJyuVyvTkkubzbX241ubsZpCohaFo+eHbTNrpsdiq32PDVSpPX5oyRvvVyqqsqkOmn0IHDNFPVK7TrOgxZFQ079AQEFYEVaaCPEjc+Lz/s87G1G3JMh56oFsuZ4nWSbn0akIRrM+RGLsEp9qu54k9etV617PooWFfBEtndgs3gUVVWrrVyiMZAv9VX7U5gVBWpfqdpgMNlsH0ob1qrdOx/YkSuYjWcxgCqtExet5iEYoA6GS88DNR61mvXFe2s8Avguiqti6JgwQslIhCYkx38kmERGwZPo3ukr1aS0HAAIOyOn11FSQWq1yW8wzHzzWrXrZZ0ejEGqdVlBBk6Y4d9IR8QOtmIFjjfouXt6Iol2AEkX1O4mtNv/ebT96D/Q0iKBLKa7aUwp55yHIeWch6kMZUr7lDUlSRhSIWBFLNqiHIHis8jREfeCqpZvMsCjGmG3D2lQTU5pB2ue1nZjNfTv822NrbzGJYgh8q5OA9G+0iPIrA52dK5BZ0u0UBmQaFEvF8qwOWrePlkZy+M0fEwUWTTabZey0qhTXJyiGuckTGkdByKqb7WvoPbgvpFqBUZVC5gdzAQ6rygSQXQnlTnpcigKnWVa8LZmNTPp70VGNSDE1zhr7Rw7Dqx4qeZozCoq4WBdyqZXJM2KJ6COjEJyCiBR+BxP6Xiw1hlR6AuzzLepKeC4TqMmXwYpdTIbNcLrkRJpTxboJ0hAwGd4QBu4kEyHTC4MEoion4+PAvHVAdVSFRbtzeclZuo+4fiI967MeWHQ0VT3O1WdpnG/2OecS7FSRtLyIEgJImJaNKKOES7q9oLjI9HlpbbUbuMEQKhl3HkeasiCcIxMdY70oBkoILxnVWie2UaF54hneEOiRetjZyf/bdN46MyNPixJs1ap3JlRFG8WJAZTdOouxEqVMnGchuogdShuWvXMK6C9tsJTOhPWtz1Q/lYZ0rvMCLwjaCoW9rZUU9NJuA7IHOCtsRM/s+kGqeGiUPEtqSSelgrh5ZKPYaD5O4aN3V7cPE1rB/1OK+O1Amsy5/DrPTPORAhEzKFDJL2HVu3o4ZhAMqQpBDAvD69dpXopQM/VW6zUEYUbFXxEs5bGjSFKRLQ1wsE25/Q75hTQqBSveCDgrxrrCaQkdSlQg9GgvhJGrlaqHO5560hk8sWbS2opxUrZ7/fb7U5Ecs6lFACDJHMmUxGWGnWMDs1tnyVbLG6jNFEUiouz+rqiMJGwDmwcd0mEw0AvQTSvWNPo+KgM+yV6yLeyHva9FQLQ48m/iK94bjZVkvDiKzCWsZTqonvZW4uqR86fc4jIEgSiLIYZo5+aJVYJsUqMmBp4AKN14jAz13mleChFzHRmQsDmahPrycIb7Qgcj9laR1nfM7kORuOZOY3pX3XP5uGguo5IDVtydoPDF2BVPCYOxHOhq15yJQalinf/NihNGEJvni9v4xiMLRDLXuQvkAQTMFc0XA1bu1kdxKSC7BxvYcTWdK2vVBPRtgNAs2crXZTC78XqOnrupZOsbp906bs64sdMyHjJRKoJoy2u92kyAkyJtDpDoJmXD7OB5462TorPm5El1JaIwBQK25cxlYhGuugkTM0yaMkLmkTMp2TeD0KlCFkAMZZ6TpqJlS6d/LM4oWoBw+NOwsQc4D6ymqAgoxCxcmFvLmorlepeMJbd8/P7BkTebJIt5OCsEy4iBLVuECr0BIxnZ8zV5bw87Zwujc9sWaUaBidZO3fGBsBiET9XiAJAoaVZpKSFeeoLzlpdR4bVoupfE6Slyj9m6nHNBuGoTQAbBVoCJdqrdrw6K0nnBET+y5EndHWVHF1Yx6dOpCHBSz0ZBOVDBPMWKgmm0LyIzjQmIgkRG/YFrODRagoJDaUXWT5Uz7Jafl5AFlZ+qvUEpBT3eejH0RxT90AkbGipPqGPhie3NHwY0Atsm9/iJgE8cNeweECDIIBYBPODjs5neBreJ/OW4amC2yaqqKCgOBgwe+LUkCwCBiKShRpV1ZbmgDURK4/Oe7f7zSw5aiz2xnn9i4o1MYBMeYkadzCICLJkEVFVgGxkKN7jQGmi1EQgMUFMLAGkpwTMU9BsKZlkKdw2EViIzkDY+lIAdZK9nXMLZprDdYUe6/NMQyXlym+dLVR5r+rG2Sh1xvPsq2pJaV4gxZpTSHhgxllUfUEV7zMotTYqRi0kvfszjDpDYaRYoOZKBCG8vFcSdZAUJcjiNcbmgtVptjJnMqk1HIYZQ01EWNDN5tZ4pu6lqU54mazbrppAQml0+Epe5BTcYy00au4ihp1TJ4SkRbcS5VxW5klaH/5bx6HmHVjnbFXgSKrOdF/OicNFRZXwADwQF0o8lWlP+sJ61HEvkheLYbvbWZhkamaehCB8lUKlCYO8wi43894tbiCYkxWSJEhdF3HeFwEx1PoECwmnIFu7hwZ/JUnvVtlJ4t5z1vlgHBcgqU0t7wogumfWBU4ovl5fMhTPrC4Xy0qgYdxCaxFDlAu7hK/zOKTqA8cayD6+WcSgCBPPRbhrCjGvBkjVOlAioqlay21R01XRdNjCrfASw6hXaO/eWMtfzGxy8aLFs3VDSslzuqHx6upGEQhlXTmDZhZgZPW1H6CVFS3FXuY3BcyKFvNoJz3pUo0u1zVuqFd50cqFI+QXuqtaWVDlDJ62eR+BIwJz8D3mnSyd3djw3T0GOy9TRBSZfC12TREprUtbi3Fe5rrml4CKDPQOIhKF6CSiXjsRXnceMx+B7IbOhAW5BMYjkkUpksWea3bTWRNZyp5WCGXxKysxwWy5yIgqIdcS7slEdC6K2KP626kAENchYmYiDoGAB9CoB/uwvdxTcjVu0d9CPKQRhre78kyIZbNurzScKQdrDNfjYjQRlBCePjQCUVNfOqQGLUPJR14RKKC4KBEoJBqNGkz8MdwsD0MRYF3aIgZEEmtQ1kuxwpQvpslfulYSGlSgNZ2qFQLBQBT60q/Gao5KFLtUIuuzmjXuUn08RJzGvA9Ks0X7bqUy83H9xMr8bqYRFbAlzdTqO8KKiRfHSfMSXRJG9MVqUIBkq4Fx1VrFKxhudEje+fFa14+KBGqa34+nRFExM6lRmYYenplwkQWxmoQl73kX0yHB/+G9e32CdF2ZZ1cNQHMz0NUikpxMPagTpStOZF1e8XDTZrYh4jEun5CEmgSIrvKlGMmUYJ0sSlJjwareU7l/VU9pOJlmilKZpaBkuvEm7t32+0Qtkp1spZkZXvxh/lIzuqCKXvdXQ4BpZbNMahyPSKybQRZdecIzdBpJ4Yo3wQz3MwFEX1D1lnBQoCZTKr1KuPs1bdPoFo2r4OWnqZros5funGepaoD4lS+7XoVpNYx8TtFovqpTMJqGOZHWEIt3NrCaTA+GjEFzU8PmZd9RH6Z1W2blCv/yAHd+9CE8NzIyB6YtGCnh8VbuBU1Moi1Xj7ybPzcviaRH43ymupHVVNcaOk5RSEIjy+AXc5HTKDMvUuQDE4wmyevE+wyb41CkSlBEgRqgoCRaGJMhWsPTSDC4/jxQ9xRkABCv/q4PDdQXEBGithavMCiHCIsbeu5Y1hVLShIUFLq2RoI/j5QkaDlDf5dakwTvhiwuIc3tv/B4weN9wNbbGIgC7jGgleMcIHgQy3L5skq+Nw+yefqbXvgQYTMI1JOxdPaLaEGaQ9gh1MyiB5w7tm4kGzI9Kj0HCSJmWD0+q9WF82qQYr7wY1XqEc80x9xVx9ufbg679tHR2tzJrQxpV/YBaSTYu3kWLQ+CCeGQ4YpnNrO2Fpv32sKtrb9mDzSPOTCPOh7IxJpOEPHCyhAfyLVCogYy5hw60PJqUt/ap5mA9xm17tah/TrtVt3AQPm6gzoLFzH3jWYFlDNdQJLwtYuNOmOHSKrnCR24RoReqDAsWg2NeBbDrbW6cFNCra7tZLw/tFkH5JqFAkW0PS4tZjoBpiXVCfAgeSmqOuZh6ekEMqWU6eguPcgZVnQ0wlv21kZu9hBs3HnXFjG90CQNdeCjaXUC0ORf874iSCCsHVzqopKMDKoAYXbEcu1wceMEP2M+K8rOaS8A9wYPgtfrqwCK5DlQD9iIxxeL9T5nvBRQE1wtpgoSNORa9okaaqxyHXP3fgnDL1Tq4WaNzQBfzc7gWJeeLdvSmRI4XTN0a8EjrMBg13rI+1O5hkF7yJnAOtoOcFi1okUqiMq7s1YToz19CiPBx6drgtyWUrUwX6uU8aFL7fpNK3ZamcJaKR5vadWwmZFbOWV6724xEaJqhtSiQEKhUAMcJs0yDvBOkKAAFZGECHb6I/qSWh2CiR4pqQq6mtTxPAqaNgPLXF2yxk4jITwMi5yTTbofd+rFP6oikZ6haVEdUgZY+4mhmmIxXdIv/eX9Gu8qSqZh6JccrvVQQPLF5wDUiJnUhqKSZrIIcy5VXBmllmDFrKgRrf05Z36ooSxv0NY0oVXQVwgeAGCKRh5yIJXp4UFLB2sm+0uh6ZOOXsSdEBGgzk5Dh7C6lCS9NqfeAh7GYDSlbqMaS0NXxmtxi9ii2UWyamGaeUlHAViRy6DAGkkjOoj39a5A08ZOiK6O3CLrjXlmpx5E/Ttr6SPJmp5ttow5iQCgzXa1n1yZSPtxbqi4BlpqP+l7ssQhNTiAvh7IiUElEX+v4iFXYHQIrvqDZCGDqrnScb31x0MPGWfKiFiwL9NZ0aTxSk4Z9LCdNTOSdYmsJDllZuaUuFqdrFYLK9jud5eXl2ma3FL3RHApCrU8zLTXRI427SIEIoIXCPY7eBBikiW5KvO3jAAEalgxOm7MMbE+xdR/aNfvp8SQTLzXakBG2XJ6VgBEFpAGOBBEIqDgU8t5bYkepFIPkaClzsywfjTYCKLpOlJySoA1o5G1F1PIXHiMh5F+jrZo5uLf9R5oWkwPzMnZz58fhrQaWJ7HIz7HqxnN162Oh+w0YTBJB9H0QajVndKwcg6b9iiup+Pr4nY+zrOV3i9fUxV8dNpvx0sQeXikruzq+GOegurJe8i5X5GS0R141sN5YB0kBrrK5ylVCWpmtR8eAHfpKqd1HJ+CZOYR8Sx3QZ9BEsBVoxNxaylO8W5KtOSGmFkLJvn6cqaT2CC0LHk55PVyRfOKW47jOI6jT4yZTTKNspsLsY5pwQEfAphJ8qyjSKwjhxpKjYAqkCUwaDHMrOZ4J3FS5xr1EqqXiLcA3P0ztVR7dNYhtJQ6x3FxF/KeXGqX0GoatVIm1j6jYQL3w9BCCTx8tva0JDPpK9QYCiCgdnpDAWc9O7xqdQsNsGgWqXN7XDZJroyb19GwSPCpOw3hRQg0UlgVllWf0OO+bWwP4JP+ai1cQxHXe7RqmwX6vE9Kp9r0JbA7qKIq1Y7yhiLWjaLWXH9wMqpJLAe8578wGIpzfm15AZEaf6r3yiuZexk0AqKF8eNb7l41uuLWxEM7bjV65pvHZBXo17VjtI0hPY/Erkg0RLuoFZIHtdgxg8CkCt2PU9ntd9c3Jycny2Fx9uDRfr/fbDa73a6UMaW0OD3Nw/n1ZqOqapOZzbV2FExtyozh/5Aw1gDV0V7SLAUT0OKNt3rthPpJlJ5Gm/iXdJdUgDHNv415gxEmVYpUcRcnSCldPBbtQ+oWpu7zfgJtz8z6SKzVLTITE0nHLXruxBVTsyE86FFr2ytYIzFNpRQmVJuzVADMYli1g/FgNGGaplJzH6CpJ/ogKKXUNUxrBkwApgKL+EgoWxDFKpbVG1Wyg0mIpeYre4YZVb4cUFqjZ+lVNjGbu+1AZ0UDQ+oWSonuKu7dy10JDCCSQ6WWdMSmXV1o7gDWNdnfBuKWRUuba/aOhL3UXovteISkYX0lvoTjGItONJHMFDaXTbWjdX37UM61F477+WWc9tsdJ+UAKyWBiyRKL19XK5pMSRVQUQwR7Q0DuqJsmjdbc3G8ta/UeGsjjw/E/44rie/kvbbZrTlDDbgdDG9QjGll2uOvyHZkVmuJBrU2+uz+L4V2PfgnZr6cEaMw3hlWwzE1liq/4kHNJnNAlFsr2sI37iD0TOiMZDgckArSELE6qNrJBROp+dq2tgdoXvhKuqCp5qUrxiDi9lLu1LQEZrSOggIU1iqCqC2JtIwRgootcJ1Uabjnl244ebQcavskratdVZvVEOiYUGbwu1XzMjyTeYJ7ZrOD9OB8vEfXV1Y0IvJLnXESKHUSSNLZWh7xA9CKl+/ofeoNgqFWxnGnOu3LNFXHxlLNoRVzhIQDSwSEotRQh9Sg0cEr3BmYMUKS3Bkuv5szvRSzWh39UKjeeZE7Zed8/Wp4NtqiBHzv9vj3mrC5K9YCSGhSx7oTDm1RunXjZOXXqekvOh4IxcxiMs3ce5yjWj7nYsKKAkWEoBGCbZ7x+vJxOwnrGh7iauVtkFgiximnBagD++JY8xooquQyAyEbHE9m1dYkPAHLsXw32nq6NZp9fETaDDTPYsZptAHT7rO/Y0ds87gduHI5S/Tz6Oe1KcCj+r24/CxLZqC2T4aIr/FUTTvDkJKZt3dTQGgKIircqrvYpUoxpOxAuR5zTMM0TVJDKA5uUlWo7cYS9JbABqqyECgGpOzUmQGXM4lkD0B300ULjvqReq2cJDg8d5aUBlcPR0fcW0x2SNaVNm6Zr0Eux2HDW1s/L4RmegKEM4A+9uyfnFJhFglV27MPzPRMON/Cv6gntJY5RpYAlJl/mB/PoX+oiZnwRB3dCqoVWkvSWKyy7LIgql6AlhKIGG83cmZMdNxmaIuA5vm3tJkVSdYmYOy0HzoIC5vL1f5lF5u35tcDAcTBPl65lSbWkAwP5VC/9fN+JCXb55w94n7YZ5IV0dIJ3aonQ1T2wtiHUQFjhObcdzdDpM8j8FRzg4RJ1EdaFShGei1rrePoiEMMViyJu5Si2mUU6o1UkX28pG9cTBfkZsXzhJJqHoWBAXJbtHh7NqLfR7EqlRGtn/cRKDw8Dg89V00RXRZuDfrBJPFuTds0Z+d4O3M04JhFnBQabeJgVj/HEfNVrMPWOJjlblnFqAiNaok58NPLdsdqqnoIFN54TgysFp2ABMSq/8pWWgHWckpQ0GIQs+8NAVOSmjcU1LyRj6WzjlfBOnE2zVYLrwLhQEhy8EJrjcLEOSvkjCeoyT+x1sD/jtm8cz/b9jP9x919Pn2OZvq7Z977LSdKbaljzTVKnsXxWwv66kEAgENNDsphM7NSHW8u5nAticXTEXNMkWo5dC6oNNkc/XljqAKuGf2OcpKUJEVLZ1pnkpmVGlKCiCSKCKZpcvB3dHmwRAcs+5oA8YyhRer7+FMf7KsgOw7Y2K0jvk8UJGX3n18rM91xdap4qcutCo5M6QqOxfeAe2ze90s8TiF0cIRrFj8S+4pDCDJsSo/mgFG2tplW7QvporVWfRfzOhENB8+hOlXjiJfVRnFt5MtN3bEE2BXCkpYOAlSzmpCaW65ajhXITtdpuSUVwqZMjHYLVXOIAZpEUGti2iqIRu07JkSjLyNqs5Db5qhr8tvHzay2ekHHe4fMdg/jNRPpmAmzUKlplpd+ZZUU7SFq/tHEmsxmgKO8nss8+uvVEqkWrcZyTN7GvT4Do0pGfPm1arYZWVvR+EtKJLXhuQwnPCEGxzVYUFxDLbTAD2mCQjALaFYLxkNfivuHhMAUlnxJDheLAo1yy4O9A81hx3sPEx4dl1gN1fxpSR8fmc2b23tEUfLx/hDwHSQVy9vrbP4JD0kn/BkvcaozrhUdGuSRhKWogxRiRTf1thKsVrxHUyzYr656iTBXA1CSvOrFYAoHznlgnWLODLX4OGKVAFKylscJQgJQV4yzipFijXx63wRxVVGZUIAo4eqYsD6dkv4qkXjzzwQCJOc2GcwZKtXu+jzcR5+oqgza3pH29X610YG3QAZu+4TuGzf2my2angnFMXxh2JggSCpF2T+BKOzvjGJ0EOSw+DODIApMIrlIQxEM7qUkwqO0Xp3jVEySprP1SNTV1NXrG+eXgZGag1M15BFYq078UiHRFSpMQs+VeomHeWmhQiVyqKBNiaQnYAN5XI73pubJr6N9RBqs34OAFgqE5oYmhAIx0kqLsx3srZSaLz3Yp+rVWVuNwX1OuHUSws9FYWNdeMQv4NuNA61xoBsgQWRwaL8TsglsQhGKQivBqplB3XKdsSMZrH67STT1Uw2XJPpD5CRGgZo54qeOBhRdtlNRnRGBQTRVm4hUQSLMYwNZ5lVWvTdDFPe7TAkkE2qMyKqXaEDtJuz1PP3el4KdSe9gb7eO+Nb04Qy2vDtu15hwhhPMmryWesV4DrllBwWI5Vb85/R/FY0Wr+cObl3Y3X1WIWnqCEGhCcyX5zZK5ENoFb3u65vbOE0pO9zTGxlFiVMxS2ZCcY8xbmwW0eNS6M6ctHpEs2bAMJFCZkBFMq2Y0lDC2ZZkgSoNcURve0T3FSMNf7CPpNkdRcB37lG9SIn//PfQUCkamHG0yjeTfEcdY2cCmQnEat0jsFoup2mvoypKlsGJuJjqqDLIIi9cQI46oqihnKyHUiyltFgsSO73eyuakgxMvprdNE1QW+TMnM3ser8/PTsD5OXLl+M4rk7Wi7TY7yYzQ4L42qyqairMSVjG3XI5MKfNdivC1Xol5G6/H7KoGX1lV+d5z3vAxZPUQEsUMTcxGh/EEhqKFTUJEf2dzSCcM+jRAscgMEkdZ3XRSAREtX3HmdsqA7T9vRwFzEUdVQciDLo+MDPrutRc2dj7vQ4s1jwwpHI1rN0n8B9EyYcTrKnLYLerfQW52BvR9kfHxfcxzpBkycNfWVIcL6ogNSFRsJTEWezFJJnZYrFgix9gxv/uS2mc2vcalOyWai3HR6Qxi6kGwuPAl2vx3sO9uboSmP4Kew+WkOYxEgn0CUkzCRMUAtb+rjXy5p/v3rtDkKSVZU8ClcQh5cTEBCsQU0kLiEG1TBOUKSEvhpQGLTuWCVbUhz1yqlmEZkVVk6lqmXZ7p45BZLu5BGS5kNVypdBpP5VpTGkg4GmKBFAkJQ4iOixyAgpy4nqxfvTk0YPTByb23rvvFRYx8X39r8HZWzLQha/HM4z98r1uWpkBGqjrxi6UyO9V5ppT3H/hTVv+C4eK6/ap7AOSmDmwcdedW/8Lq9nH/oQ8SAQJAuXvFvxMmhREMxsjAY3eXV1lQPNP+s9tuFER/WQ0CHJXMOXkSyvWYAAFSIIETVSG84NqRBunrcwCc16beshLzCGE+fXcIDa14wRmfC9H+zuxo3Xk6X7vL93XOJp55X6vxyTJnfWEd4eDDveJsQdV4I2ro+280ddE05xyQJxquCsnpmTTOJ2eLB8+fHhyclJKuby8vLm6nnY3TKmUMgzDydlJpux2O1WVIcuweHF5td1uh2HIadhNo+mU3MA2pweQSJJySjnx29/+hhm2N9Ory5c62SKJTmOxKXmGxNcVsDCqA0bWYnJEyuLBoWjbQdY+tAbPaGljV9SIPQC05ZxRo7sIc1TvTOHeu5l5l97ZIT72M4/IgR1wys/xAG/IkaPjXVvcmqLo9wA8WS8hZRuCpDVNE3rjULfOajAye+0W1UttYZKoZOqPMHqHeUwAlRE8FhoR85rK81ZZyJKHZOsssdZsv7oNsN/uUNs/S40cmNluLOZ+azM4IEYMw2BgcSejrk5lRFJnhNt5vzsyRfWVvYvWwT6BSsd2z/uDmr2ap0WtTnTg/NzZDVG9cmcdY5yPg/NplhPNkplBoZjCkjeYlSQpLRKQHThWymilLDPOT1avP3n08GxQ4OJkdblabLfb7c2mgGcnq9dff7oaFjc3N9M0yZDz+nSxXnz27NPr7aaMe5LrIUNYJlUScNeWAhNTqHz0wfOUaWZlmkop0/X+slyM4yhJvC7CLZGKeEZtz2sARJh9BVmRabLWRBhoxXQuoKNbTP3mIFAEx7cEgzT+uzt2fWt/L7/dk9dtNHa8xUUOvj3QqL16OPptThJ+ETqVWhHJYLUkWy6yZjv92/D3quVY1XVEvOJBSCR03VcrGioKpQUEJXGRUk765NFpsjJD4HwJLgA499taXTyslKIKMAPw4IC3LlGjY+XUyDgCNyz9qQhEYqDbA6owOdzDrBaP+jjMe6k6od//ils7s47mfZ59DX+3H4qQEFMPTQFIInkYop22RIlWKZhMPTu0HIZBdNpdb9JJSmm9kPWTh6rnz5492+/3q8yBmpMtBmZKXuXhhHn5ZKC9/+EHN9eb1epkGNJYShqyL4TTwCmE0uzjTz5c5jwMQ6PaaZrKNOXlbKEwyICsOs6FqIeF/F8SVA7ygm8H9IOMWtYato0Xp69Q00OrIQKPDTkY41fZK2q9FjB/AGYxcDx3t3RgdQ571urzhJHsCS07l9HOmy8BeOCVzk2HIlcTX3nLAPdM64/YKQsesj7r1aSqtfATw8oHKK7ZkpjXJVqi6X5P7K3eOjoHgavVSkSSxILgNTxALVJMSimTllLKVGzUorDdWBRGTZOpqbXkMmB37z18FzKhVetxxgwd7u+TlNWkuVvs3fI3tIvD3Xs1zs+gMoeVSXKxWPjS7QC8edl+v49OcDkv82qdxuUgyUoZtzZSVXe73W6znXbbab+/3m8zdL1eRwMi0+vL68Xp+vzs5Pz0xIouFpnU/X5crpKqmNIbLXmvXlV97fFj1DSjZxpTitXR++c/2sysVXiYmeokwQUA2JpiCWxei7zaOwdXPhg6IubFbZBfcd/A/Tz68L/IdicBHN0lD0dYmdlrAmsH4/jTwsKv2N8D+jgssuy0cLUtayKomMEsCSCxuKR3eVJTK6rX06VYQQ2k+IwKuNtucs7LPAzD4AcBCPjZp8+LiZlNWibToiimxWw4OQkIiDtRIY4hZnKv5rljY1dN0m9yj+MR2RcCgUFBNb3uHJ8E8n5NeNf5ZfJemvV2IAytfEiLx7RFJCfJgpPlYrXIQxbTombb7fb58+evPnuxWCxEZLFYTONOF1mYp3Hc7m5e7fbD9SohLRf57HSpClMsFzm5IE1ecphIcWPk+vradPIpTrHlnPN+v7fqd/Rbe60KSTXPWkhdEwWcB9xIJBgrJt7i4JwhcIss9KG5/tK/ABdZ0xzV0mN1Oe+YX/H7t296yojzb01lZ2M3kXHE5zlRTFRsXud1ZsJo7VhvF6UV1hL1HuuL55PuZv09vLe3eOtuNaNjDxNcpBJgrLpTdELRQbzNgccFQRqSJWgpe9VxKrLfE74+VtGC/Ri11+ajJCRSYnUZqyWQUNFNfSf5fqzumTkrv5xjD4C/DLRkgSXEXoksR4ErdLXCdyvV/qmqWaHeCzuBBYai0zRRbZomHSckyYw4R4HZVPal5IcPik0Xlzf7ae+4KENCHsaC09Xq9PzB+mS9XKxUy9XlxaevXqTlarrZDDKkxZDzYr+bSJycnOx3E2kiKS9STguS00QSOZ8aioBeB1NMoTZpkejcgdrzJgpbzWwuKWdttm2WWtyh4iLdnaZ0FEUxs1q0VV35g00pklD7Hv3yvdV1LNuA13m/E3BvQLQb7w/xc0JBrOUoR6bBwTn/+T//05rcPRBX9Lb28QPlXDyunulqkS4XASHpo+PfrFQHzyGLqWrYhEIRKaVEGwg1Khy7kDIMO9b6y3hW86jgbCq7Xe7dLAzSlpgPcLYHTqMvpc9MfTDgl6rBI7rvhV0/cn3XlsNwTl1E1XV8J7Du3LfCpWbHHmnOo03U6wZaQ6f4rFNpdwyAnppBTfcpcZCBmWKiOpViqtPJyVmA0KFmNCsertgXDcxTh1xT2DAs/QHsgIwq4PMWvGuQdLvXDqiiqG047hnnI1aR4/nqMlX3Cq9Z93RgzLv3h5Zg+3A7fHLPNiNgb1+HBpa5tK3nr/76OaWanHBdHHAnBzV4zNQjsI7TN0S3vXCUCcBXUfAoFVGbP9A/ahmpShLiKAqCWnRM2ZdopBHDkAWc9mVzs12fLeu6crl3lNu7tmSgrxms3o+GHSTEX6QbQ3FC+mXbbXvdZdjn/cQOPtDUSwgigSVVirBy3DFEHDig6V/ikLCZKoyiS1bPJgJiFXJFQ8QeZGHgXsHRKS+BQMo7D14BlZRzOJuSOefWzWdRDgfBiVoAhWahAp7i6vcAbkcjaZhbmLbCHXMjsNmgnTQPU/O+ubvjuMQYzh5TzUDcfRFr9DOnAK3b/yrbvV6ls1yLkgfGy0CyN3dzrktExo/FE/dWqq2UussBFu2Au6UVzbwlexTQeLrH5XFyOe1CPUGy94DBZDrt9ufn58thtd1u95udgIvFsD55UGwPqSD6jiKt+WZsvSZEADNvCm6zXCABu7uQ/ZdsNRo52/HGW9eZS7rq/3WnzCuW1ivcbYH0t+xPaMncOzUh3UIhIujG+hxs8LNwRBGuKYVJbfJFH1g9NxGZdDy4MltMm2hm8vymZFs/GLWAyCBEYroTEwtVqXAFAiGc4XWejWq7cb5zdCD3Mc+9lsLRS/2y8VdUExidXGgP92+1VenK9pzVnOb8zh1hZRFn0qgoQSXyeY3QBltEW3hViNSMHzdqwkAKRG6FfZvmnBssuxR13J2InJ2d7Xa7i5eXKPrg7MH5+fm427+6eHl6Nri4IIqDQqNjVw9e9TcFALSc/i8d7l9p+G4LszuG+Ph499mh1bMJ1Iyi+/bCSAF6tYqvwGHN7jg0XglfbsE6yd3uhb6aoV0/Sslvbf1rHsq7u8tw2qcEtv8AOPydKEd70M3yiSRR4PuQFccPcP9m90WP7/shD7/9pcRQG5z8u1LO0e2CA1EXqvpcEvWG13GodpgqgLdnJDB3uPCLt7xZCy0Ie8QMos5Fwt62cZIEElMpqlbMcs5DHnabfc7D4werUopO5eLlq0SeLBfJV59GLUurtrPekogaaDT/y+bXuFW5/BfZZptkHsfDrb/wLc9Q4IUUMQKsOKS7PUKtxanVgZjXrgmb6tY+Gub6K/sAwTzc5As3NO/OWyWZA7lTIEVdoBbnVAZDWEe7BjQ+7HV+XXsDhyIAXrUkrbir7pObqYxq1VazKl1joTqenzNTHiq9Qxne96PqIBzP452bzYbMHJ75d2PFe+/LjlCPUis5mo3Go0cPEQ9donZ0phm6RanDzCVZm6y5z5Bq7MS5BzDCl5AjSWYmI4QpJabh8vIS4HK1ODk5GXf73WabUzo9Xe1uXpIqAE2rawW4Vq0j5tQXy7J5i+NbcK9kvwwMdtc+RaRY/EgEhQ+Zh7WCoqsN59xg2ANhLqk8jVIDfbf3CRQJk9fPNmv7+2MJ8765Xs38jEiK8zbrkqzC7MVEhgpqN7ZuYn48PneRqKbH4UiAiMDNollpgDpG6mgvzIkKkywGY2K07PMONj3kEfVJ7tzPbPKrbb0m/KXs1NvBv7ry/LwL3rrv7avdPpIHX03L4Il4iVKaaLwjISxn689biTeQNqobU/POoIOmw5CwPAyTFjNNKS/yQmHjvuw3N/v9+Pv/6g+fffzxN7/5zd/5q3/ttTfevL68ePHixelKJApSD6Sd1baLRiiCEaQ+Akl2e5gmSUKLJUR/hb0nYAjQ22CjwoBu6SKpbFlVyKy8jGZRUtRp5vvn1ViXn6uxBLA6dfds0z2BIgL0xo0hzpsDYqR5XzCQMKUIzFKW8IZQDQ5/bHEj2a0AAl6dwCzxubb9DDkIqDXy6PatpYsTTzcC7WVrAR3vAJF02+cEZu7abB7Mzr+7XxnO49myCL/kJ/dd5/Z93e5uYYKKcD6OfmdHPBFqNdjvQ+OLG1Z33UspYrAJwKpg9AYWHWV4JJqgmCohCTqO+7HIYMKshouLyxevLv/Fv/iXP/zhD28ur5598vzy1dXbb7/59ptvvfP2G9PmZUWb+vJ9bDlfB5f58uOKlm1SgzeBn/egQ8+Oj3/enqLQRC9M8jSMqfcC6/YSaSIf2FBErN/6MpFGrcXNbCW595Y+WV/9HTXginLfrxD3PUZCpjSoTnMckuZwo4h5ajEhdYLQk42DJGWimlKopszUWDbFhY0byN60IvJFhsT5q3h3ypz96fbV1G30XKWMB2+DXrWC2GaVfrRnpwGOiP5OZjjSRb8iL/27q8H77juL1/svnndjOT9dJ8Hu5tpUV6thEI7TjoY05HFfXrx48dVvfPPly5fTVHJO+/305S9/+dNPP9tsrt987fWbm5v9bvP06dPr6+uHDx9++uknZycnpG02m9OTk9243262kuTkZHn64OGLzy4N/J//p9//gz/+o6urm2/92q/tttv33nvv2bPnw5CWi8Vbbzz9wmuP/sk//genJ+vr68vVIn/xnbefPftkmqaUkvcRMhjMWp8MVnTicVseU9fqiVEYKQ42MIOpx8r9W1/ySSDV2AzUv1/HENI6ZDZaHb02QnGoLStcoYK8W3GUKXxFxK77V92bFXohrEcSAe8T4yEuwo73pah49TpipXjnfp1AE2HKOcxmCLyqH74m1oFZHYsr1Q4AxjjHhKiLLjqnuRfH2p1dQPESWTMlde4tMm/3EZwRqk1+GWovIqAtB+KJ0oKAl2oehv6CVrOXfd/OA2aoMeJ4gK7N/p3np3uirKbav0v7XO+utX27fM4yCj6q+XMMG3/mf/ZHv9huN9BpyDI4mnna7XebnPP52cP16cnFxdXV5kYNy9VJKfbg7PGnH3/6e7/3e/tx97f+5r//+utPry4vAR3H8fLyMoFPX3t88fLVxcXLr3zlK1/84heH5eLi6ubswcM//8GPfvyTd3//X//xd/7N9wz82te+cXp6+uLFi6uLy5OT1Tjtl3n4+te+dLYYLl89/9Y3v/6/+Y/+w4uXn203l6erVc6ZYuZlKhSjgCnwOzrd5zI1AdzHKmvblPmcKJk8PD5HF32tiUNv0DnFvw0AfIRhmmV+rKk+b59Ab8ZrojaZUm26z0ca1dyHbb18Eg661/THG1HeDgy1LntHoSLjzH5Od2LOeGBNeTUmBHXUsdlvv5QJAdwLjO5IHB3P1MZfc2tmhxaY3T3ObTXiWBCn66n3b7FvC9Sygx0Yiha4F938bdN7PFumOy2arhFMycvVyWp1ssxpGrebywvDtFqdLPLw4Ozss5cvL66uk+TFsH7jzbd34/T9P//hf/P/+P9897vfff78+d/4G7+z2e3/1R/84Z/84R+ZTt/85jf/8Pf/1enJyZe//EWdyrNnz548+e5v/3t/+Rvf+uaffOc7r73x5v/rv/3vi/L73//xenX2pS9/9bNXF68uN5eXl8s85GE9qRWTolKYXn/7i5++vPzRj3/21puvvf3WO0Pi9dWlWfEVKorFkpJuxJtbyndjUrTqBFTNoKQHIapfV1djjHYch1eoNV3hDfZpA6n6jJEl7ZATdwiEu/cBFYQ3/TLMFirUFB6K6va1N0f82nW715XDq8sPjzdbSIC2JpTvU1vg7mDv8ZzwYkhvZ+axcB8BsGa4vEllTbAc8F6vN25x2z0aaV6X0hVX+8YdWla+1yoYeeeozvAJHnyoZu2tf+XA0jnak9b61gExpGa+qld4vF7Djbr4T79XfxXHlR3uE714zkjwf/9//i+vri7KtM/E608e/nu/9Ze+/M4Xbq6vBkkFNiyWN7v9Zrv/wY9+/L3v/+iHP/jx5ctLmH3961//zd/8S689efrBB+/99Mc/Xi4XH3/40WeffvLtX/v1L37pC08fP/nDP/zX77777m/81m9++tnzP/nuv/ndf/RPfvLTn5+cPvz42UtDnhSTqpeZjuM+Je62N7vd7nS9PFnk3/0Hf//D939u0+7t15/87b/5199568lHH37w2muPfJKMKBSrK3I4OOM2bOq+ImPHVbZzLFbt5X3dtWb359A1STVocSD4qZ/vq9zeAmXefesG2T3NgqHE58Pi+uNwLMtdmtAl/G1N2J628qFfBA0x7/owXpNulX0eMPL4fal3aoycFtGf6PB4iwgcjNjn3KVbrPZX8vQ+Fxt8+7369bzaOfeZo6ixjNt05QsuOdXl//d/+/+9ubl69PDB2Wr4k5urH/ybP/87f+tvfOXLX3znnXd+/vOf/+l3vvdn/+bP3/nyV/7VH/zBs09fPnzw+L0PPv67f+fv/LXf+Z133/3Z9//8x1/7+lfefPtLv/jZT9977/1f+7VvvfbGmz/56bv73XRxdS2Sv/Slr7z9pS8/e/FyvT79+te/+ZN3P9juxmG5kJQvXr147fW3F4vFxx9/uNluzk4frk/s6upivVr+8Ce/eOO1J2+/8fj9n/74J++++9WvvPOtb33r5ctPwdr4CxoCn8yhkY7r/WIf9bzwMmQBYBp9+FsLDpLG1t/taN+QQzjKCvrEeCzRauLHC0Luiualg7ZVsSlRSrR0m68M4B7EjzG0bcvC2dyG+Y7jqIrnLiK4U2z5jxSoTZRqlJjVKIhoZ9VaqV+b4ZdvBp1ua3iYwupaFAfHY2D7vJrFKN19O7mHA+9nkl+e/2iTCxe7h9o8pu/uyyOJ3G4iLIyIFwAC+dGTNyQvfuM3f+PrX/nSd/7gX/3Jd/70008++tY3vn5xcfHZyxc//tG7eb3+8te+9fDRax9/enFyfv7rf+k31qfnpry4uPz4408ePXr0yUcf/9GffufL73zhN3/zt8p+/MEPfrTIyzdef+s3/9Jvffvb3/75B++//ubbavjtv/xXHj55+w/+5M/e/+Dj/WirkzPJS4VsdmWz3Z09fHC2WhfT6934w5/+/MWLFw8f/OUvfvmr4778i3/xL//yb/36yXqgQ03NBFL7NVldlf52kBy8HbM6tF9c3h+4ModXAJDmWoeDae9prrvLvfWBd9Jowt11N59jzhn79dnjnFj/8NZxiUqYOzSheI/QQ+JIgDf1u0+n9Q9WDbO/GD6wa8SU+n1tDXiQ+UQEVo5H45eiq38lHfirnfDv+Ks6lDjeu3wkCeRHj59+8umzF5+9Wvzat37t279B008+eO+/++//h+WweP3NNx48ePTWF7/47i/eH/fl5PTBr3/7N8p2+uM//uMPPvjo1asXzz76+OXLl1cXF7vdLppzLRbr9XoYhsePHz5+/Ph//Be/dz3ulsv1H/3Rn3z/xz9/+tqbF6+uwPT09aff/Mavv7q8ePnyJSWBw2cvLi/z9W63Ozk5WZ+cfPr8xZ/86Xf/yT/4O+er/D/8d/+NTtu/93f/V7AipopaJui5Mbk7j3Rn3qba9q2PyExVd46qtRqRflhbpH2GExki4hCtFO+ajbvpRuRYTv8qRt0dRHZ3iJwRHb3lPQlot47D3d9ZZs1Xi7XmDx/DcHfdHe7BdhIwvVtrHD159/pWQ2htoKJK/b7Bqbc6+HCvi8oDA+eXb10f0ThQWxzceXq6Z95V51KS/OG7319xWi/TL37+83d/8tPtZvPwtS8UDmer5Vtvvfni9OXv/I1//4c//dkXHz35+MXLn/z8FzeXN9/9/g+++Y2vDcRU9tN+PxCvnZ+erxany+H87HSR8O7PfrS5eeOdL7z1wYfvfe9HP/7f/af/aVF+53s/WK0ePH/xWVGePbC8SO+///6nLz59/OTR62ev7/c3IvLgwdk06ctXrx6ePdjup+987wff+NIXdhOfv7qStISNhkIzM18L0r3kcudLop7gA0U6UD+6SsXAsU5DODkKzJ4S4CTZBlEQItqHHh6qoUQ6GwCoFeuKVgcQKJ9KrIfL90AYGcbO1WHNnvVv03ggYIOoN4m6xK7qvH0Q1IWEb0li3pbNBADR0D56NIAVSndMzPeDB+7kk8nkLgSM5pQ93UoQ0eECoGr06fCncZmaqk92MMIAaKpU8ba0h2x3n2lxiwPveLb+c1y5KjE1g3cU/ItoVIWUeh0Ryb/11uK73//5D//oxfe4PHn0xm/99l/LOZu8y7L90+/88O//3b/12afP33333a3q5fX1J3/+/cXy5PUvf/nl5dX++uKtpw//8d/720mn//q/+mdvfOHJstykvX3zi2/+4Ec/JB589PEvFKOZ/emffOfNN98+PT19/4NfnJ2dGBI4/dEf/2tAHj88Q9nvtqOIwXS/L5kpieyn8er59YsXL549e/atr//6N3792//8v/sf10v+r//x7xLT5atXU9m+9vjRq1evVien+/00juPJyclqtbq8vNzdXJ+ensbbFk/mOM7bdpMqhmG1tmnc7jbrs9MkuHp1cX52klIax6kUk5RyTmqmClhZkAIW2GRlKqY0ShaRAqYsCamM+2Q6pJwFZrovo6EYRCSZsiWT9vt9znmRhyFl1UlVYcWAsRTveJxEkDLhrY0LveLBq5PLaKVQDEyKxJTr0jimpgSyo37MVAs9Yuw/VBNmORTbvuU8WG197zQkIhTLmVpGN24ZCw/W0IP/0ZG0gDapl9Or6jjtzSylNAzDcrW6uHwJYLFYTKYkc85Tsetr4zAsVyfr9dpXKzBVEZZpX/Y7jLoYmFNGmUoZSahqGjIz1TiVaSxGgpLXp2eqKMWI5N1Qp0lt3C1lGrKRLKWUcVLTPMgwDGam41TK6I0//IRxHJkiwudNpxpmuRuuAmhd57At8x5A++jKwdCQal43SxEZhmGQtN1c55yHYSA5Fe9MLRDJizwpSimlWP7zP/39za6sHrx5sRl1OKcsxkk/fvZZ1v3N5fWf/PEfP3/x6bZgzGk7YUJ6/sGHDx4+evrgDLpdL1e/+w/+nt5c2e7Vez/78Y/+/M9+93d/dzXgr/6V33pxdbFY5O12MyxSkuhSAZMMUSPKZDrFcg6sdQMkgPXqbE8ASGlYrZZpsf7s4ur7P/7Z4werX7z/83/2X/5XX//qO7/9G99+uFh/9vyTnPOzjz5+++23dVg8e/YxmR49enDy+DHJ7Xbrw+1DRhqZl1mMC2MqyXIaVJEEp+uTYRjG7YZm1DLpVKaUcyaTGHU3qmkhmYfFYjGkrOBoOqRUStnuR1FbDENKMo3jZnNz+nClyhZyExFYFsqTxw/G/X6/udmMWxpyltVySEOWaWIeQJZi+6KlqCRZDOvlcrnbb8bdnlqGnPJiAZ1GLY63D8yAWTQtoo3jznvwkJ7eAAERlnEbPnOzvB0DXpRkqqqNBHyVFyLQO4Dq1GpnUxpQsYNdcw2mIe33+/12KyLL5SItBgCllE+fPVufrs7OzszsersBMI7j1c3urXe+dL3H5eXNLz5+78Wnz3f77fn5+eOHD77w1hvDesA07jdX01hOV8PJeu09vcYy7XeTUTgs18vMtDDKT979xdVme321LZOt16evPX762muvPXz4AON22m3GaSciq9U651zKOE5TlmRMXsc3TYqwTcTBAM5+LjSnSWv80xnPJMXCGBQZd1vMNrNEZAJYLFbjOBYrkod1Xhqx3+9v9jfnJ6ewsh+d3QhhzpmSb/b7lFJarIa8yH/nP/hH777/oQ3nT/Zyca0//tGfv3rx8tmHH2K8WQ98/snHnzz/dPng0c3GsDobFouvfuMbFy9f3NzciGG1Wr3zzjsf/vyn5+fnZvbGG2/883/+z/+Df/wPHz95+n/6v/wXv/Xbf/Wv/pW/8tm/+J9cZieYqi8cKfv9HiCh1difGxx634pESSmt1+uU0ocfffL+e+++/fqTt15/dHl19eMf//iLb7/5za9/+fpqdX5+Di4LCMprb761Wp44ZmAcx2G5oGFGMxTv7JiVabefssiwPB2nfSm6yIubzX632Z+en52enQAoRpL7/X67v3549mDa7yY1zVnyopA32/12N56fn1NyWqysjHuDKgmRAR9/+tmwzMvlOucBFFWoUseyfXkpIjkNq8VSyGnaX+1249Xm5Oz85nI3lmm5XJ6cnpHp1dXlx88/JXl6un5wdpqIcb/dXm+HLMNy6UvQhYxWBSEUqq1zllQXDJ9Kw3MMeebASnleTWa9S9w0JJCYhjBHvcN6Z+ge+MauMYTifYhTSsNgwH4/bsf9+vwcwOVmZ2YGyTkvF4vVg9fe+/jVzz/46Gc/+9lnL1+UcfLW4Mucfuev/dUvvPXao/OzJCi77ai2eXV9efnq/NHDomBKw2KdhuF6s3n22fPnL1999OHHY9EymRmXi8ub693l9ebh2fp8hbOTxenZQwCbzWZ7dQFVEUlJSWbJjviv7XDS8+fPRSTnRU6LlJJkSTlak/mwqKNBqAYtwOLkjNpGi1NdKG5XoEigFAb62DhwwMXNLgvTsMjLZU6DwkrRcSynDx5tt/sXl9cXFx/x//if/9+++/0fbfa82kyfPL+6vt5try6XIrq7Plmmcdyvz883ijEtbHm6L4UmV5cvH5+cYLzZvHz+H/7u33/njcfLxL/8m7/+4x/+8L/+5//NN3/91/7hP/rH/+Pv/cthffL3/+E/+S/+r//39fmjJ49f/94PflxMCrNa2o/TsFq76yMJiUyZIkjguJu2m81isUiJTx49OD89+eiDX7z47JOB9nf/9l//9W999cWzD7/25S88efygbLfr9fqb3/7NsZTr62tVHYZhP45mPDk/Wy7W4zgq2BoBTqpG7nc6lmm9GM7PTzeXl+O4Oz9dX168fOuNNyctL168+PiTT0spT5++/tprr61Wi4sXn5mpkbJYymJpzPui+2l8eXH14MGD08Vqt9lcX14l0/PTs9Oz1c3m5WK1EEm73W672dNksVgNw1CKencdLaNZkYTFYjEMw3Y37sZiZnkxJFm8vLx47733PvrkYxF547XXvvTOO48eniZwGreqSkzr9dLdGENpzr2I6FSclSSCUo4ywTAMR7Zo64nWGKlxZjGMJikvRMTxZSEc1ZoZ5iqw/TalBNLMSimTqRkkpySDEbtpJNKDRw9zxkefvPzoo4/2o/78w+cfP3/58uVLyen09HQYhjLup3E8P1l/7atf+tbXvvzo4flCkAWm0ziOkoYCE2bLMu7Lh88+/uGPf/L+hx8pZBiWw2KZkq87L4OknGzB8e03X/viF7/86NEDQVKdRGRYyNWry5xzSqmUcdzvp2kEKMKzs/OK0ImlewTJgHG/jwY4VJoao3YcatEtyH13j8cIN5tNTgsRKabTNJFcLFbL5bBc5HEc9/t9UW/ZPBhRjN///g822/3l5eXl9VX+w+/96Orq5mZf3v3Ze9dXu6ePn54uk+12j87X15evDPaNr37tz37y7oMHjzYml5+9fHB+en66Plkth3U6X6Sf/OQnf+Ov/EePz09/+pN3b7bb/+Q/+U9+7//3P//iF7/Y7/erk7PvfffP1svh4uVn06S77U3KKyYKxft8AaiBabiy8iavYqDaOO4vLy9Vp91UhuXqwen6o08+/Zt/468/ffSQOv7pn3xXp91+v//ZBx//zl//m2+//c7zly82N7vlyflqdZIXyxcvXl1dX2/3Y3YNVsp2t5+mab+fVHUhfPr0yfb6aru7eePpk8urK6T1brd79uzZhx99PI7l+eXu5fV0erZ+642nu3G82W5ffPrqxcuL568uLy6vt+P08NGTs7OzVR5urq6vL6+y4NGDhw/P17/1278OlotXl++99/GzZ8+zDE+ePHnw4ME06dnJ+vz8fCnY77e7/Xa7mbBTQ3r4+LXT08Wry/277773s3ffvbi4UMXDh+cfPb98dfnTd77wxte++tXHj1+7ubl59fJT2atE53L1RLPQEwU2TVMpJRHDMOQs3gF8HHd9vLElmkuXpGa0wyOZtztNkiCi6l1qQMJKdCI+QFcXUyINMuo0TmpmoAyLIS2WSYarm83q9OH1ZvPDn7232ew++uSTn//8vavrG5XlsFg9efqGDHkcx+1+HGRx9uB8u7352XsfXV1dP334cMhIsEcPHzx9+tQmXG/2V1cvL2+ub25uXl5fvrzYFCwlJeMwFY5ToZmI2SBI6frq6mrz3gfPXr3xxmtvvv7WarW4ubm5ur54eP5gvV6fnCzyYjHkddJJJKdEydzsdHNzfXF1vbm63uxHHXVSffTggYkNkmSQZR7ycljkJILHD87cBInWt5OVUgrs0ZPXtAbezTAZVHEzTZ++eJkWeblcLtZDKfby1eWz55++eHX5ox/9RFLOOTOn/Oxye3V1s9uOu7HkRB1vymazzrLfbB49PP3tv/zXvvLNX/vJR8/GUZWyXC7Lfo9xfHbxme63X//iFx48evj6m2/87Ec/gJbdbnd9fbPb7f7pP/2n3/72t//j//h/e/rw0Q9++JOPPvnhdrOf9rthsVKD5LRKkoniwS8tpRRDoRaYrdIwTfuU6D3bt9vtuNudnp1RME44O3/w8Ozko/d/dnr2AFZ+9rOf/f6//sPtaL/92//e62++8fDR2YuLyw8+/kCNz1++fHVxdbPbprwQydv9brvZl1LWq9UwDDdXl2+89sLKtL252u21lPLzX/wb7xOuaWVWPn5+9cEnPyw2LRcJYsY8qo1qkyWmzMXqg2ev9OMX++243+4SuFzkxccXhvHVfrdc5v1+/PSTZy9evBLDh5++XORhu92++eabX/ziF88fnO5222fPPn7+/PnNzfbho8dnDx+t16c3m82zZ88vLncpnazP1rJc7XYvX724uNlP2wlfePPN5WpgXnORRTR7YKZMBABNsJTSUHwRGG/ioqpaTItFCC5s/hrerO3lZ0eApErOpnl5klOy3a6UUaJDnQVw0ubmFI7d2Yx7IzgMq+WKSS6ubp794r3nn72aiv3at3/j089e/sv/6X++uLg6O39IZlmdAWmC3OwL9mWapmmaFgtC7MGDp5vrqw8+fvHZ84tpf7Pd3JydrF9//c08rLa78Wqzvdls9mWCUHKS4XQs01S8HRloJmaFWhTD8nS7v7l+fvHs1eWPf/a+iOx2u91289prr52ers/PH56ulznnlJjzImf54IOPduO4vbm5urnZb/b7MrlwKfvRhJmUIS9SlkEWOYtADIvF4vzk9OzhgycPH509eHR6vhqWePFSr6+vt9ttHobVaj2O47Nnzz55/uzZs2dnZ2dPnz49PT/TYi9evHj/ow+fv3j59MnrEBYmgvln73/y2WefSbFFkqcPzk5EFNObTx/tb26+/o1v/r2/+3d++O77OQ+vrm40LdfL1ebV8zeePjp7581nH7037rYnJ6tRizdp+JPvfOdrX/taTsPjx4//s//sP/vhj37w9jtf+vrXvvrjn/7kZrvNIoshbbYjmCAL70s5aillUlW1CWUioNxB7dHD1xar5Xazf3n5crsf12X98w8+4Bff/u73fvj1L7/zs3ff+9pXv7S/uX762vZknL77vT//zp9976vf+OYX3vnSq4urq83m4aMnxdLNdtpO4KT7aXt5fTVNOqS8OFmcn55vtiPSarFMFze7mz1THrg4cZ4H02TYT6MwLderDTBN426cilssQ6ZlsWyDjLtph2R5GIYFl3ksut9d/8Gf/ejh+clyubTCxemjQVKZps+uNjnn9z757P1nL3wZFtXJzZgPXryv9r4ZmdOQl8vlciGrm/8/Z3/WY9t2pQdio5lzNbuP7sSJ05/b8vKSSTKTyWQyMymnVGrLLisFy6oyqizAsA3Xk3+GAT8YMGDYSFQZliFLJRVUqFIpJWXHTGZLJtvb96dvo92xu9XNOcfww1xrxz6XlB+8cRGIGxFnN2vN0X/j+zzVpQPOOeV5WX/w6cOHT453tsbjUW9vZ2IN2I6OVSOLK6h3RZ4ledJjguAb731ctySmroUHYSPzRMNEhESKGDrWYB8QOKOUmE1kpyZjkVr8tIJGqr02NSUR0CZImmcmSWvnjg/PHz15+uTx09PpYlmUy0ach6KWpD8x+aBpGgAYDUbz5bKsGmNMkvbSnFTVKcyWVfBi2QCnaS9hmxPgYuUXqxMFIwjIWZK0ajsC5JwCQFRMMIgStHG1iCSWjDE2zb36Re0BQprmo/7o8GzK8wUfTkGj8TciACCj0URANYgiIDBayykxIqchpqOCUiuo18p7ACFRLJrT6VKfHTGgIiOiIgwGo6IoqqoCpjTJvffn5+fT2fn29vbZ4vjuo2cueAAySZJl2WjnchWCOIlR1MyW9XC8TRKKs9PU0Bdevg2uBOeuf+mLvd6gce73fu/3ZpX3alRZ1FvCxfRM61R8AKYrV66cnZ2dTWcfvvvOL/3yN3q9rCiKb3/720dHR/fv3vvhj3/6t//u3xsO+s7NjTGGULwXqcBoEFBAEQkg2rKPIIIE5wa93rUrl3f29hfL4s79e6tV6YJs7106XxR/+N0/+1/8/f+ZUxqNtx+dnV+9duPx8+eDkZydnt+992BVut5grCZZ1RIkVB4EEhUsarcoggD2c1M2upP20Kw47aWprf3RyfmciPIsLYMGtL3B0AC51dI5AbXMuGz8clXWjVc0aDyoCQBN7Y1JEFEEy7KxThAVlIyQn1UhrEDDsD+YTCZsE2hUCcuqqqpKRGxq0jQlIkEAaw1bAKy9K4pGV7UxVZIkl3a3FRUYQcj7ZjovpucLBUkTMIYym7BpyZQxeEAZ9HqXdrcvX9of9DJQEuEkSdI8q52LYL+1dEAIQUTSNGUmUlLVunar1Wq5XJa1H0/2vCZZlvnQgCiQscgBhYlElVCklb5QAAQCNkhpXjt358Gjd9/78NmzQ5Nk48lONt4+Pl8dnZyWZbNzaWtV+8WiHI/Hh6dnzJzlcSYUqrJARGutA2RiCLooamvIchKCn81WaTJwosEHhSgLCs57F3yv1yubuikbEUmsTdM0SfI4u26aAFAbY2ySIWLl/KJYpmkPsV0ftkYSHMRmQVzHQNMm6j6ExquIi9RlerGwDIjIaFqpLAAQcBAhggQAxdlMVEXQVc10ViihtfnelVFVFTYbZNYiopOYu0LZBGaLBk1iLKJhk9VNgGp1/eqVb33tF+5+/F6i8l/+H/73EuC3/6v/+gfvvE/WnDx+fvnG7SQbLpbzXp5tT/rn09Niufjr3/67ly9f/r//P377N3/j18bbW71BP7WGrZmMxlmSvvv2O1/+2ldSa77+i1/7vd//zmA4nC3madLjJJstKwEDhpAAg8bdLUAA0V6W5mm6vb1trS3L8tGjR9ZaFxRAgw/LVfXDH7116/r+s8PTy1duPHx4//nzk62dnb39rCirs/PF3QfPJ3t7167fsnnfOtne2fvgk0/ny1V/OB6OxrPzqQJNzxdZb3g6WxjEyc5+U5fT2WykZLNhSsZ5v6qq2gFR0iiGBlYN1MGATRXIBVFFZKvMruVxp9a3iCCIJSjn84TNsN8LYJqAmTVoUwTIcls0wYvv5aMAsCxLNCwAGkQEnAvOqyKkyIzm4zv3kyTpJ0liGUEMcp7neZ4KBOfcvPHQCIGoBkucptnZ0jW6OD4vRHwvy/cv7+1s5XUNaTY8Pjl+/PjxycmJcy7yY8epXUxKdYPBXhWPp5XBZ2zQGJMlaZpaa62xNJtOrbX9PM3z3BoioizLsrS/NczuHZ5+9NFHDx88XqwKmw3ImEXZIJlQzslm452RomXDaR9mq9Ugz0Wk8U6dByWTZNEYXFV7RIOQGKYovYzMiVWyXtSJBKdGmQwrEyDPVgUAICfGIBGIgvMhEAGYoBDEOXFBIEkSTtIszSKoIIhvWy0aM4OwgZGI+wCIRMwUvABGwRFe/zbOdddjVaZWgUNAi6qIBWFAAzZR1Uagrn0QbFxAB13mT/Gf+MjhHgIAmNXxyWtf/nKP/Pz42Xd+//cmveT/8n/+P4nI7/7eH3iAunGrurl89QBAnj5+cOXK5VeuHdy/96llun3zxgfvv4uhuX37dtrLb7908+To8PrVq5cvX/6jP/qj1157rd/vk8D09Pg3/9pv7O3tf/dP/vz58ckrr99SSs7m9wRUG1LVoJFEvd0Vrari1vVrvmkEYG9v78bN248fP2agxjWuCavVyr33wd07n75889rXv/qVNBu4oHfuPugN+leu3agqfzYvi1V9Pl9++IOfDre2f/Vb+zdvvTKfL85m56tVGUTPFgvnXL/ft4Zq7+arFTPnw1HS61dVbYhGu3tJ2RyenLqgw/74eHpc1KHxoBBUofECCMYwcxKPbwihHb6LAorH4IXZmEZ4WTmBVZOkrqnrsmp83e/3+6NR2dRkOBsOlkW1KiqT2CTJbGbIqFcBoCaE0dauBN8E11S1AUytsQEapybPvUcfCzxCUe8UglOD6byWsKyaujZmebao0/QwhECpLeuqKkoXvCFjjBVlBfArp9huTK1JgAF0tVpiRwRN7YAXENEQNU0lImliYhRlNsS8El0si/Pz2ap0QGmswIOiEqkAInvArrXKZDh2DhERqF3Tl9gaiPzcCAKkwHE1QxEa0aAgxEiobDxAEG28+ICAcfkYRJAkGI/EAkSR4T0KQPqgPqioH/YHqgrIIbgAYT1liHP8C9hMO52PxIIdnCYSLLWmaGNLKgAEUfHOBR9aW46Ac1JVQBQgQBEy7c4KIYJRVlAGQtoAjpuX3/yF/d3dp3c+LObz165f+z/+l/+7f/Wv/tXf/we/dTZf1AEWVV37kPVz5wOj2xrlZ0fP6tWirmu7uzUYDH7rt37r6ZNH//Jf/LOPP/7wy2++8Wd/9mdZYm/duvXyKy999Re+zMxvvf/upUuXvvDaK96HXn9cNP7o9PTGtesPHj716mJ7APiiUZ5Yu7OzNZ1OhWj/6o3d3d3P7t5LshwpSTOjxhDbsqqfH54+fPx0MOzdvv3Sg4ePj06Oj0/fReTdywfD0VbTNJPJ5PDk5N133037g/F4YjipnQ9BszQt6iqADvu5iBRFgYjD4TCIClIjOi/Kugp1EB8EimK+LJ33InGnWIIKMSOyqkoAF+uL0GgnUepAQTBFE0TLogbRKDdBhpfnRZb1mG1RzrN+L8971WwZVECABJip3bwCBgabJt6BDy6EQEgi4pwLoLlNGo8+kCW2JmGQ4LzXgCYRCU0IjWeDtKhkWddlUytKXOBSsgrsAhlBQQg+rruSdJsZkTK0P8gQJNIlx2PqVEEl6fVEwDnHaFGNulAvizqE5yfTaGZICTOLEgQVhbTN1QUVQghR8tRaK40jIjTEhKAkgBIkltugoECK1C6CKApEI9R4uAXUizgXGu8BQEVRI4UCAogHQdS0lwIxM7FBitrMIqLoQ5z1qUaWMqLYxxRdw4lbS4rFkbVWYY09jDNDAABmowICKiIXtbQPNks7M8aOYCdyhbQkP4DUohuRlbrZRjTCar786b07o5SuXLr0P/17f2exnB2dHJ+dz03WO5ktxdh8MJzN54Zxa5QvTg+Pnj69erB/68a1b33rmyGEJDVAOtnavnf/7l9+/wcHe7uXrxxc2t596eat/b1LP/7pT46eHz56+OAqkPfu9ddeKT1M/+rH8/MziqsQRMwcSfIlBADdv7IP4p8+ewyclE6K2tfOB8TGx10kk/X6eTJyrr5771EQd+nS7rVr1/YuXX745OnJ8VldNVV9vLV96erVq4fHp++++27twquvv769e6nf642GOakulq6qKmt50OvhcFTX5bJYAbFN07KqDx89rRqX9/pE5uzwuGl81MRQRREgMpYTY0y5KrwE55wXJ+IREQkYSYIwkKIVUee9dcaQTTOb56n3vmrqZjqt65pSG7u1SZ6BUu2qUKmqMpkkSRhtWZaGKUmSgIDBKwTvQSXUTp2ESL7ckKAG1bjRxYjGIpPJmNlkCSNqUgOAokiAIK5xgqrKbCwBr4kIL1jSGDRItFgCMhH8xqoCWjYBOMmSnKxpQlgu6+VyVTce7YABnQQfQt0ItGqwmGQcITstEwQJASOxpwYpUATmkHTI33buvya9CEogISi60ARFiV2hANJNYmIUVQmqShJTzQAAUfGQmQEMcoi9XyIqyxIRiQEReYNaulvblAgBBZR2maRFEinoem2fACCIOFHx3kmI/WdBVENBRQAYWFtMUwtliASygojakhcKtZOiCyOsF4t+Yve3xjeubI/7/WdPnub9YenCp/cfzcoqH2SG2fm6LMu+NdjQSzev/uqv/urBwUF/MHjrrbc++uij09Pjd957d3tr8vDhg29945fHw/6tW7c+++yzv/r+X7766qvf+pVvFE1197NPVpVcv/nSyaxwVX0+nVGSAwizYeag3nsv6oFgezI+ev78/Oy0P9q6c+dOAAbkoJikuYhvqsp5GOZp8F4A0ySfz5fLZVGUtTHm6tWrTvXDjz/5Um/UGwxfunWravy7731w79NPnz5+sr29PZqMJlsjQWBr4lZz1s+UdLVaLVYr45plWc0Wcx9UmQlNWdaqyMwAGuUQkSmKQK3mC68+iAsaECNgkpEsIIGCiDhRX3lHLkYDQ3z58qWTs9PZcgFAq9WqWFVJliqiC66u67p2IkJkkiRJjO1neZqkKdsqNK5uJADZxKCZzc+B0HCC6pqq0CDMnKa2DL6FRIr64FzwhqKGXIikDBJa6hYkYjQCAaBtKminAyigta+jaicxE1Fc2hTQqvac2IST2ofz88XZ2Vld18QpJxiUg4j3AAGIJEkSYhukYy1AFBH1gQkMMphIURFC8AoGWqOIm7KRvLht/qiEIOBEg0rw6tVDu9eikb4C16x0sWGroBpWqxWSrtWhrLWpMcaYqqmJwQgbY4CFiAkBgePiJsa2a4ciApC6XjOUx6tEEeztwYsEJ8EFr6pKyNYgQASldfh+iWt26/yuLSk1IBJGNZEN4LgB7w3r0dMnX3nt5kcffJgk5vnzoz/9i7/87P7DfDCpRM+PjyAEVh0P8zdff/Xv/a3/+J/+03/24x/+6Ne//RtHx8/f+5fvKOmqKsunxRe/+MV33nv/YG/7G7/49e9++lme2n6/70JzMj1Ls/6lvV0AeP/dt09OjpJ8WAeH3eZpCMF7byz1s9QQnZ6exs7BanVeOUnTnnMBAPIkpTykvX6SZl78YDS+cnn/g/fenU6nDx89OT093d699MabbybGVuVqNuXtnd0kyx88eHB8cnZ6enxydNgb9l557eXR1mg4GtuEG1dVtTjnRCSAPzmeOhfyfk+Ri7IKobI2DU4QKEr/haDo0WOQMrimEggiQVGQoV32JCJI2x/HrXCRuq5BXVHOt7e3t8aTAFpWzWy5CAI7/Xy5mAWVdd9S1VVVgQqT27fTxDCoBtfUlWVEawwjRcwtNq5yzrkQQpIkCDkRi7XMLKoSgjpQa43huq4SQ8YklpgNqAYNwVXB2jSeeAAApogZBUBrckQgYuAoOcIAoIRJbhTBSViV9axwy1qIUpsPF8sGEBAZFdEAAAZF9EIUjCFrCEDVaxAvqKLBMIiAVw9BBRTJEBMhiCgAgkKAEBsnKt4H9UGCSgw7UeIHuYUialSWQ4w2HLUvnTjc6AYDqI2/pZaFVTXE4gIBABy00kOKpBRJvhCR1Fru7IcgkuACKlCIEPnoC2IzMaICeYPfAtudD1hDdtsHrL/gxQ6amL61qPUbb37x13/tVz98+ycPHz78L/7X//if/It/hTZzimXd1I3PGG7euv7Nr33p1VvXf/hX33v6+KEifve7f7Qqi+l0unNp5/XXX8+TxLv6zmef7U6G3/ve91T1C1/4wjs/fevew3vjne39g2vnp2fF4dnR0dFoNDqezm2St7JXAKBqmIfDwf72lqpkicl6vdp7a23ZVNamtQtlVcGQ1hX1sqjsdDbs9+7evZv30mvXrllrz87nw+Fwe3v7+Pj44aMnly4fjMfjcrUY93v9XqaK1pqjo6OyKQ3S7t42ES6Xi7ZnmFhYAgAkSQJkqrqJku4EwkBBSUCYgZlVtfahlyUBRMQripKwQRN3GYgV0BiTWQKDvcRYQ+J9XZdnoP3BcDwYuubcGMNAs9nUubq9W4SMKAEkiKiE4EQMt+BpThLDjKhhOOjFGYOqIIEiWUuJoTzrxa6dEpIxUReJmVk9MXAksREMUeRXAkEC0QsiRvwpKCFRROGIROwbRMJtAHDBr8pivlxVVVXWjQcCRW0CcBZDBKEiqYp6H5wKoiOyEY8oICBBVASJLQH6bvNAECRqK7dkXKoiIIAiXkLwIfg4wWupXNriLc7nsaMqjwc68iRYw3FFUCKqpXEOCTTE0UJLJ9CigWIh7xFjwqhRvS9GxTTNIYJnWzhAZDKSummcBEFB0/JhK4CotFjTuMElAACCQgrMSAqtzDtxTInhgjkeANCQm710+8bVy1s//sH33n3n7X/8v/3f/Nf/5J/ef3bc397/+M79wWRrf/+gXp6/9uobv/5rf+3k+bO/+KsfVU6NxcVi8Y1vfP3td9+pqvJX/uZ/VFXVd37/D5Je/9Gzo62tnZdff/1f/5t/9+1v/0Z/NvveD36s+Pa3/9rf2No7OD6dBuTBaFg1AVERAyEwgkloMuzvX9oF50ZblxzAydNDay1RAxrUu+2tccJUNEsvDrgXUBdlMZ3N/ubf+hsffvjhcrm8fuPW7dsvf/Mbv3I+nf/xn/5ZmvUGg2E/61s2ic0Xq2VVlYrZrJgfnx0GV48ng93tHSSpyibq3TLzfFk0zoPgcDAIIuKVmRCZICBwRBuralITGxTxIl5AAgRiaO+iigjkCQ7zhMnk1mSGm1qYktOTI9UwGI0JZDToCdD9hw+2diZeQ6QbQkS0iCZRDc1q7kmNTVJDST9NLDOAc9V4PKmqqkXAJ7YFQKfp1tawruuqiXsPICLqXeV8P8sDiIYmeuWEY189aZoaAIgCIRIpaVQzUEzaBFUQAREisovM6dHR6Ww+W8wBOU1zkybOuaJyaZKIqPoQNDABxWoPJIQAYhhitNJYbomoRPItbE2/c8JCuI48EFRUwAf1ok4aZCY0xpi4CdTFPE+AQoYBleJWRJzrRdItaIOh98H7xHCv10MGCJH6AxUEBUQF2XTdE1AIJBJhOMHVACBgVGLnhhRIQOuyiZZmyCCiIMSal4nWqXJkBmw1qpC5o2InitaIEDVIOqYOM3323uSL+4zLP//LH/zD//Q//86ffa9S5qT/8OHjg8tX87x/dnK2NRxtT65+9MnhX/7l92YN5lu7y/np3/uP/sYX33jl/oNP//pv/vpXf+FL//4P/tDmuQdzcOu1dLDz1vufNmr+4E+/d+Ol21//tb9+dj77+MHT/nlx+cbNh4+ezBfzLO8xYVOtOEsm/bRYLr72xVeChw8+vl9WvqqqJhASTUZjLyFPNCO/Wi5Gw9729sgFt/JVLTAJjQL8yi9//eTk5N6Dezvbex++9/6NK1e//pWv3bn7oJgXg2uDl2+8cvfOgwQTZFZBy6HwxWx2/uTJo0E/H/V7dVGiBsuYJYnmUrNvPAhgYi2lpPHOBxUxqkrkiTDLUg0OwCKajpMPkSIFOAfnU4vjUWoNiGsM+PFgUFXV/t5LZE3duPEwW5aVF3n19s2yrCSmO4iIyEiWERHVO6NNP0kpzV0N4l2SJJNxvyiqLDW9PInrcABgDVqjqQWCuH8Yb7ZBYQ+pgAa02GlxEpFlZObhIHPOBXEEmCQJM0OQJjRpbl3wEoAss8mKqjk9O53O5mfni4DElCpQ41WBkFKbAIEQSRRWQ1CIQugYVIJvpCa1CUdkgmuqum7Y9hStSUzsLalGtKKqAAEGhRA0BInodEE0SRbJSaW1wMj4CnFpC1Sk27tHQgbWoJFD0hi0bFv2HhEDSAoMggEQgAksJ2jYEYuqQgARVOhqQjDEqgoCsXPkQ3DBeSeGeC2eKQAUJZI5Liq1olfYcrEDCCbEqECR2Q8RIyiF4ptu81Xzm7/+tZvXdi5fu/ZLv/KNT+48fvjkadW4qnH7lw6c6Px8NuwPDg6u3r3/+Mc/fvvo6HnZBNG6cv7ylSuPHj26crDfuOq3f/u3nx6emKw/3No9OptNpzMMXm0iIiez1eWr46xPT47vPT2eKoJXGQyHSZIs5ufDfjbs944Pj+pymSIGy/NF2QQSRSSLCCJevA+hQbCDfrq1Ne4PelVVsDWioXTubHaeGs77g8uXL49HO1mWicB4PL68v3//waPnT5/efvnVna3dRw8fP3rydNAfFKq+rObz88eP6Nrl/euvvW7ZVFWVJikRGaQk8T5g48V7r6oBncGgjMACQIxKhMRCZCO3Bb8griqI6K2mSTLIOLGkCRBglqWJRSJSJO81NRwSK6rIpp9knd5Cu+DHRAYheNfv54NexowhZxGxbJIkGQ56ZJjRiPhYE0ZBC0vKCSZkY0QlAgYOBHUIQNhNxZQAjTGxZ5OlmaE8wt9ERIOzwQYI/X7GbIuqPpuenkzPV0XtQ8SdkhApkKJRBAAiUPWuzdha81NEJQSDGIUowXkCVAixzidkIKLYet1QPqubEpRU2zIV2TIDozTqECVCyRlJsFWuYqRO+yuSLkYeUjU2abmtWkGHeHXVe8+MbAiRmNAQMzMyK5sAimoUAolfI2MNsgfBluUyqnYqgTAwRv5VQo7Dfl0vhXW0YrGbDkBEDMDSVtwRsUOo2paQrRWar//SN4q6QeDFYvngwYMQgjFm79LufF5lWU5sR4OhMebjjz9+/vz5wf5OYtg1JanMptMvvPry97/3F2XV3Lv7oAYc214QrZxDm+Zpv6jcycnps5PTfNDvDyZ5nq+Kmpkzk6jqcjEDAGNMURQi8vWvf/3111//07/4vnN1aAUrIY5YjDHGUp7nWZaMRqO4xG2tBbAKtCqqEzr3Xs7P50XpOE1Ho0m/39/amkSR57d+8oOtrZ3XX3vttVduz8vFx48/s1lSlMuTw6OH9x/cvHa9l6eGsaqa4IIEB0EIMbVk2Yp4BAtIkZyX0JhWm7M90IhRNuOi1EbkEBJjKO9llgnEEGCaGAQJKs57ADCWe5whMpKRjgOC2vIeEJURgrOpNYaQGS2lAGKQmClJEmAyaOIAzHsfvA/BU7Rek2DHB8pokCGCntdGiArMbInjjTbEsTEWQIFMmtoGEdg45+fz+fHxydlsLkpsEmO4bSDGpUKMDUMRiW4d4tGMk/3oqpDiHkZczQdrrSUAVFRBiQGGNAQVkNBRD6rGfxqTC0EiH+cDiC1n+QuUgRc8cRAJLNnHPUqF0HH9RJqMyosBRiaDhoiE2RhCtqIoCBTtHE3HeyeK0XyCtFuVRAhA0fRbAF0s7VSBFAG4IwQTIIhhjwDTjsmrTT3XiXjXDxEEc//+w+293VdeeeXP/vIH3/zmN9P3Pvrej94SSoNIxoOUqKhWxdPlfDkzCSshesDgx/2BNN4QPbh7r/fm4Pr1G8/OzsvKceKv718dD4azs9PzVVnUfnt7IgIEOOoPgpegUFbN+XJOCsPRwLIpq2o8HOzt7R8dHb333ntJtg1CzjnvHQBYy1mepGmaJMYYW1XVYjkvisI5lyRJCGHRhNWqLIpysVgQ0XSx2Nney7Os3xvmeRqcSwyq6snzx0QMhr/8xhvpsF8WxXvvvXN2evrZx5/sbE8mW1soASEOgBwgc5IwszJbRiZea2uuaZy890hRU5gIYi4a1xVYAhEjgRJIFK4OAZumafkWVBLDCTECKbEPAaClfuO4oYaAqCQIEnxoKMqaoQEA0SDBQwBP3hhjDScMjtR7JQnGkLUx14nidsJElltZCI2bx93WEmgCAKiiqkSaxCYOWRA9nc+Pj4/Pz+dl1WQ2ATKKqGAE4odEJQKNZDwEZGEdc1rzI8TIbRPzxsgV365rBJVW4hsirQeiBkBIEysKGkQANYiCaNSnVOF1FgctEOZzvKkga+Jj9MSgKKQdxxJCNHwCQXSCIioAloCi+kHbYiVkQhDVEJWQFDFIkMj4jQCIzARM3DVfaD1MjOEY2u1eVVBSIiRCA5ihZY2iOa1GiFIk2YuYeFIIZrFYXrl2vd8fvvHGG1/86i//xQ9+4pwDQ5PJ1vHpWeMdADRNk6dZ1u89efJoa9DLGa9e3v/6V7/y4x9+f2drN7PZanVYrJqkP5xsXcr6k7P57OHDJ9WqHG9tzZerjz/+2HDSeOdF+8PBaDgaDHunxyeGuGma8Xjcz7NnT54+e/Rw2O+XGp1pt8pNscfHVVWVZelcXTdV7FuKiG+cuiaS/Nl+HxWmi+VssbDMw/6gl2aDfj9Nk2JZnB6fD/s9zrLmNIyZMra9JD07Ob772Z3q8qVemjFRZtj0cmNMECHDoOS8swRIwVIkBZQon6CqCSOAxL70uuGMSEi27eKJD4ISHACod+KaoIKAhpgMa8TcgBjDcUsIKeIphBFRRdASKhElbNIkNUyqKuItk4gACIE3hIaMZQpC4oM1ZCI8R1Aj84U601o1tmkTKKISAKdWRFSIO+lC730Zwvl0MZ3P5/Nl3Xhr0yRJFDmoSgCNjmWTplFJY5tF2p4/Yfx9G42JyLTmp4ggqCmbGCYUSIMQiSAEicBhVKIgqghBBAkFNDG0SdO8JnTejIRr4uaAxJx248N4H1q5HgYlVFVoMWaEZMACpjaJQTZi5lRDXI8qm6rNUIgiAU1snDHq57jFsIWeRj6eAIAAQgREYIFTZGrZaNs/1FaXl+JsFhDNzvbeZ5/dvf/48B/8w3/06aefvff2O+PhVq1YVdV0eprm2XA4rOu6rIthf5DlaW7NKEtvXbt+ee/Sm2988dNPP71//8FivhoNxzsH14fjndPp+bMnT2fzpSGsvKZpqj5wwuM8Z2t3L126cuWaSezjh4/yPH/+/Cmq9rLMGHN6PE9stiibIAYRrbWIGimSvPchOFUNwamCtZaZQ9CmKQ1ZAEjZJplFBd9UwTXqdbVarhbn83Me9vpltRoPRwd7V5bO3X36ZFmUvSwt58tQN7PTE7daSfD9fn80HueDfmpJ0ZBhFWRmiyGeIgYADNpNhTnemzbHaPMxBWDqsBcaIiiaVAKAMYZVNQImkSPNgYq2LDgUB21tRY+oNjExBTWWrYl2IiBkmYLEsT4wKKEgqUEGZiJgRkJVJIXQmYgnBUIDCBI1e4GQCYInIDZkkpSZ67peLovzVfn08FjI5r1BmkkI0XLZEnXYLlxnVu33SnGbTy/0zCGGBROH/hRpHRUAGYQ0BipWUsCoVQqKyoABWzwZAQtwVIj0ckExuPn151IqBsRA1DoL3KwJxTKiQiQ3ABSieNckghEMErauigElrs94UhIDhMSGiARJYnc1kn503Iek0NJAKUCUF4hdOmBGZEGMsrEbY0OIZAidczGn57OPP/lkZ/+g8fL973+fiMqqOD2fBzBZnvT6ORHY1DRNFdTv7+9nrp4MeqN+7+jw8Pbt2wf7V95+/+OtSwf51qV0MC7K+vDorKh9Ppio+MVyPuknIBCCNI0jEVfXz58+fnZ4+Hf+1t+y1k5Gg2fPnp0cHe/sbLsQzqbH2Xg3+BbdH1u7sQ0Yr3lkKCCKcyBBTIxNXNAGxFcOVFi11+sPe9nWcPD88aOD/b1v/srXj549ffTwfl2eT6eLK5euZsMtRjzPe1JV4v3R82fz89MrV67sXNrb2tkmw8omzfMkSQ2RISAVjIoMkfweJUKmoCPMW087ERRFI21gqxHeaiZ0bDctyyiAKhkyggABItdAS8cEqoCiKAGppWombds2SgAojBhbcIyAnbAxc5Q9CQRAbCK6FRQQKBonIseVOSAmouBFkASoalzTrObL5fn5bFHUjQsmy9IkAYDgvPce2vn42tNHqDdAZERvxdXa6N+WbnELaH0c21SBWulKigIrEMENgsBR9QkU4/UDbmn8CUNgaT3dxWRtbYRrs8RuPt4ASlsHtn8YPaTleJG7ChzbbpaRmohYIrOdtDMGDbmlADam72gsRVy4MjJFQIFCiCIIDIikItJW9Wv9EkBUQDUqbQzHboW6rdI7CQNjsvzgxo3/5H/+D45PT9559929y/sf37lvLYPi9u7eYrE4Oj4dDcfZaFgVVVEsLWOamLquy7L4d//2377y2qu37t6rhcuiWbpZ49EF4ZYKIEkjT0JTRcK/ncn45s2bAHB4eHjv3r3IU7i7u3vn08+Kosjz7OWXX54WTkrnnItyaLH1QUTtFiwAEbW/NWSTtBFEIiArEtSF2MhJkuTRowev3r71y1/7yv6lXVfOb9/81s5k61/8d//6/p3P9g5u9Pv9elmo+F6egW/QIIKANFW5qr1zEnr94Wg0stYSk3ZsZV0fFBRaceIOkrQ+GxJZWqDFK0V4VCQjFLqIJxCPBFlqU6NOhzymQ6oUxF/cs8htH0908ERoCOLFWZ8yAA1BUIIwEwkRg0Z4vCGDxIxkAEgx1mfGZtYL1HWzWC3ns+V8uSjLug4yGIycgm9cfPUsy1QVW60yaCNA+4pBFJU5NkLW9hbLCREJoNhqSEFsa21qBmN3WWK8res6zsQ7ljOI6av5uXoAcDFLwI0HIKZdrXhBRhrHhsEzY2K4XT4SAQDWkBkiUOY4bUeJ60jAGlVomJiZjI3vNqgQmgAGSABMjLEGCRElBCJgbNs27eqIIoARIAZWUkRGBkYDpHXtgJSAgdQUdf2f/ef/+ObtW/cePGFrQggAkqY9o1gUy7ourWWbmDxPE2vJN9bal1669eUvvfnBBx8UZem9D14XZYGprXzdeBRFIqMiQQOCCmje79d1XdbVYDC4detWuVolxgTnbt248dnduwBw9fq1+Xz+xhe/eHI+q6dLBbUJs7noOAJAmqbQsl+JqsaQCEDGJI33TVWBhH6ajEf9YT+1jC+9cvv6tau/94e/xyo3rx382n/6j/LUWkOL+XRvX+98+omryyxLVovFrZvXrt24+v0f/KA3SLd2d+pFRSoAMp2eDnpDhyZSv8UHInDLQEvr9IJAO8BEbFa0268hQAi+nR0RxaVJahfKYlRVH7y0ooTQYTaZGAkSRIwvmhjqXl2jVHKEBLeHTIKqeudi+4qI1AcAMNZaaxUpTjqJLBF5hapyVVPWzaz2oa5cWTdN7UJAsr00ASVGIDIX/NBxoIWI0BJn8FqJERSDQqehDJuASEQTiRMjoWXXxVBm7sYSqqBIrUpHi1zpyqaLT4esP08uOzqYjYZZNEJhjk/SFmGxJwwAtNFyj0+sqqxo1RkiJMZWmbLdckCmmDGKCDMaY0SkbiQESchYm3ZOUFRENWR5H9qNjO6NISEbJ+QA1CtZMmwFxNWucc216zcqV4fGBxDztV/+5vlyJY8e/+vf+Z35ank+W21vbwfAAOCDJoZsklqmpmksm+297f/4N3/TL+f/zX/7L7/85hd/6Ze+dnQy7Y/G59WZqqq0Mu8xQY681IjsRUyS5HnuvX/w4H5dVlVVLZfL+XIRF/OJ6Mq1a2QMsdXYb37B58WkoGVViIyg8TaEEBpXmSRJez3f1M651Wo1yG1vOLxx9fL777798OHDq/uXHj569D/8j//6tVdevnxl/6/e+qQqlv08ndWr7a3Jm2++cXBlr2mqqwf7ibUQPKJ6772rfdDVakWBiIxlYy1bmxpDsdkgrVawEmtARNQI3QjqiSIaE1QRCJEJmVqu7AvkfjubiM5l06MTIyL6xsXP6JyrABBbBAaoQ7zoV5k2pNLu7iURH0IIGpcNQZE8gA+wKqrVqizrSoKKQOMlhADEAVQFBJDY2CQ3xhBRVVWtAWCLH1h3PrtRhLa/VdC4W6Cd8bSZGEiEb0ZR1k2tv3YdAVFQUEh5rZDFai9UtOSiAcNsfx4rdnvFqHt0RiuMgi1hWGfGLYTIQ5fRrL+SikVE0RCCE1EATtK8lydZOp3N+v1hv9/33i+XSxGZDEejydZy0QQF8ZGjJDKFCyLWVWUJjTXMTAyqCqJBYLi17UVFBJmttUQQgnoJf/a971dVFfv85unxybsffZzk2Q9+/OPhaOLE9RjqVWFsGrwn1DRNs9Q6CZPh8ObN66L61nvv3n7tFZtn/+q//x9ee/0Llw8OHh+drb0yAHKUfI0OjdCLT1Njk7Soy4dPHqMoWzqfT/PT7M03vzydnxtIDq5e+eSTz06mZ4iIHX4PEdaXtwPaazx/quqck+DjeSciY4xIIAAJoIpPD49OptMr167vTIb9Xra7t79/cK0Wc3DwyXIxnUzGgyv7BweXblw/GE+GjW8ODi4fn50uVwsXQl3XJk2QTONdgpkKqAYvoXax60WR0Yy7dn90xoJRnDry1cfTG2GMrcxmi/jFSA8BsQJxzn2usAEHqhqLsXXkVxAQUJUkJeoG3EQUiZUR0XDCBpMkyfJBwkkIrqqq1ao+mS3KuilWlXNOFYFbFrYO+EjIiEgCGBRE1DC+8GhdR3xz7etqh+OKdc7PMvArQgihE9Vsq8HYlG9dScAoahrjpCKgMTGNvPhJFDM1yc+1wHX027BAQEWKJOndDxHX1OE/p4ZEFQPaNA0S9LJBrz8SpPlidfj8+NrNG48eP/nJj396Mj2rVoVzzlqb9oaJ6QPZSIo7GQ23t7e3trYGvczazBAbw0QUQqjrummqyvsnsyfLqp63yz7FqiyKomiapizLqq7rug4hmO/8yZ8UdQUAyrQoVuOtyfRs2pWnRHGhJLi4JyDOf+dPvjMZ9gLoD3/yY3G+Pxo+eHwnhEBGFQJGHmNQA0oYENAYU/vgRRvvoIHcuTS1vV4vrqiSNWmeVWW9KovT6dl0dm7T3ubF3bzE64bYWqyYmROyLnjna2bMszRLjIgsl8uz08Nhf3Swv3v300/Gw/7+5Sto7WtfeOMXf/HxX/3gJ1ev7P3SL/3ScNgnhun0bG//0q1btxbFKoQQDSCoGCbvgiWUKAwrygJsMFLThwYQ1cT9clE2bZqZGJK2YYGKogGDCghyqy4GjBqb0wGQQNs9t+5jrnnpLz5s14KPspRIpu26QiDRAMgIRHr3wYMsy/qDXq/XY6S6rlerVVnVq6YJiohskpTb2qbb8UNe6yuKdyEEVEkZorBae8hbEvwW69i+t0j5BLGbgh0PYnvAoVsRjiMDQsVOZJS6XJZict7GQxIUg0ZQ2k+68dXgz9eNtARKUdZK1pqTGHftQanTM42ZNADQhjRdd6IIQNRXeT9DYxun0/kM0ObD8c7la2+9+96Pfvzjd955r6qqfr9vjAlBQXFV+KBAANbafr8/mYwnk0me55ZNLImDet+Eqirq2jXOPT+fNaIaAhBZa5MstWyQqSxL32pmkJmuVojYNE1cPsgH/aJa7ezsubpJEluLr1Yrcc4Yc1Y3q8X5qpht777xVz/+0c54vLW38+lnn6V53u/3yyAkESanjNEII+NGajkB0dI3JjF5v2eZzmdnWS9L0/TsfJpmWR3Co2fPPSDbVLs0Ix7EtQVGXg9EjHtPsSxMGL33GAKAGJOQQnBemMRSYrP9gyu9Xq5kZqvqO3/y5ydn53/zb/7tN774+p07d3r9ZDTujcdDET+d1fP5+ZWrlx89f1rUlW+aAOqcC4rOBU/KwERkDJEhsmwTa4mqqgaEAEElCGjwHZIGjKqKIhAqMhAgqJK0SzeMjGAIqJV1bh+hUzZUUFFQhcjziRjVWwAxYlSwDrFaIyJDzNZyaiwzs8nYYOO1OJvVddmySLABMoZM3NvE2C9xTfwtkHCkSqE4ao0D9kBRobjtCQMCgkiHTQNtFyCihwjYdSkVtFOpi8qQoLGd3+r3RR+EHLfkY/lMwBRfjahrPcmFxFVsDgtuKNi88BWiQm6nkxu7wS8M93mtSUNk1jF88yuwqXwA4TTPtye7PsDZfPXw+NF//2//YDZbOM3644kxxnvPien1eqO9nvPeNY1zrlE9mRfTsokpcZzTBPUgraC3Igy29kwQVzdeglf1DjiIgiAm2pUSBlLrQxjvbjsf0l4+n8+TJLGWqyK0/WgNTGSYXePndaVGP753Jxv0Rjtbn378ab8/uP3yG1tbW8XxGUiEEYgBYBCGFtllk1zUh9IrkUkYAeu6Zub5cjb7dDXe3irK5snzZ4QGmSQIbLDix3MTHXCMfqFj2okP11SpsVnWI8KmLr3qaNDb2d69cePGeDg4ev50sr07mUzOzs7OZot3P/jgl776tS//whePjo7+9E//eDgcvvLqSwrw/ofvvfLKa8vlvGpC0VTOuarx4JSIAIOgsBKoAW3AS1BXE7FhUAgYBJEkiLY33AfqjNCwTYAUkRUgklkxo6H4X5SJFJT2vK8fcUvAe6+dVBPEpR8ARUgSE6MHkhIROy7JEaExJlEyJkGM60jIaMgatpHBQUWCKqgEQLUt4BWIADGqpgICEkaqlzgeAIrZdBsY17Owi94vkuGINGhVnNogoxHQGecxHZ5Bo2GE2DNQgg7kgu13EWGzBoICAaD83HlgdFyI7aQ2iuHEsMjAEd4Z06l15FSNrSEUVVTW2LRByfvjqqk9AKU9T/zg6dFf/uBH77z78aqoFY3p9QLzqmzq0lkLgVSgindTsFPi8ioYhsOeiHhxIiwoiBYRgfB0VWjLK9XySCgzEa2qkpQYkYGMANbOj01SVMveoF8VJQOXZelDUzeqAbLU9rJUVZUp7eWLplBCm2eLsrh09aCf9z/66KOrN16OM1CIK1sKTMogEmel7VUG772IDIejgytXFovFarWq6uBVRMk5ByhN45gMQIv6ITJr8yPiyGtARBH1LyK+acaDISGYSMwqiTFmb2c3rhfOzqcffPRZZk2aDa7duNnr9e49eDQc9FVDUSzPZ2dPnz2ezU+3d3cePXoync7mxYptVvlGiE0IPvisl5Np4RdBvQTn2kEJ5Hna1YRkqGWqQkQNKgGCKmhQCGhIwQugbxxRC85m0piUAkqcua2NDREjHcu6JtQuGY+hx2umKCC4XtiJ2Mi6rvM0zfNeklhVDeKCq1VDr9fbOLjE0BbV3ZMKtJV828q3SQItuaByXBFHjIBOgFYZENuQR6hgusQvpjAtXBpal/GiA1kLmMU+6gvCdfGb9m2u2SEuYu7PecQB6TrnVFVgZLDYdW9VCbCFk2krlh5Jltp9ciFsgjaAJ+ezuz9978NP7j9+dlI6RZtxP3ONFC5IJURJOuxZa12MeAiESczNvUqk+WqQgCkwqqqXlp1bvKKYGJchNgGCU2zHPxEcJwAm6uYsi1UIoa4gy/ur5RJdQDJN7VV1kOYAsFqtvPfjdDIeDJer+eWtnX/0D/+Xf/z7f/jhhx9+8Uu/8OjxEURIFwJoUIqjSYr8JyEoqDpRbJrGy3iyvbd/6eHDh6vVCq0HQGt5a2trPlvGU7WuCYkiRAtiahrxynF9zhhTl1Xl3O2rB6vlvGkaRuqPRqNBf3/v0u5k64MPPjg+PvJ18+ZXv3J+fn52Pm+8fPLZnQ8//HA06I3H46/+4jdms9m9+3eU7Giy/fjx0yRLewmigmVDCq6p0sQ0GoiI2DIzRaQuAAAsVzURJZajR7AmSQwTGXFx0TcItAvgLdevd4goRB6VMfLddUj/TmEEYr8hLqp1OhOKyMCKyIiKpizqONxvW/MUT7AM+qmoL6qyqEpmJoqOmoqiIgJjksge7TtoS0TqRKinieBYY4g6YBoAAIfWMgS0uy+tPmtrNsja6hh2FtgaD6oEgA4m0iFsJMIygeLTqHZ7ENpK817IYyDG/Vr23qO+2C6PFkhrK4w5sCASqTIodAgKiV4MMEQhe2QhQmLteqpA/Oj503c//OjHP3nr8fNDNnk2GCknZenSLEl6eQ7svdeW0029a9r6mKAb4SIAI8J0tjCWbJIYYwwzYAgSq+I44LIxIgf1qqgasqwHIEFRxOPNv/FboeWhj6MniiV3CKGbR4cWem+tMVRXVb/f72fpt7/97aZpvvtnf2qttUlW1aFsGmJblqUxyXg8dnVDhkPc3aZ442k8Gu1t74zHY9Dw/PnzxWKR53me56vV6nw6L+sGTco2icc3xnvnXF3XLSKeyFobF3BBJAQ3ytN+nkEQH5rtyc61g8sIcHJyXBSFc/VsMTfGjLe3gnonYTTeWhXVycnJYrEYjQdXD64MBr2iKOaz6Y0bN6qqOjk5Oj8/7/V6e/uXrLXLYhWQyGTEVgEDIJIxJuHEivOGmFFRJU1ML0lAfFVVjBSCWhvVzgInCSCuVqsAGgvdoCExNssyRGxclfAa+/azDff2BEdIVSxyIhQmeiWiCARQUa+qHKWRGCEm7SGISJakcSccCQiZYhUIERX4wrC7s/k1phmJ48oPGoKqKgyztSYGcBEhFUEwxogivPhQ/DysbN0LEYn0boAU89z2a+x4w7r503kl7bZVoq8BaGObiDfGEGicbBtLhhMEgFoZmK0VQB/UZpkCTWeLpJeRSdKsb7O8KMu79x+89dZbn9y5f75qAkDUjSKbgJIL3nsh5OgiugJTAUhR1qMy7KBnMVNzTYgtemMMkYmlhGqoXWjHMwyMhgwyW8SoOB1EQMQbCAJBIER4Rdtt0gjYpQjLIkURUBEvgftZzgJPHz/7wff/KuvlADAcDhvv0owbEWsAMDGcIrMQk7GkSqZt4jOSD3o+XxZVw6jzxUq8WpOmxjo2lrFBqH0TVJitamA0RG35wWxbpAWphhDEi4CKE+HVauHqGhUm/SEhagjiw6XtnT//3p/7EL74pTeD+MOjw9HWZDKZrKrT/niHTOq9f/b8OM/zXi8fjXdE+fr1m7du3bp//+7De/efPny4vb3dH45WLnjv1YsoqWJQp1ghtty4gzxPLaOGoigZILXJ1atXY84sIlVTN00zWyxWy8VwPAIAJ+2aIhACYXAhhMDwQvCPx5e6hge2K8MtezSqdg0q8d4TUZKY1PbrpqS45woUMbcCwIgRTgWgGJuaSqAoiGXd/KwRKgKzFW15R9kDMVpiArVsAGPzFjA2C1AJ0PlIP/HCU2E3TP+ZABa0gw11lWAXa/HnWCC2EaydMsQ8M9ZX3gtioJbLWBGxaZqmrg+291fzJQQiw6vVqphOx9s7l69dWRZVNhhUdXjrJz/97p//xad37iZJsr13WRoKIdbMSC4W4957n+c5gGIb7eIdAET24WJCGLHvxAYYQrNqVTtCACXDjEjKhshtFvygGnyz9kpMaNgaaR96cdVCey2wPQrxrWD8/FWxGg0GhuD45LDf72fWJIlZrOaI7OsGgY2xiU0MgSHIEuuCjwtyhECoEtyqcKsCemkWQrCtiCk7F6qqqarKk2GNb6atHgBEQaL4nkFSFe8b7aC/LvhQu6qqUmNFJE5gTqdnn332WRB4/fXXt7a23v3wg+l8nveH9+49yPJBxhbSdOlcsSx84wghYXr04KFF2Nvb293alTqsViv1Op3OHJmAFHtFhhMbHbyCBZR6saqXZdvBw36eZelgdn7S6/WSpB+CRij9YNjL83y2mMdSTYLrkLEoIpFttAXEbvSfTKuSdwEHiefSe0/EiBArZGOMtWyMGY334l9thCBB1bOTU9QXwmy7+t25880w2G2Z4qb9iAiAKpGIqqiIxFUpS4yGvXexrbkeKcVn2DTC+HLtseteRV9MMgla8iVtu5ygGlVkBToTgJaSEFQ1SRIAabwXEZuYNE2sscx2virZpF6xKur+eOulg6uND5/cvVuU9Uef3Xn/g0/O5vMkz65cvb5arR4/fT7e2UfCyKG4vgXrt712jhf+RT1oOyHr/oYQcTgcxrmPiPjQhE5t3CTWXHT42/urqkiEiMzExOZF36MXf6vadSk78AQoAJRlubs92d/fi/J6SZ5e2rs0Go1OTk6qqrKGyLA1BKKokhgCpKDe+RoADDGzXZ+SddVX13VVVa4J0YvH3TxAiRYIsV8eBUap5ZJVCEyWGa1JaY1rY1qUxXx6fjY9ny3nb7zxxptvvnl8duxVRpMxED569Oj6lZt17aqqUNXRYJimqTg5PDyenpyWy9XW1vN+v59l/TQbLObn0+lUjSobYyPAuom7SJZx2O8Fr1VVVWVliPv9XLQ5n51UVVKUGSi64IlIQJvae20paONKHyiE0K7DSBMCQZspMG8awOaF6u4JhhDHhF1EBWiahplPz45bw9s4L4yYGEtwYcPrs7XuzWzaIVDLQ9SG1Mj9hAAgjRfGuA0kGol0VUkl9jzWI6V1SX8RzX5OPLwYEqzf1edstf1JSxsTsAVzXrRpiAiREUXAO69IwowKnGaD58+P8jzfP7h2Pl/87h985+7DR8uyevzkmclyNjbJe/PFyoVFnvX3Dw6KUjbTy/h1fXc+9243745qzIRbkbnhcEhEIUR0TvsIqiGEiIBbX/D4iOlSfB7zuRdbv4aItLPUOIKJJoq6PRmBRq7LJM/zwXhw9crB1s72T3/6UwAAiBUCiYJHZCSAACIdUguIWt5FEBUfaq/LZUEKTe2tTZM8q7xrl1s1NtCVQJnAuZpNvLdBFYnAMBpj8jwPIUmSBhWKxoXprCxWSnjj1m0P+pN33l4UK2OSqqmfPz8iovn0vK5r732WZb201x/ky+XyfFlkWR6Cnp7Pjk7PmLnX62U26Q/GyyawNYSgwTVVheAzQ2R5MEmHk6H3+fHxaVFXBElTN7PZbHd7ItKIQG/Qv3JwBQifPn02nc1CUEAmMtbY2LOOodyFRiTSCmsIuna6REIEHePwhaNEhQhPU1VEDUHLUuq6NnbN+ozQMlwwATaNv/DoQZiZgqyNcDMMIiKE2OJt6QIElSSSuAgBgkFCJiIglcibLS2GNgKvtSV6gHXDE/GihRmZ0aDtxILGnfvuiIt3mwZ54TJUI8QPu+cMgABQlE2WZdamwMZ7XzdeVYOHJnGTvYPRaPT06OT3//A7f/XDHyvC9s4emKRovCsaUeQ0Z0JVWhZN8B0miQjVEJGEIKFlDY5dHl37CIRWdh7j/JNEJLbeFgvYzDViiRtUi6KIKPDoZ2EjHq7N/j8YCUMIgBgi2VvMAUAVdTAcFaslGe71MmvIN8309Nha9o1DlciRy2TTNCPAPLVls0ASQ+v2tIgCIuZ53jRNVTUiAgGKolLVxGTEQCbWFIoUkIhYMCigF8H1dhmiQQoA1G2s175x8+WC48JAYs+XC1tXi+W88S7P8wgR2ppMinJJRNYYVS3LMjpvm6YtGVHwvnGN86LgUgXDSkYFgZURbMKZ5WGa5CmPc94aJk2DJ0fV8vykLFe9wTDP0+Pjw5deur21td0bDibjwXy5sgZ3tibz+SIoMjOSUcXG+9juMvlFzrbZmNmMiptBTDp1JdWWST0OfpLUrG2J6OJApPyCn103XTfjz6YpxqEEt90/VYy7RaAIJKjMTDE6RVk22LScTfv5XHtp/aLrRO5zxhZJqz73QBADSJFPre16tpUtsw0K0oQmeABIsjxJEsJEgs0HkzsPH/7rf/M/vv/Bh/tXDsaT7afPj1RFMA7+iZQBiA1bYAn1OjVYG8lmJFxfmfhWQ/AtnKC7U9GcpNMnX3+uWONRjCQXPKjwuYY/EbU1YQgXQTnujndVfrxM6+E4VHXpXD3ujfM8V9X5+bRYLpyrF7NpXVbG2OACW87YGhu5WYAQVFoGa2ZI0iRJ0uiJI6Oud6GoalUNCGhDBESLROIgBAQizBIbQhAfxToIIhM5yfkixP0aDcE5p0HS1GZZtlqtLl++/PLBK0VZLpfLwWSMiNWqEMA0TQ1y1TRlUwYSAAgqRdMQkSJymlnDzFwHrYsyyXsiIQ2h30v2JsP9yXB7mOQJM6iCE2wub4/S1CrZfDDM8nwxP791+wYizmaz5Wx2eHJyPluMxlvj8aTxMayQcwFVCShhY9IM1nVCtzZBRN77uMrf1kjdoY1r9XENKASNWU28nRuJaMRqkrajgDbri3+zznd+bqLYtUribJ3jXoEi+BBifebbKVwErGMILh4MxK6LgYiI0uF8WrQPEiBShPNtIPRpnXn9vDlENDiNzULCtmYEUgWbJEGgCSrASZKxTYNCUfsQ8Pe++7tvv/32+XyW9iaLws+L41VZbm/vVk3jfEADhhgUimVRluWwP4COwGGtVNVe/9ajbRSEiK1kg8RPJ4REzBdOp93lbps3AmpaEmFdl3kx+DNFxhxUkdYItesHrI3QWksc96Nab8kIhrGqKmvMeDzOknRVLuu6TtN0e7J1dHQy6GVMiUpFqAgiPpSrZcKmUed9U1WVKnI+yLJsNBqdn89Wq1VV1cYkCBGTjczc+Bq7UyKojAigSJqwcU5d8N27jRkuMbULhMzsm6aqCgH1Enb2dufLeW/QG4/HtXfWWmPM6fHhztZukhgRYAgKEDTU3i1WSyTK89ykSQhaL4uI0O2Ph15F68qJJzZbw97l/cl2P0tIUf35Yj4Z9F5++eVsuD0vqrP5wkv45q/8UtM0H3744fHx4aW9KzevXdualHl/AEplXTe1bzpxyA61AtKJk+FG5RZFVNfVSGcz6zlEC2EHgDRNkySp6mKze7626qp23bz6hR5mnJHAz0RChdiFR2aG6ItVAMC7EAgoCKkAEDMashwBGbEXeXEUOVo6tDsTGrX+AEhRRYTw4s2so83nSqaLhw+CsamHgCQtOo2qOpAxWd5P8syYZL5c3L9//+GjZ3/0J99flU3eG4xG23VdV02dpb2dvXFRlU4wKIZGqqYkImPseJwt5zNsaRxeACqvWzXrDCXmJgkb5733kS5duwwc2xK9i1ZEFJ/TS5COaOvzl3pdah586+9WVeW9EFFEqGALvgObsEESCUhqjGEE0JCB7u/txgWcazeunpycTCaTLMsOD4+fPHs6Gm6dnk57+eDll189PDxe1UU+zmarOSlZa4mMMYkEWCwWy7KMFP+4BvopAsuqmhPLen8vDrUAIHjf6/UwztwaF7NH7yWxOXQN3U13Hu9rhOCFEKqq0hCIICiyQWtSZvYS6roOoEw2qKRpytZEoQNrLTKhwt7eXqjrs+Mn/YS+9dUvvfbS1ZRCQhCaGgCG450G9J0PP/vw0zseeDAavvbKy08fP3z+7CjJs/F4p6yqEGAwmhRlHVSDVyVmsnGatL4Ta2gebGRoEWK/bpwmSZJlWV2X2qnqrU8wAORZv6qqyIrfKpCKAEovzT7XeIgvESFH6yAQ/15E4ved+9eYusc/bluv2sqpGWAksJbZYFzVjbDrjoQpbvq3S0zEgMCESuKpa0S1MbD77PHUds3hVo5GnDfGMFsXgnNeFJMst2meZvmyLIqimZ6f37l3//3333/y+FnpwnCyr2AAYk+pI+QV8SItmSIxmfaEgwpI4HZH+aKhEoPh2iC1291Bw0mSdMlj5EGPijGQJNn6pqh2vRwAAdkE9G1aoK6XY9cvs/EUF12g9ucXfhQXi0W/nzdNowhlWQPAcrl8+vSpc4GARcQ5Z0dpvz/MskUTmuV84XzNZAHA+5q5igK3bf4dFQhA4jvVloNdoh+Kj7i5PBwMon8yxmQ2aWXl1Hnv4nsMLeoiPtRaCyAhONHGex8k5Gma52nZ1Mgk4sumFIAkT9gm8cazZQB1IYhA5WqpQbybnj1/5datr7z5xrBvt7aH8eqtyvLypb3FYrUsVoUP3kuvN6Ak7/f7b//kp9ZaIjo9Oj1+fjqabOV5//ToWJCI2ZjURLL5ELxf83OFiKJoUTHIcZgbD71q9LmhrtX7pm7KTW+6bgNEC4xXL+oBRwNL2KxrSICLWjHOGKN3jz8xnbFthCldp0gb57KdWAZFVAUXRJAZNt6SgmCraNYG4Xg3PYJYBgoagrZYIIiM2mE83mJGVWyayntpmibS2G+Nt8q68nWdZOl4ewvZropyNl+dPnz6wYcfv//+hyenp4KYpOlosnNpMH52eC7Q9o0jtAi49SlxKKIgIIQGCQgIG9foxorW2hDWRrj++ABAqmV3kS9cGyIirstIVe3q5cjs3S6RRrNaf12nn6p6sb6+fhPYmf7aN1CX6QNgbzhM0lxFyCAZHozGzrnFqpAASZJ4L3XlENE5N58tZsuFmCAgbNE3YblcImLW62MrRgwBRSOHsUTBnRDEiXhx6wZgbPBSYi0RkYKqOgjOudZ9chJAQJFRIZ4ka2LOBgAx8zYIariXZ/1hzzYWmWrvtFBEzPOMCBsfvS8wc07GsCXLqiDe7Y3Hzx49/HT27OWb17BeHD8PV/d3b928cffhk+OT09r5AGa2KhXIsAXFX/zFrz9/+mw4HO4fXHn+/Pknn91ZLpc2ycT7dddFg4iE2KFwzkXkhGqIhT2RIQLv45I4ifh447z3jfgAcfxI6wQn+qPz6Rkzp2maZRmzRWRjTJras9k5bg4J290DjSylbIyq+hAgiCAYhNC4DRoBjA3Y+HLMkdMRI5NVrBaDeCOkuq6doEvHNpz4OmgDqoSAICHin6Ulg0c6PZvaxDCZuK7ExiCoBjk9XwzHW4Nxb75Y/Pit9z/85OPHT54tl8WqqJoQDCe90YTIrMry+OTcn8w5GbAlay52JgiJiRFhrRcubc+TAUR8Iy+2l6I5tOQ62K19rTtndnOWSESkGABQIm8q0GYwUwRijvRtMbDH3yCi+tCGt2iEPxsM22iibbFC3M1hVUmhKIqqqsbj8WK+UgiuCWVRW2sBsCobRE6SbD5bTKezJjR5P43VtfO+qqr26HCLUQxr9yNR6SYOTwiZYaOTto6Nxtho4TFPS23ivWcAYI30nDazNk1iyaQaDLFlFhHxPqpaWyZgMDbpZba9uCqJsePxuJtih+g+xfmm0WY1vX51NzV8enJ43NRffvNLO/tXpsviL3/409l8qcRJ2gsCAsy21CCP7j/I0uTSpYPtyXY/G145uH54ePzWO++keS/hJMp9NrX33isSEXjvRHyEL20aoQggMiKLeEQ2hgHIOU8dm8M6lsYrEzHA8edN42PTmIiY0SacJEn0quuo6JyL0Y9enFBnNoktivUMI1ovQAs3RcMYfUGkFQVVvmgoxGMTnxk2DvdF9GZSDQQsKAQMFDX91DAFCHXtKlcxsElNwomivXz9xief3vvBD373/fc/PJ2eMdl8MMiyLB9uGeeC17IO3jciYpJ80OvPV43hJM9TZvbeR8ktkZBlGaBIEA1OtNHmIvNff3zcaMNcKAq/aJwGKaCuI6F2w4YXFmHWD9GWjOjFxzq/iE9i1gXAzz7DZiLaXV9SgFVVqepoa8urzucL57wSCWJRNdWq6vV6WZovl4VzLuv1QvCN995JCME3QRHa88EWAOQC8UQxjFtiIAaz8R5EAcCggQBOQ5Iko8E4SaIQrJ+dn0dmITLEhlo+HoVYMTofCKwhJkZV8a5EptAEa22eZV5DWZbW8GAwOH36qAX+MVhrkyQhBfYNoSxnU+qPXrp17fbtl69fv37ns3u/8zu/M5lsOUiIk0a5qpvgfYDGVXXPGB32WzjeqD8iTJP+dLZ48PCRCrCCqtZN3TRN8IqIQKoQGUmF0MQsChCMMUgKKkiKoIBCSDZh1/YDIrStpRSw1o6Gk5jCRftxzjWNC8E3TQVKwWvwzTrR4ggpBlBBNjZN0+jamqbxKqqoEgQBlIhaqu0otiCR0yliukUBIGEFkBD1obvDSkTwIiyujcORL7ClHI5wEIhbPiGoEggQ2yThNB9kCace8f/6f/vt6Xw1m83q2qW9cZJkiljUwVpbNUFEev3+eDLw3k+nsydPn2/tXIqeKITg6joGNGLwDr2rQ9NIaDYNAPlCiAZiJFdAQHuRxGpkmsG4IiSynooCxF69wW5PqIW+63ohG9utBohE/hsPjG4LMOqZxIdudK7XZT2+6CGCamTRyPP86tWr8/k8quolSQJKRVnVtcuyQVFU0+l0uSwEwUvTNFXkXInet8HGew/osQOkIzIRAHFMfrAb2nS2D6q6Wq2cc5FlYG9nd3t723s/P58lBiPBBBoGipbWNI32+30VD0ECKLFhVGKKu61No+KdK7xzTusK0xSb6vWb14lIJJRlWVeFX1YuBB+qg4NLbCYvvfzqtRs3Hj5+/k//m39xPl+M9w7YpGmGxiTeCbhVYthaG1J3eWdnenr22af357Pi6OT44cOHVd0AQO1dlud53ufEqmrjnXci6rWjcmFmYwIrKyspWYt17SFyGYI0RYh/E1q3Td0/MbEEjdeHyFhr8zwfDoeqoCqPHj1SDU3TRIcdf9Xr9fI8XycUSZIAQFVVAKAQ1oevxVwTxaiiql4C+XUMIVTxcXey+yebVrf5fcxNBLH2AVEZPDAxCAgxBPFQrYp80B8PhkmeMeCyLA6fPz46O3/7vQ9N1k/THC2XVbOqVmma5nmepLkoFkU1nS5ms5U1qTXp3l7fppko1nXdNE1wDhGT1FjiYjEP4sSHC1llIFFt91he7JesA0B8rHswiOipXTHb/JgXJv35ASyotvyQmxmvrmsJREQ0Lx53hK4klcgBpnG1LyYkqqrOBWOMVyjK+snT5+fn5/F50zSvXeMk1M4dnZ6cT+eNF3e+4ASi5QCAcwFRDKhzrmUri8s4ZKIpRh+GyIieiLrNY4rhfjSchBDm8/nh4XFZ1kS0Wsy3R32bEFIS2eZEQxPPFgGiJtYwYQjO+cYQ50nqapdaE0BdVeVJMtnfZ4NVVSaovTzNEwtbY0LNsiTPc5OYxi+2L+2VdfPTn/7o0ZOT+bKonCxOTiUQoU2STER83Qz6fWtTRH744NEw7z148PC73/2T2Xze7/cF8PT0dGtnh63Jsizt5Xmep2lqEmsoaZoKQZmoFdASEdWIvW6qioiywQAAXF2rqunl/V6/aZqmaUS0c/mVqoJSZJEpiuLsbNrlS3HeGwE0ZIyJJ7jfb5+kruvYcYFOqjXLE+2m1QjKzGSYEMhYIAKIKHZVhQgJDiGsocUbh+/zsLj1byMIBCLrBVEEbovoaGsbEOerYv788PTk5PGTJ/fv3Xv89HD70rXzZXU6m2VZtru7l+d5VVXLReFDkSRJfzAqimK5XJaVHw2G/TxRAAkuOK9B2ua7qIo0dS3iQQOAUmwBKSBCEK8bK1Trz75uFH/Op0QC9s1P1OWlMbrGpDKs08ZILQ5rkNDaGjWqyiICmJbOsmsnAwCSIql4jxDxol1rW1VV07QXKeins9Wzo2MR6We5V2nmq3gIXAh1UyLTYNQvy9JLAGI0EcUjwQcBGzQwUIc/JNAQ82pENGS7WlSRwWBkzeSt3b3bt25Zaz/55JO7d++en50OBqM0Tc/Pz5PU5L1e2svZUBAJvnHez5tq0OsnaZIwr+pytVyiaJOkq8Xy0u52mqYG6dL21iuvvEIqT54+fun2LWvIOXd2dnJ8elxVpSoAwZVrB8uVO58tz+arNOkHX9377O7xdJ4mPWOSLOvFXtF4OBoOBr6uQll+NJ2enkyRaWtrS5Wdc9tbu/PFUlURNc2zra2t3d3t0WjQ6/XqJsUXhwexZmMiALDWDgaDWKyGEPqDwXg8WRbVcrmMg8S6rp0LTdP0s35oQRhtLyGOT52rmU2s0ACgaZrFYhGBe+uSiTuRwzzPq7pYn604YIwN/TWUXDXEKEmR/AIVCTEq70UoN7c1JHadehQFlS7lZmTsth+6kKuKxM+eHX744YcfffTR8fFx3TSg2gSs4BxMkmd9IpotFvPlEpEj5Hq5XEYGWiKOzfDVarEOU4YQmUGDd66uHIhgXNJDjMBJUQmgkVNsM5JrNzRaN43bXJqZiMqyDCqqGBtmqp/bF9nIOVU1fvYL0Mvnq9C2JhQniUltz5R13VSViFiTsm2h3wIBEPI839nZUdX5fF4syxw4SZKnz44QU1Uvavv93jIsxImo1C4YYwRlvpoHlbiCVTmnqmwsEgcRiQQqQBhhcWEDo4cXkV0wCHqOjgLkEYK1NrXm6sHle3c+XZzPbt++7SkpCqfsF8U5oqaGq7pMU1tWq3o2t7DvCItief3qweXLl89OTi//wpezJF0ulz40g8Hg0aNH8/l5miaHJ8f9LEXEoirPZvP5/NzaNO8Pp+f04K079x4+mM2XVdUUZe1VrE16aYLIKeJgOMiyTETm8/NqVczPThlpMBoik/Mi0oSgdV2jICgYwxpkenai4na2x7evX93d2j0+Pj5fzGOYCiFUTS0ip6en1hgVOTs9xe6ITKfnjx4+RWPjvl+sXU1iLPHWeLQsVlVVefEmTdigl9qVleWkrh1i25up69p7yTJTFMsYsSLOwRgDQCEE52q86KYCoPjGBecvegeRC4+JmAyxSmBAX1cBtN8fAkLtaiJiNhpk0BsyYFWUg34vTfLz2RlbYra9Xs8YU9fOOUdsrTX/7J//tyfHZ6enp845YyzbBAJYRsPsNQTn5SKi0maWSESJJWJpQhmaEFzTFb2MiNTm1SoQ1m6ukQj6I0BkY/XFMB49SGw6UDesX1O/J0kWW2KI0HIpYDt3hU4pTSM9mjhV7fV6LR5mY6qBAN55xK4Uuf3tvx8LQudcVVURhEHdHAkRkyTZ2dnZ3d0FgOWiePbkGZEholj7xj+IE8yqqiJtNhHF+i0CnaATGb+YbwKssZFrL6KqpCA+bHbnJN4A8d77yWTSz3Jj6cqVK9bau5/dOTs7S/MM4k5jVQZxaZqIb/I8zbNkOj1xTWUtb21Prl27woAPHz5OTAZKy+USUCaTSbzE29uT58+fXb169YtvfuGVV17J8/Tw8PCDDz64e+/xyUl9cjw7OzsLIURoLDKlaWqSBADI8Hq8VrnG140Ex4DMdmMBF1XAWI4ILzbqfQMYtra29vZ2/iff/hveeyTKsiw2VIq6cs49evJYRLx2gKb2AUnaZ5saIu99bORGebPVamXTpNfLyJp48U1ie/nAkI095thhjjcoGuRmwdadWgX0azqYdtW9I5VpRxyIkVffWLbEbrXa291mkxR1FVnqkEzW7+VJtlgsMGCepBqAFKwxAIKpLZsySRIRsDbd29v75NM7//yf/wskXi6XRVEBAJmEiJgtEtUhrDPAzRossvHGgxSn/CGE4F1TlevA1VH3x7CzYWkdW7GA2jTpyuwLNlfqFil0TTnZ9TN7aU8V/VqxFKJGAMai13el4+eym00wRvzh5iQcb/7a3784RlUV7VA3mM6YeTgcDgaDmAD45iKNwY04niTJOrdZXykiql3T6s29WODKBtZxM4CryKYRqoZItJrZpG5KZg7Bfe0rX/3a1752cnKyWq0Onz05fP5MRFAhTS0bLBbzNEvm59PLB5f2trfYkIhPU3vr5s1XXn09yYbzRXlyclKWK+fc8fHxfD7PsqSuaySNkLosyxaLxYMHD548PjyfubJoIq0GWwMAbE2aphhJbiTEDwXQsvGpeAaMap+xyo0H2lrbNBUA2ARVg/MVIqap3du5PBgMhqNRpAtARCch1syq2q12aryriJRmgzTNY0xrmgYRk8Qwc3wbiGizlLndPUXEYlmmaR6vcwghy7LYBF47/rUdxuoxzSyAtGK0sKlKBkQQtzoRlQiiEV6aTMbDUe2a88WcyLAxjXcikiSpiITaJWxGg7Fl1iAmsbPVsmxqERmNJvPF4g//8I/ee++D4XA4PZ+FoCporUVjEZHJmMQuimUMNev4HK9Sh9ZoGQZEpGka55yGC/kAbBstqKrMLQO3McZwEs3MSwDS0CJyW96GiHCM13ZtRa0hCaYmjcRtIuIkRAMxxgioSLvfqB0MOF7zCw+60QHaHOpcbFFghx6OLnMNZg0hRLrSuq6Lohj2h+tjt7bpTUN/wbraWN2CYuKnWQe+uMEdF8VjggwAaZpG6jjvm+ids6THBkPjAO21a9di6fjpZx8/uP/IMh49e1KXq63xJO+lScKDwWD71rWXXrq1vTW+fHn/+dNnz54/+dKXvvilL33p/PTk03sPi3L+6Nnz4+dHlauDc8enp8VyScb0skQQcLq8//BpuSznq3ld1LXzoAkRZVlmrQW62FVd5/TrpIiNSdiUhRONY1m/3mboToOqhuim0iRng9bas7Mz731V1zGnGA6HSZ5FfXYiYsQ1WxkRARK0UPh2n3CdUAyHw0WxqqoqVGq7QKeKo9Foa2snnqHpdBp9ZWyKbiZgnWPWIA5RiQyhYRZm29KsIQIQtLLb3e1mGQwGg8GA66qqqhC3k733IrPZbH/vkpikaRqbmn6v15TtRrm16WQy+ejDT/7pP/v/PH9+dPXq1cV8ichE0s5LjBURCRoXRGBjpxw6oKkxptsnCbEyjOpdWWLXbr0bRVzcrPbQm44A0qsXp3jRTOqycIjJwro30wa0AL72xthoJhQ66s0kiVkSeR+XrWPSsRldNi1wbSZtVAeB4IKGmMgKARkyBCQxCQeMrDYgwMipTZeLOa0fCADKhEys2qbWGLkzFXwnqg7UShxrkKDSjSRaNup2TwvbVoAxUYgPAIBQmZAYiAAN1bU7Oz2uqvIpPplMxnXd9FP7G9/6xjBPr1w+GA77N2/evH79+tnJ0dHRERE+fPBwenb22u2XM7b/5L/6fx4eHn7jW78x2LlcNc3x2XS+nPnGeQnGZmzM6XTe6/fzLEOPjavKMniHSAmCiVgF6MATGsB7H9aYzxBlJ4GCBAgIfAGNaE95XEr23e2M/bcoL0p108zm89l8LiJpmlZVlXakIWTYWhu7ZSGmQwiEEJwXEQkhSuV4gca7+bJga9Ksp6plWYvIYDCYTMaL2ZwZAZDZTqeiGuo6EmEk0DblIfY849uL+4eEwKwiEMJ697od/RO3n4iZQfDu3buvv/raZHtLRE6m59PpVESyLCOAuq6C9yGE5Woxn53XddPv972HNOt954++++/+3e/WdX3l4GpVN1XVDAYD70SJ0djEpiGEMjSxJ4xwkS6tY0Y867G9tI4B0WwAZc3Mv3H6Y9OhbT0CClL7KexGQr4OoRG8iu26SpvJi4B3EW7GqipxbANtkYwIqWXL6JVbcI5ITGtpI93reh/StkAA28R685OsjXX9feh4NagbJ0ZvsZmUrv84xnRjjHMuigqCxp2UKFOBJmoQaUTXoyK2cseIAUPwDTIwG6S4Re+ccyIeRW1iEOHKlSs3b958443X87z/9OG9L7/2Uqjro+dP/+LPfvTZJ/vf/OY3z8+m77zzzrVr12LZcO/eg2sHV65du0FqHj58tOt5Nl8GEVGu6ipyaxbL6tL+1VVRHB5Oz86nq8XSBc8YeXugi9NdKcuEiLKxG7a+Gr5xiTEal443uvOq2jSNiYqexKohBB+8qjZZlq39oqquVqtVVYYQZrNZkiRpnkUxxlj5kLFoDHQ7CjGRcd5HKK9iWyAlSRJLhsVigYiR8mMwGDRNk+e5tGLjax/x4i5F9547yOSaL0OZmYjj30YNFSaezWbn5+e9QX8ymeR5/+j05OzsrGmafr8/n8+D94PBwDk3m81UNe/1vOA/+3/9vx8+ery7s3d+fr5cFv3hAIAkQBxN68aYrrVnw7yBoIptkoiVXSdlXRszEjfiC+N3pI3v29xtXePhhrvUbj6x/mZt9mukUczn2rFnzDyhW4LhlujJsIlnL97QzQCoG4+LSBhJ0TdbJp1T4c4+291hjR32NI3shnGQGFGa0fA2WjUmTVNjKAQHIQ79gVQUkGODmUmDRIZYIMDIoCyKSCIeVRWl4xEL4FVBsiQNrrn37OlyuRdcbVm//vVvTMbDH/zgBwd7ezs7OzduvXR8fPyHf/Td87Ozoig+/vQzVV0sFsaYS7t7jXfT03Mx5uHR7zvAyWhkkiw0Luv30sR4rZ89P54vl8vZsvbOct7LLcUddvWEhF0WRB2k1nS1+zqBibhEBIaWdzNab7yqLSXzOlc3xsS7GFcloOs0FEXRHg7TmlPMndI07fV6SZqlqU/TlJOUmDSCvyEASjzr3ntjTDTsGCV+/Vu/1lTV3bt3U2sNESP6pjHrqr1dBOxoabpkJoYLBCVqG/pExEiMMYqoqoiKQ5em6Ww2Y+ZLl/fH41Ht6+n0dLWYM4F3NbMNIZQiaS/33j96+uyPvvPnRVnleb9pfG8wlEVRFvXW1tbZbK6AFhmBvKgPrfaWtTaS6EC7fRowIlBdAwAEF/lX1NyRFjl9Ad+BljGttUGR4NyFQYoGCCISIkxt7W03jTZ+E0GzrUPajD2iXr2qYofJXofQtRlv1CMXu6Nr439hs166Z4n9lc8lsjEb8a6J8Y2Zsyzr9Xqx1u/1elFryTkXE27vvavL2LtTaN0FACi397Xzuh3mh1qMj1d1rhIRQGXm1FoiOzufbm9vX79+/Rd/8Rd9Uz169Ojg4ECC6/eG3/nj756cnEQftFisVovl3t6e9z7N8uCgqBvnT4EYwSZJdmn/gNNsPB6XZfn8+aGWFaEtVtVisarruq4dAKC1rITMbDiUQTdWXdaHO0mSEIILYZ3GXDjXFzGA8dr2+31RH6FhRGCMUZUQQr/fj0CiJElUNXKTp2kaF8abpimKIpbo/SzP83x7e4cJstQqU1ARRUtIaVpVlbU2orfjE47H463xJE/T2XT6/Pnz0Wi0XC77/X48AbFSwJ8hgIpnqNN9a3nf4pBYWpUaEfWRPwVBMmMJlRhswqticXR4cn56Jghxxy3Pe+fn5977gytXiqL4y+9/7+mz5wDU7/eJaFVUw+GQmafTKUQyySQhw7HijY7Je78GrMfwDp+jomtFDkPry+wFhBo3cD+bfx9xofFTG2PkRdtY/9lmYtgZZJxjd4a6NrYgSZbGJ2lCs5lUateVhC62x9+u815ExMtf/TvadRfWwRA6FPnmSQIAQDUIxlK/3x+Px8Ph0Foba+KyLJ1zZVnGb9ZZKwGitpCrtXHG7HfdsYg9BkQEIkVRivpG7cdgbEcmo9Gon6e3b9++fPkyMxPB7Pz8u3/wx7ye+SIak8RsPs9zBfLOEXOWpo3zGiQfDcqmDqCRnIbJFkVxdnY+m828b1F/0eutLzuxrBEn7akFAIC6rnHj0V0maiVy2ysWJQkFEeM0GX7mwa0YvehGA3DdIh+NRtPpNB7ZNElU9ZWXXhbQW7duPT85roKbbG0/evKYTGIS65zL87zfG0oIImKMQdHRaFSXVWytaddLhG5hb10Frd+ztWk8nOt3GP/JeDw+PT1OM55MRiH4nd2tyWRUlStxPjgfmz2qKtBWzmmSO+dMmg0GAx/k4eNHdz797PDoxDtWuVhlJCJBUtXlcpkkSZKmiBgi2LD1C+B9s047tasGN8PUpuNjgxvX+YL7lDDWeLy+j+3PzQV2dH3k1g7388+vGJ9z86ZHtgtrbXhxQSn2JAN8zoxfqPXalxi88uubRr/2iGVZckv4a9oNy84IFeJCQ5v4Nt1jM9ntzhJEVFIXSFtfu1lKrZPheMOjEXK3i6mqBG00z7IMxI/H4yRJYjxZLYrVoohpOiKmaRbjiXPuytXr0cWenZ0nSZLneSwg5qv56194td/vP3jw4OT4LM9zVTw7O0vTvL0inZJBezrBAcjab61v7GaLGdabqYLMtivYsOWrJF0zEX4uSMZ/vXaQsAHTjX/QvvOOWG13e+u/+M/+V7/3e/9+99IeWrOsapOnxqaL1ZKMFVDfuLp2ImKJGY10TA0hXNyy+PxRko06Woe1i4yfL97GGJ+j9uh8Pr956+pw2L98ef+VV1/63vf+4t69O1/9yi8MetmDe/cfPXq0XC5V0Zpkd3f30uWD6dkshGDSTAHv3r/31k/fmU6nvd4gS8dt+dddjUhFV9e1TZIW3ihxvkLM1JVjYe0m4sdZl7UvGAnKWko+mtz6t91t5c3LC9CJvW5YxYYPvXj+z9nh+mlVVTozE5G1Heo6BPOF0f6HjNCwWXvii2ElM8dU8GJo3kZ8F1BDcC22sCtz13sf3RkC1VY7CRXiJvhmwzfG3rVXVtTop4hIujadqgr4LjgjoxgiF7AoquV8sVqtjDFlWefZIM8yRKzruqqdD2qTzCS96fncGFMH6Y8ncc3nfDar6uall1569dVXrbUnJyfPnh7GapY66ndEBBCk9i0gQqSlUoQXcfItwja2LkUEFSSICCDa9S1unRFDzG4uunMXYEVRAo1qQp3QbbyDJk6ZvN/b3a2KcrFYNE0zGgxWs7NhL8XgARXEnx5OR9s7wTfPnj0bTsYMKD7kaWaMqcvGOccmAYDN7l+83XHOFt98jGPrCBwrsehhy3K1XM7jnufe3i/8+q//2sNHdz/66L3nh09E3R9+53dDU+/u7Ny8cfvKlSsMfHp6enI8ffLoMZPd3t0rqvrd997/+JPPau+G4x1VdM51AimtVkzbQiRVCKIYurwMkSVKDImHbr6lqqhCIHRxv+JVa8s/17X+21uJkRkRRULbuVfVlnoHAYBRN+3hwvYufrQ2P4hpTrQhxAjLazeNnHjt0tqwtmcEoou+bjQQ7UrECyMcDAYikTPDRdrPmOklSRK7gs7VIaj3Tfwb6gyDNsBB7aZZZ+vapemqgTaQ6RvIjAuA72YcDiHQC3ToFyvMIYi1bZ1AxsaqVQTq2vmAoBpHbcQWEb0KI3iV/f39V19/bTgY37lzpw6+P8ifPHlyfHIoIvP5vCrLqijYWqJ2VAmRkQAo5rbR2a39C27cmvWSF3S8oO1fIIJGHbO2Ex3vqaps3oDOvxJ2BM/UkUG2z9lNw/I8393euXPnjqr6xr337jtvvv7Kw8dPD4+fY5pZhsNnj9Je3u8lDFqsllXZhF4vYYOKeZoKIuAFOGl9tWljjVC7cw8AsdZd565Zlo3HwyxPDg+fee9WxezZsyfzxezhw7uj0ejg4BKI393eGY56Vb3qZ739/b0kyZ4/O7xx65XGh8cffPzZnXvHp9OdvUvWJNPpFJUiWIuIgClyCIh4QFINLiCgKAigKghiZDb9fDjCjeoAXnxE9MyLjzb/2jQzjchgAHlRkOdzj/V53ghfF/YjG43MzWN88a4QIEhka918qs+9nIHgVZx6UXESAEnFeST1TqJMo6iPTCGkxIY0EJv12xLpeKXWsbELhkiISKwAXdoMUVwZGWPvIdbfIbbrvQ8hSNAsyxCZoEPYqI8i0yGEYgWuaXzjrLXOeWOgaXyaZESECnk+2tramkwm3vvZYt7r9RSx389RZVXMpufHs+mxtRZQTg6PnHO9Xm97axzDdZqm8dhBJLdUISRUUSQRUELWVngAuqZ2ZLMTlZh0Rz6xyEEQgybGNRYFkbaDp6pxhBhT0HhVJUibSSEyd9cNUYMY4tFgqEFuXb/x4O49tMn52cmbL12/fnD55OgwT3heLvauHtS+hhDSLBN1WWJTm6CX1WIemmDSpNcfyZoCbaPmrKsCLzBrF8m2YURjEG2XUkldFa4pdne279+7c/PmZe+aulp+7atf/trXvvbe+2/d/eyT0zNfrhb9/pB39vf3Rztbuzdv3Hr89OinP3nrx2+9W5b1eLzd1K5qQj4YlvOlRB5RQQJC0iCgGgiNRKyzSpt3xDvxYm2mHefD55aGL8zGIFzIPGGbbrw4h+jMIA6ZPl8gbP7v54yne8U4xmj/IGaf0FLBKXTbhEqRNFy6xLTt0Kp+/lVMUS7X9AqqGJkmQUQBJVxsfEcUBQJ0y4jtG1p3dLArjWADgY64tj+Abj4D3dxssxXUuRFpGo8Y2kgf/QoRIqRpBqB1XQNAmiqiJklCwCiwv7+/tbUFAD6iTAgG/YwZjbXT06M7dz9pmibqgyNiL83TNI2LIFVVxVdumrrX6ymEOEsAaGepqhzBr+sYiF0Wwl2KKSIE7eFWJAmkiLEURiTE2EJ4QSBlff8ANEjo2sRIUWth40DEBHJvby9eq7pYaagf3P3s1o2rV69f/Te//+89BDZWUc9ODl3Qfn+Q2Sy4ZjIYbm1tIZnj6RSwbS1Kp6AQO9vxVda3IN7Bui7X2Uq8+7FNjTT55V/+9Xv37v7ox9//u3/3bw+G+fsfvH109LyuVlcOLr35hS8OB+NnT45++MMfrpZ1rzf84MNP7z96engyZZtnvTQgKYIhznq5Bmnn0hAkqLTSd6QKKh6i8Gc8TqDeN7GQ24zh60j4M5nkxXHqzv36Yl5MC3/Wxv5//Opz5reW3V6/0PrMR6e18feAL4wh4WefvDVC75sOsBt9jKoKgBBx5xyhC3UBQKDjeFt/VIrcixvjS1UVCZG4XkCVdG2iiGjUsHA0QujGrGyYlFi405NePzmk1nK3WRhcMJYtGyIa9kfUg9OTExIJ3pVluVqtVENQqet6e3viHU1PDw+PDokozbJeRgA0n8/yNDfGEKGxnOe5MSZCAmLrWXS9dq2AIGRbvP1FSgeqynFG1JYF3a+JVGJ3P+JhIO5EIrYFX1cOsaqqIAAjhm5WR2tTV9XIcebrZns86eU5qLqm6fWy50+e+HLxn3z9t46n56k1z548Prh+/fKVg4Orl5fLApROj0/PTk6+8uWvfvNXvhEEfvc7f4z0guBRvOxZlsW0c9M441l3vnEemLnfG2xt7+3u7g4GPQD5yU9/NJ2eTCajp0+fHB49mc2mo/Hg1q0bBwf7dV3cv3//0f2nZeGMSf1c7t27l2T9/UsHi6J0znGaNc6fzqf7O5OATRBVCW0qGJPP2BAXgVZ9MT4ohMC8ziHXUZG6Gv7zfZTNbgUibXwfvyqAxh745yzhc6bYJgIv2iFAlEiCGPci6HQzEV2/DUQkwPjP9Oc35DaMEEC6yodiaBWJyAAHUZsAEUlVqW3e8IVuSWcnRERVVa1P6aarQETuPJlgpKwiUpoMJ+vZDq2H3Rqy1JDS+pMbY1JrrLXz+dzmaZqmhimEEIGsDHjt4HJRFM+fP3US9vb2dnd35/Pzo6PnVVWUTblarchwZPteFYUxyWAwquvG1T5LUiKKMhgRhNAO0wVBo5ysKmLKrEQMm01LAog5jooKyIX/I8AAIZITIwEbZDZxK1pjDtNWZaBKChggYnQF46p3vMcaUCFJKWF0TbGzPWJWRA1NvX3pIEvz8XAyHk5++tY71w+uBJVL29tf+eIXbJJYm0wm25999tlbP3l7f39LfP3e+x/M5ydsEmMSihSLrcsj8bV3LhJAIKI1SWoyazlNcWdn6/r163v7l9I0r+t6sVgURXF2dnJ2fFyUc8v64fvv9vrJa6+8dOvGTZsYg/Thhx/95Mdvjcc7b37xFx4+ePpvf+d303xYzBaU5MPhMCjNixIAdnZ2QmjW6MrWecUlRESvihouuhqqKrj2f2vzuOjnbZz7i5B4EateeKhePMNmXfc52/sPpaObP4sBNt7G9UtjV8lvvrZSbAjB5jP/rOWbOH2hVvgDRYKrvfe+3++HECLATYK0t8pytV4VAdT4LkVBxRKv3Ub0tREM61VRUYMCgGXLnSTQ2eHx/pUr1trz8/OyLGOakdkkM6YqSiLa3dnZ2toSkdlstlwutybj2WzqnU84yfJUxIJ4m2Un07ObN2/2R8NIIvrZ3TvMLEDn86WisEkQsSobRciyHgA5cZy0sEC/MWr3TYgMANYYDcEmNkmMc64oKhJDROuUNPpjadeQyWM7gIndHcIQ1CkIKrtayuCYOc/zDpWiACDYmqGCCHkFAWUCtWCQFEVBPAbcu7w1n549uP/B7VtXGH0vT4uiCv0hQPbJx/d+7Vd/4/l//9+dPH62uzVZHB9+//vfv7S/f/36da/wS19585VXXplOz/MMhj1cLGcJ91PuLZcFAKTDYagdWBn102RrvLOz4xpZreqXX/rC7VvXQ5iuirn3/q0f/jlx8uqrrz+4/+Dtt98WkS984bXU7AKGy/s7r7/+6mRr9ODBg/npYr5cnZ9V3/jl37hx/dYHH3/yo7feSfujoJT0M0C7LAtRsGwAoCpWGupO1SiWflGWG4PzgIBKvD6iCMAQnBf1KkhE3CaiCKC6PtztyY4Xt10pAqUIYd5QEVW4CEqCncHoRpUAm7VAC1dYm4y2T8ukXfoTy8HWDunCxqQT5ABRbPemLurCWI1o14RDRBPTkjhwV1Vm7vV6zOzqOua5sfGACsF7DT7ykWjX/4QNOozO5byQK+dpFttTm3PCEMLu7v5qvgohDIfD/d19VTXG7GxNzk5PcmsQcdDLEkPeS2IoS8yTRw+iI9CQrTkzAWB6Ph2MhhH8VTXNbLEAgOVymfd7hG3PFrrZO8SanYnRILYjgeAlgKRJQsT9LGPmoiiCV6feOb+/vRthLk3TBBFmSzauDgEoRgFLAYrlsUIwhlGAyDBzUEHfeu66ada2Sl0jRMkHEkVCYRDE0AZ/a7Gplox+PM6vXb2M4L2rnQuc95F5viy8F0Q+fHZ4cGn/5Ru3vv4Lv/D+W299+tGHy8WsKKokTRez6WA0+da3fuX2Kzd++vZb77378RtvvHn71s1XX311tVoRgU3w/v17n3728dnp835vUtf+nUXx9k//cjKByVZ/f/8gSRRJjYWXbl7bmYx3dvaqqpjNZrs7W4NB/ujB07d//NaDR0/YpIPBqKrd9Oze+x/e+eTTz548Phxv70CIwlwhBBcEEYNXAPG4cdA/90C9+Hrxw40H/Afi1cUfQ0RGtkaz+Q8349XmNz8bEj/3B5t/phtzpv/QP/z/42EWi1k3vlMJvvFNU5eIqL6d/iVJYtkYQ7HqtR3oUUUwvpWYH6/JZLvPED99lqQxfWUmRBAJvmm899Plst/vW+aqWFmm2NW8c+eOYdLgsEMzAR/m+AAAJQNJREFUe+/j4lye53F4Rd2ac0xKL126lKbparVaLBbOORBJsmxnZ6dpGgBo/RNdXFBVJCVGJKTIeRoPxWKxiAmbtTbuNOZ5L8+lrmtCTTObZlZVJYCTEIJj5ojvZ6b1KCXed2akSGAVyR1bOJ5AnHHEd0SMCMCsbeeGAQAFCNQSWWPFx0KYtyYj3zR5mqh33vvpdHrl/1vbl/VKciXnRZwlt9qr7tLL7W42u0k2h+RwyBlJI1nSeEayLQMG7Fe9WLb/l2BgbPjF0IslWYBgwAZkayhOkzOcIXth7333pW4tuZ8t/HCy6lavQ0mjg4tC3loys7JOZMSJ+OL7zp3zhT7n3NraWlEUnXbvu9/9jf2/+p91ratSnU7SsvwcGI+S+NIbF9c31/7Z7/xgc/Ncu9UviuKTTz55+Ojrq29e5oKGw65zMJufnI7TVqsbSCirnHDQaocyAERMs2mR6axUZW12dnaOjo6ubF3qdtr7+7vpfJ7nuTYubiV5nudZAVxMZ6kXF0HuFRbAGGNsc+WttQtA3Dc1BsZeEly+ZkKzlV6C1Q8+l2Vd2fgVtkQr4bGPYxdLU2r+kBAXcjX/oOEBstL/oqqulwWi7qDrcydBEARC0iIRmhcZPlvoWE2vrTpDv51lmbWWeTJazu0CINNKknPnzsVRND49ravKGEPO5VkqGfeInLIsV6/jMjpfNu8XRXFycrK1tZVlWZqmvomGMWaJfJc6ADSqxasr5oWYUbOK85yenCdx7HGJ3u95OJ4nh2WyITUjIqWMqY1zRkpurSO/REFE27RXV7pa/ZkZY77TbBXh4RZkoeAccUcAjHwkxllDv2oG/V4riabTU5/aXV9fn8liPp3OnL50ZausKgKIkpgHcjyezLN8tLHZ6w1UbVutTpx0lDLTyfzzz3/2+Rc3v/X+u7//+z+anM7Hp8f/9b/8t+OTQwD77reuX75y7r333wGA0/E8z+orl6+vb3Yf3LtpbDHLsum0YDyeZNnx0XQyKYIgBmJpWn99/ykjyPNcClZVNTAqSjWdTmtt+/1+t9sHJpUyURySI7/6c5bcIt3N8Bma0xc34Fkv9FIHiCtNsS9+6rkZ+KLzXH3nawx69cXFOVtvgKuJLnjdPeEbjWZuVWXpyX+EEO04SZKEiLTWztjaVoZrIvLNO1prsRi0UuT13JW4ANQuLdMoba2VjHNPRUwkGQ/DMA7Cw4MDr+xTZnmapv1Ot9/p+uXmsoToK4pSyqIofB2Wc97v9zudTpqmRHS4f+C1TVpx4oWH8zyvnIuSBACQnZnx4mqeIXKIiDMeSBGGoVXaKuWPGEVRGIZhGGpdt7ttR9ZaW5XaOgeAkqPkAXD0OWMA7imEOOOMc+7B+Iu7BufcL4Ek40TUMDWAT2CjQ8fQa9+S4DKQXCCg086abmd4YXODA4E1jGBtOJQsmk+nURQRQlXXnPPZbNbudS5cuDA5nXEmty5evnXnjiMmZeic6feHuzv7morhek8pxRibTCYPHjwAdDduvFXXpVI1oJUilAHWKv/ll5/P/va4LPY2z/WDqD1a63c663lpiGSY6FtfPVgbbUZJDy1WVaUMd5alqQ4SEQQybvVdnud5hcwAMin93FhWsMi5RlV7CQFdnYXLxcsrTeVZB7hqus/Z2OqeV3OV8BIfCADPlI5Wx9Jzwko5/sXnX2VXZ99i8RZceQTwqffmTxRFIYQQnCdJ4s8pFBIRvfIuKA2Moce4MMY594wyYRgujdAu+Or9KS5bE/0ZXDx33jknGOdS6FoVVQmOgjCsy6ouylAGMgwkF0EQMsDJZLKxubZs81muMwGgrmvPz7esYnkmDn8T8dwQURJ7JH4YhlnRFKOZaERwve2laQ6+POe70ThyjqHkjowjw5BxHnDGfOO2KiurLGOOc8k59/QW4CsNDK01BMA4WOes1dY2vA+MoQMksIiCC0TLNfmrZB3ggjyCEIVAXxVjDLlgXDIuBePEyMHm2ujqG5dbSRQEUbfT8jjeJIpR4P1H97/1zo0gjsaTCQje7nRv3b175cqV4WgjircRuapNXZn+qHt0Mr1+49Jv/eZvr402dNedHE//+I//+NbtL4+PDzjn7XabwO7ubt++dff46HQ43AgjPL91/tsfvitkPE+r3b3TX/zi68PjuTUMKTo8OqmKutPpMeS1shCKuNUztqoro7WzBo2zQQBhEgse5GWhtdbW4IqNMSkWdPFn4/n05sJOVjOTz5nWqq9b/cjy9ZcYw6vs8LXB7Yvn6cvmzx36HzlEr9uJoqjdbodhiI7KskzTtChyhhBIAVJwzgVrgGZSyrIsfZ4GvCQvICADbLouyDlwhOSVsTnnPAoDxlgchEyKIs2quiRyDGHY722e27h47jwxPDk8QsGdNlmeprN5GEohhDPaGYPkFDkFMBr0l50sSM6oGsl1WomSqqoqL73GGIgkAXBhKLXmviOCMVyw9QEi48g440KAtWgtco5xKKNADnpdHzk75+WQNOfSWGWMCgMmpQxD6aGVymhjXShCx5Fz6UmuSiDniCE18YpzzmrkRJwDIvisDTa0f4Tkk8SIaJ2TXEguGQBpRcSDSMZR+9sfvHfl0paqyqKqh8Oh1cYoHQRBlqXjycnmhc29g10ZBsDZ092d4Wh9/+Do6dOdurLtdhJEIohaUoSDwWg+L4hwPD49PBjfvXv/+vXr83l2//79p0/3giAoq+yrr766e/fr4WDte9/7+Lvf+/Do+MFw2Pvs8y9+/sWtg6Pp4eEsjPqd7qjbGWV5XRRVpWogLMuSCRFKYZycTk/rug6CIIqSOI5RcK11Xddaa0/SgUSechccknEvTt8Vh9aEiEu/QfgSh/OyykEznlsz4jJRufjo0laxeeFViRkCOIs5F2uaM6jNajCLiGf7aVb+i5Ogl3va1SF6vZ5fy1ZVZWo1n89ns1md573hcNlSaI31eQ7fL++tcRXE8NwVQV/fC8NQyMePH7eiuN/ve8JZqzR48Uei2elk1B+sra3tVNXjx4+jKIoDWaraMxf5o/ulnbW2KArfA0VEvnPKA5d820Qcx77hwJ9hVVU+i+sbScirPQAg6DiUzSoXHTgSksVxHMfR8fFxWZVEgMCl5MPh2sbGRhzK7af3nNXamlqVjZN3SAhBIBBRCp4kkf/KPlDPi9IHCIt2rdChF5PyUKNGpcg5hmgYgjOWCS45t9qougbLRSLbSfzh++8JiXVdFlneasXWaqVqQijrotXtJO3W3/7dJ61up93t7O0f/ugP/tXh4eHR8bgoSiFkS/ak5JPJLEpkOp8/frRz/c0PNjaC4eD47p0H4/FUivj//d9P9/b2goBZp69cvtrp9La3dx88uPcbv/FeOt/76Sc/e/R0vz/cPH9uizAsK3t4dNJud9vdDhGpqkZBjFOlK6201tZTsAJAWdYOQSmljdZaO6AGV0zW+SQGOHJnKLnV9eHymbOZvXjxxZdeNaFXS4irOYXnwt2X+thvMByBAzpjnfm1+ENx/87dq2++ORqNxuPx7u6uqiohZZgkTWMEgbWWjJWMLxHby29IiwSDL2wMFiMMw+Pj48PDQ6fN+vo6OiKiJ0+e+OJHp9PxrauIeHBwsL29vbu765ND4/Fxu93miFbrVqt1eno6GAx85tNDmaMkybIslDIKgizLut2ulNLz/4VSGmPKsgxloGtV1lWn0wnDsDbaM2f5fpkApeyJrJiWquwkiaqpyGb9fj9PZ5ubm1lacM4twJMnj4QQ775z7Y/+9b/cfvJESnn+/PnpdLq9u2OMJaL3P/hgb+/g/v37R0dHKHi71ZWS53keR2Ge5xsbG9ba8eQUkDiilMI5Z43hUviwv6oqclZGEZBlBEbpYa+/u7PDUW5ubvzRH/0Qmdvb219fHw2Ha0+ePCmKgkvR6/W0q2Qodw/2D4+PCFir3bWO/flf/oXggbUuCAIU3Hd1Jkmyvr5+MnX9/ijPKwT59lvv9rprn/zkp1VpL196UytVFNnp6cm5cxeq0vyf//03/UHn5PhASPadD3+L8zvbO4fr5zeOjmetdt9YUKry0iuMQxhhXk2MsboC5IIAyloJyRljPuBsGmfJWa38alAgkrEM2KKoRwANnhKeNbCzbs4Fz9VyLGPXJWHcqiUgonVmud0YMDTN9Sv2Dks4LaykcJ47BCwCaXemoosvgQEsouCXGxg+6wlX00+LP/H2O+8g4nQ6revaexK2oN8yxpB1PlGxXKcFUlRV5ety/kQ9Ccr6+npd19vb2wcHB61WyxceGUG329VV7X2aX7/50HA6nS4xU96JJUlCNLROTyYTAPD9NVmW+Z+z1WqlaToajQaDgc9eDodDAJjP57TgQUDOfRMAIopSLqsaPvdLRHEQqqIkrXRdkTXOamet1rVzamNjraqyqi7SNL/+9o3xeJzn6c2bNw/2RodHexcvXJrP556+ent3N0kSY0xeFFVVdTqdpNPu9XpRmADA3t4BLnRwuRRBEOR5nue5Lxd63pplB4kQjJMwxgjOZ/NJr9MOQsYZPHhw7/LWel6kT7efxK3e+toF5GxyMuGBbHWTeZZ+ff/ek6dPs6zY0Npa6ywcT/cRGZNCKRWG4WDYA4Cj48NKZ54yQ3B+cnJ669adk5PT0Wiwsb45nR3XddntDBH46emk3e6+fe2dne17RPa2vbu3c1iVajKeFmmJKJFxB8Q4OLJaaW1qH15ogwwDhtxX2U0z1HMrPSLy7SmvchwvdSlExAhfqncEK+5u1Q7dCovZ0rS+ub9anvbCaJuxmo951af+wUOMT489KjqO4yQOFUetNWMgmKiddeAkF1EcSCmVUnWtrSKnDSOI48TriiRJEobh6emplLLbaiOirmrnXK/dabfb6Xy66Iw2jPnOEQfg2u1Ea1lVVVmWzpmqKpSqrLWAjjFYX18norLM9/ZOtra2Ll/eGo/HURRcvXoFET/77LPT05N+vx8EwaDXtdZ6OUgiskQs4FJKRwbIKmWJSNd1VVUWSDIuEa1WQEZwkgKYkGGAYSRPTw+GgzUdia2tt/I8G/Y7VZH1+z0hxLDXv3xxi4iUNUKIR48eB/1gf3+fyJeGMZvN55OpEIFvrwZwWtdKqTzPwzB0QEkSFUVh0Dky5AwTTApmXSNs5hNL8/n83NpanAQXL57vdNookEle6ZqKzJOGVFWFRl+//IYmdXh8mFeljMIgjlrIARhw5jPDeZ5ba5yzWtezdNrqJUSGMWxWZQCMidlsFoT49jvX8mL08OH94+OTMAx7vcHjx48PdneH/e5OtgvE1obrxjZc1FVVcc4JyVpdq7yuS0eWodSWScGFlMjAOqN8eGr1sl5HRJ5bwAOa4dVLpOdMhYhggZJ/+auLjaUdrprQq3b7qvHs/QK8H6IVUMrZrpqSLzYLx8XnlgdceWTLrZd+2SY7urGxsawTFlmeZZkviDeiuQtUl1vwNwMxX45b9n3O53MA2H7yJGm3R6ORECLP85U2JY+NbpyVpz+RUr711lt1XZ+cnHhJGT8dZcCttaenJ77y3m63gyDodrueZ8Uz84ZheOnSpdls1ul01tbWoiAsyzLNszzPvUIDD2QURT43YJ1jjGmti6Ighq0wCmQgGEXtBAX4dtsgDludZGvrXLvd/cE//9HBwZEU0Z/+6X/utAdZOrt1sic5ALFWq8WkODw8tNb2Ot2joyPGmAwiz3CltfaKSSIIlkQEXrLTC3EzxkRVabcUOm9iISKKoiiM41jXpSrilhiM+u9/8G5R5saoOI6Npa/v3cvKojcc1FqJMDDKKGO4FM668XisleVcKqWyLPM9EAR6Ns+JbJwEYYT3H9xBZO+8/e1r19/I8/TLLze2dx7nRXp4uC8D/Oij70yn0y+++OLo6Igz+s3vffzRh99WxlriyrI7dx/sH4+r2hqlNWrnrDa1UtoYR+QcGsQQkQCMc6C11lrZZ+luqUGZPDdTXzEpfYC3XNEttgCeWea9yopw0dr63G5fP1bfs3raSwtcdhoA+O6FszXtrxquqfG/uvQCAGJ3e8eT/0wmkzrLMAjiMFqCwjxaz2qDiAwwlEGWl3ZBHUtEnuHLWnvl6tUsy3y+BBF9Jqau6yRJfC5HhEHcbtksA844ylLVVV2VqnYIQRD4G0E7ifb29kaDYb/fn06nQRBEQdjv9uq6Tmfzoih/9tnn/s2C8YO9/aooz58/XxRFnmVpmqZ5Vte1EEJFURBHPovDEP16UgjRSmJXVa0k2thYi1pBms7SciYllwKHox4ACyTeeOeaNvD2O9d2dw7yNEvi8P333rtx48b3v//9NM1+/OMfPyge3rx5czhaJyKjLNlGp8zf+42qnFG6RiTqdFtBEIzHY1WXm+fOFUWRVyWRp7tsmnEERnEcE9h+v398dBBHcnx6DOyGUvrx00dpmvcGazsHO0Wl3rhyqawqQojbrUhGyrnx0Xhvf18p45szrbWcowhRKVXrMghE0hKj9d6dr794+PA+Iv7hH1zaurR5491ryNTu3mNjcyHx0uVzH338AaDZ2T4o0kmnLa9du0gg8qLe3jvJs2mRzaazksug1g3BLgAwLgElkQcfkSNjrdVGW2fAt5M7i8tyAQE03ole3tuz4j5WNxqsxUpuZnW8NBwVYknP4W2pKUv6oz97QATfbPQsPNTnOR05R9aRIbAEDhEJCOEZC189jdeX7OmFeubZeSCK+dFR0W4zxlRVySQZDAaSi6IoJpOJlDL07g6sd19BECjdwNn8LqSU/jbv13jtdtuXMYjIu4hOp1OWpXPOe0JYENeNx+M8z33O05tuFEVKVaenJ1cuXb506VIQBE+fPnXO9fv9brfLOfex1nQ67fV63v6dcw8fPnQLlqQlUIYx5rMyfs1pF/2NjADAScnPnV8/f34zzSaT2QTQOHSf3fyUC3H79u0//IN/8Ytf3l5bGz588Ghtffjk0eM7d+7cvXt3f3/fGHv37t3RaBSGYavd9fDuoiiMs4joyZS63bavMWZZNstSzxe2vr7u0XYLOla7vICBB0tUVXtt2GolQST3jw5u3/7qrbffPBqfnJ5Ou8M1AnBA2hhieHh83B90GEopJXCmbEMsYq2NogAZ5XmmVNUfdK9cubRxYeSo7A6ine3DT3/6N1tbW1ffeOu3f+d7e/uPzHblCI+OTn7+xc3B8Idvv/NmWZZFdlRUk92d+4QyzdWjJ3u7e4+zzBhDAMzUxihnCIQQCzkHyxhzThtD1hprlzxiAM+GiL9ybfZSG/OfXM3ZLGfza8LR1Te/auevNxhY8MovK97Ll54r7q+E3C8/BC1u0q/5pkK0216XJwgCwbiq6lk5rbMMhSBrjRcMJOALevxWknjS/7qqyqLwBunj0k6n8+bVq1EUHRwcBEEwGg6NMcrUaZ75FUW72+FSICKQY4I7oFan7Q3YkrPk0jSN4/jo+PBkfCyEULqOomhnd9s8MQDgM37GmOqobLfbrXbCOFZ16bM+YRgGXNpAGmsRkTEMAumcrWulrUFEMqRK05K8yNIim3NxbjgctLqhMSotMsYgnU8vv3/5k7/7Sbc3nM0mb1y9crB3sLW15bTJ8/yrr25ZS4g8idvGGKOsFEEcIwBIGXY6HUeUF6m12kdEUsqo1Whxrq+vj09PfVcKIzB0BkLwP7bgXCnV6/WEgDRNf/r5Z5Upta5ns8nu7o5nWBxPJ9qak8nBySTmLLDW6toQURQlDiGv8kpXgUBESlrBcNh989qVG+9d//r+L668eR6Rbt96eP/BV2+8cWU4ap9ODi9cXNemLCuhTVWU6cnJyfbOo6tvXvz+b33HqHw2r5UGVaVI2lklg6SsKkeCM8k92afhAODQWKuMU9aSc4aBb5FxRL43wlsIfPPq9mqKcmls9M0+Swsqx9V4b/n4qtzJ6hFXY1FfkV7eQRbLB/+RMxh3g3w5+xdeXBO+5ss24Sggr1SNwJGRURZUBUJEvS549jAiMobIgqKq8p0Q0i3Y0wA8jxhyjs65osjG4+MoiWezSdxKgqmYzme9Xm82m2RZliQJY+eTpNEeOjk5ms1mo8Gw0+kIhoCURGFBOhRhURR1XXY6a0WRObBVqby/PTk+jpOk1Yqrqmq3E2vt06ePL1y44GMka7VnlzLOEtFkYv2BiqIgojCOOKJxzoE8mZw+erIdJUm7FwvBldXTSf7GlevI2Y9+9Icnx+Nr129YS48ePfmL//HnHh2GiEfjE0QcbYwCGZ1OJ/Ojg26/51mfoygYDHpM8HDOp9PT6XRSVdVwOPz44+90u92f//znjx8/DIKAcckZAyHYChLIGiMkb7fb2Wza7rSKeSYlPt2ZPH76ZGvrYlGau18/sEb0B+equsyrMkpiY2kyHZPDOIpaSafV6tR13esPs3weRPG58+ta10fjo19+dYsHyDg/ORpPxtM4jALJd7YfTafzNJsB6d/53d8GgK+++nJ/7ygM4jjq9LrrZQ2f3fzl6ems3R7O0noyS2fzOo7RGs4lC4KYgFltjTO+CXxBT0KADhhHRJ/U54K/zBMSoPOM7K/IeeJz2x4lz3x5joAY+s/6R3QejQrk1XjQGWWBUSOSyJ4pSL70eGzxtAVomj79v4to5Tkf3qRmXsDQ4YohsjM8qQPw/cf8VduIhPzStwicswRkwYFXS+fI6iIDAMGZ4JyM1lozBCmlrhQAcytJJBE0pPf+X+AsDkIvfRzHIRFNp9PZbMYYu3b9zbIsJRdKqaOjA2ut5E1vu1KVUgacLassieLpfAaAyNhgbX1yOtvY2EDEPM+VUoFgXq/CGg3gewIbZSyf9YnjOIqioih8Pqn2GrdhIJBZaz0RIJGVAQ/DMFjoh0ynpx9/77sXL16KoggIfQgqhGCLitIyoeIVhDljXnfFA26Hw+GFCxf6g16v1zk5OXr06MlsNvO6hWmaN1rFjJOxlVZShq1WCwCqqkrCgAvGkHuu1SVXYpbPjdeZ4dJaqmrNGBdBYJGALZGZno5qQezHIBBcBkjkCHSSJN1OnM6mRZYb6waDwZ/8yX+8dOnSl1/e+u9/9mf37t799//hP3344Yd//df/6+ZPf9bp9BhKa42jShtVlXVda6UtESKTjAlqbryCENGhIQcWDBlC4xghMPT3ZET+2k4fz2jKAJfYsubkV/k/z6p4nDFBCH5BxsAfB4GhM9Y/v3zGP4IX1KNn7MfvavUszgx9aaUIZJ1xdkk1AM8SxfoTRnqJg2389vIczuwTGApqYgG3+si5XG6L9c0NAPAdQ0VR2Lo2ShkEDEIy2jhyTjurwRmHyCzztT6HjSIU59xzhASB8Ba4KOjbUpXa6W671em2la7H4/H+/u50OvWt0sN+12NBlapUbX3DHgAhOEATR0G31+MinKZzbVVW5ADAOY/ioK7LMlXWGc9HGicdKZvKmzHSQzTrutS69kaode2cM0YBgNY2CCJnAdBxzWvlOFfU0NGr+/ee3L/3xBgTRdFsNpvNJr1ej3Pu+1fcgmPO10UJOOlFAs2JosyPT46yfJamnSyfF0WmdS2EzypraxuCes5EwAUDNLVCREZO1TU3TaclLHF2jIdRT1hrySEiOMO5Q0QuWFEp5F7FA5BxBmgJgaCqKoHgogCYYEjWQpYVqqpNpU0NQRwNemtxGMVhtLV14Qe/93sfvv/h3vbe/XtP9vYOnOV5apwzSbs1Hs8sOWvRWu6bSREEEDpnF4R00ACZqYk22YLi2DuE16co0E/sZ/Iky/jthcQJIpEFrztNQNhovXgKu7P+Q2weARw6dJ4uEwCa8HiZYX3Z+TgCILfkuX1tPfD14+xMFmMFE+de8yiGayNEJIdVVaVp6jOcYBQ5AkdgneMIyAEdODLGep5SQJRSxnHMOfeCJB7I1shiW+uVxIloV6vhcIiIvrwehmE2T4moKAoPLyQicmZx+4F+r2OMOTk6SrNs89xFrfW5c+eMMbs7e0JKwZHICmRCco7gnMuyjK2QLy7vTEmSeCMUCzVJ55wQxJiwzPpeCvCcGtZaa31lpSxLT/VtreVcrq4NaNH6hMA4E846ck1jFjnIs0LVGhYCTHmeW2u1ts65PC+JiCG3xoUhBkEAwDwMkHOslWIMG05ezpZ5ad8yQnYFDY9IRO040c42NJyAjJoE4KjfM1obo8uicEYbqxFJMr42GtWOfAnxyZPtIIgGg9EPf/jD+/ce/uVf/tXh8WkSd/kwmE3z+TytVJ3n+bIHetluAisyY830Ig8TJv7K8vvrxmuyhS++86WFjdXfevUZWrCoAACtrL2fxYiu5lqAiJ5hiznDl/76x0v3LPI859zrXoZSyjBppWlaF3k1n4MjIAAmBEey3BpDzhpy7FlfvEyccs6Bs2Xe0qccoihqtVo+t+mXdkfFYVmWzNNHNUQYzU4EY1WebW1tteOEyWBtfbM9T9986+2Dw8OPPvrueDyezyZExIGKMp+eTqqqSlpt78Z9emZZo/OY0mUkufzyZVkTMbvQ0GQL6i4i5+1tyaVnrc3zcklZjV6aHRiRpyHzvK4MgIxxda2Ncc4ZQBsEwqeCfeXA85uVReUB6FJKxnDRI+IJ4RspSSC3BNN4LmdHZ3Wq5o6AVNW11p7TQHgFRM55meUAhIwEIAgRSiEE85lqcA4QoijyHTBlWabz/LPPfpZl+fnzF6UInz7dNlYxxsqyDIIAGC7vaNZaa2ipibCM03y65B8wC1eXiK+Zl8++/+VG+OI7G9KXs8TqSpqHvdIInXPGnQkiLbnefr3jNV9ZbO/uc87juOWb9DpBjEykTAAL6jKnugQgS4DIuAg4g1gK3wSICxVSbFSpGGMMGKMFrs8/xmHgzTtN0wcPHlhrZ9MpFyKKQ7YYgje6jRyRo7148TxjQjvy5ZCLFy9eu3bt9p07aTrzCY9OEudF3Gm1hRB7+wfeq3hYwzKz7H3RcgbDgno4imKttdYEAEI0SssAUFWKCK0lY4wxGQAwJoSQfrecM0TmHFlrvIJfA5RDNKa5mXgmb+usEGIh1IwAzGPZBZce+KK1DkPhvfSiQXmx5nRnk0PVtQ9QF0l/dM45bQgBiCQXYRgyJsD5LyJmk0kURZEMWEORbrRSWilTq4sXL25e2Lxx48bVq1fjOP70p599+umnt2/ftdYxEeXZ8cOHD6UMk7jLvHgmZ2yhUEBEhs6EQ56bVez54tuvf7xohC81v2ZjYYQvfvBVuDPPE+NWiXP/8V26rx4v9f/C5blDNMYJKcMokWEcxi1L2O8Ps/ksT2d1Vbi6JkLJRSB4pxP7JLvHCFprPTWin/GGnO9M9+ElAByXxWhj48aNG/1+38tTDkej4XBYl7mPYJ1zQLb5jZ2LJNvZflJVinE5madZXj689/XWlTfu3L49mU6dtXmeM3J5ngdSjEYjjyBdcl7QQrvn5OTELtRSl1dZCMFYI+kOiyBzibv3tw8fvjYtiCgQDOdcCIkI1jpjdaOTy8lZYIwbY6x11pCzyAVUVeFbzohIqUZDwhjTaXd9t74xRohGCFnrWgqxwDMvaGQBYEEgsNRFYo2khBPIUMhQhGEYWkseS0TaJFGMSKrI67okcEEQxGHApTh/5Y2PPvpouD601v7kJz+ZzdKvbt3a3z/kXKTzfPvprta+wwTKMidkcRwuC+TLzoNVC1zGF4t11t/PCJ9JmS52i/iqGr6fr7g86HP+5Mz8aLFsfEUu9NlnVjzhGTj7n3CspohefNVTHnIyJs9LYLOkbYUQQRRJKVvIZBjpsqjyeV3kzhpLzncDKGuWv5Ajq40nvSe7sAFjDBABIpNSa722tlbX9cnJkbWNBv1y8WOttabRBydr83m2uTa6ePEiIC/v3idgX3755S+++nIymfnrOJlMJMMwDL24/IOHj/hCGdODAbxj8m3HsHDIfvHpo2JjjEdWWWuVUv724UsvficNat6CUmUUxQDo41lEzjkx1ogNAqC/BpxLxtBaUkoJKTmX5Dy7JXeeWYzQw4Y8vtzfDjjnxqD1qr3eCOH5WrBb4ZX0l6gsS2tJYV0K4VHyQRC0Oh0hhDFKW0zieDDob25u9rs93ybcasVHR0dPnz69efPzLMvKSo1Ga+1Wt678F+eDwcAYM5umIpBBIGDB2E8LrczV6b4ysV5nPK8Zq6sD+FXh6Ivvee79qwvmpQ970WmvRhkvGuHybf90q8HVk4FnbwoCnAMkUFi7eV2pvCw73X6SJFWpuGDdTg/arTIKT8GVaVZrlabGKqWsWRJPeOejlOKccynjOG6322e5U2N8i4a/9HEc61oppaxugPZVVVmjmt4CMu++/c6//Xf/5uOPvvvZF78Yj39MXBhDRVm+97sfaK2Pj4/39vasqhljs9lsZ2dnOFrzdzKllEei+0sZRZFfZ8ZxjIhLoXZPo+jcGTCiqiqlFCL3HRv+buJF6omoKCrOuZQNsQ1jTAjOWOAvpVt0+nPOlVJaG2MNLpR9acE84G9YSze7xCIiojFmiZFsyC9WbvluwVSwtIooDFXdPBOH4bDfP3/+/NbWVp7O/coniqLRYNBut8uyPJ2O93Z2Hz168GRn2wOb1tbWjk8mWussK6SUTAR5ni8UsVi/329SZc+IVT7T9ff8TPp7TtrnLHC5t9d7wuX2q9I5Swe7mkz6huezcqDXneQ/cqxa+HNn+P8Bdv9mnTik0l0AAAAASUVORK5CYII=\"/>"], "text/plain": ["<autogen_core._image.Image at 0x7f7089c83710>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from io import BytesIO\n", "\n", "import PIL\n", "import requests\n", "from autogen_agentchat.messages import MultiModalMessage\n", "from autogen_core import Image\n", "\n", "# Create a multi-modal message with random image and text.\n", "pil_image = PIL.Image.open(BytesIO(requests.get(\"https://picsum.photos/300/200\").content))\n", "img = Image(pil_image)\n", "multi_modal_message = MultiModalMessage(content=[\"Can you describe the content of this image?\", img], source=\"user\")\n", "img"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The image depicts a scenic mountain landscape under a clear blue sky. There are several rugged mountain peaks in the background, with some clouds scattered across the sky. In the valley below, there is a body of water, possibly a lake or river, surrounded by greenery. The overall scene conveys a sense of natural beauty and tranquility.\n"]}], "source": ["# Use asyncio.run(...) when running in a script.\n", "result = await agent.run(task=multi_modal_message)\n", "print(result.messages[-1].content)  # type: ignore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming Messages\n", "\n", "We can also stream each message as it is generated by the agent by using the\n", "{py:meth}`~autogen_agentchat.agents.BaseChatAgent.run_stream` method,\n", "and use {py:class}`~autogen_agentchat.ui.Console` to print the messages\n", "as they appear to the console."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- TextMessage (user) ----------\n", "Find information on AutoGen\n", "---------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (assistant) ----------\n", "[FunctionCall(id='call_HOTRhOzXCBm0zSqZCFbHD7YP', arguments='{\"query\":\"AutoGen\"}', name='web_search')]\n", "[Prompt tokens: 61, Completion tokens: 16]\n", "---------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (assistant) ----------\n", "[FunctionExecutionResult(content='AutoGen is a programming framework for building multi-agent applications.', name='web_search', call_id='call_HOTRhOzXCBm0zSqZCFbHD7YP', is_error=False)]\n", "---------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (assistant) ----------\n", "AutoGen is a programming framework for building multi-agent applications.\n", "---------- Summary ----------\n", "Number of messages: 4\n", "Finish reason: None\n", "Total prompt tokens: 61\n", "Total completion tokens: 16\n", "Duration: 0.52 seconds\n"]}], "source": ["async def assistant_run_stream() -> None:\n", "    # Option 1: read each message from the stream (as shown in the previous example).\n", "    # async for message in agent.run_stream(task=\"Find information on AutoGen\"):\n", "    #     print(message)\n", "\n", "    # Option 2: use <PERSON><PERSON>e to print all messages as they appear.\n", "    await <PERSON><PERSON><PERSON>(\n", "        agent.run_stream(task=\"Find information on AutoGen\"),\n", "        output_stats=True,  # Enable stats printing.\n", "    )\n", "\n", "\n", "# Use asyncio.run(assistant_run_stream()) when running in a script.\n", "await assistant_run_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run_stream` method\n", "returns an asynchronous generator that yields each message generated by the agent,\n", "followed by a {py:class}`~autogen_agentchat.base.TaskResult` as the last item.\n", "\n", "From the messages, you can observe that the assistant agent utilized the `web_search` tool to\n", "gather information and responded based on the search results."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Tools and Workbench\n", "\n", "Large Language Models (LLMs) are typically limited to generating text or code responses. \n", "However, many complex tasks benefit from the ability to use external tools that perform specific actions,\n", "such as fetching data from APIs or databases.\n", "\n", "To address this limitation, modern LLMs can now accept a list of available tool schemas \n", "(descriptions of tools and their arguments) and generate a tool call message. \n", "This capability is known as **Tool Calling** or **Function Calling** and \n", "is becoming a popular pattern in building intelligent agent-based applications.\n", "Refer to the documentation from [OpenAI](https://platform.openai.com/docs/guides/function-calling) \n", "and [Anthropic](https://docs.anthropic.com/en/docs/build-with-claude/tool-use) for more information about tool calling in LLMs.\n", "\n", "In AgentChat, the {py:class}`~autogen_agentchat.agents.AssistantAgent` can use tools to perform specific actions.\n", "The `web_search` tool is one such tool that allows the assistant agent to search the web for information.\n", "A single custom tool can be a Python function or a subclass of the {py:class}`~autogen_core.tools.BaseTool`.\n", "\n", "On the other hand, a {py:class}`~autogen_core.tools.Workbench` is a collection of tools that share state and resources.\n", "\n", "```{note}\n", "For how to use model clients directly with tools and workbench, refer to the [Tools](../../core-user-guide/components/tools.ipynb)\n", "and [Workbench](../../core-user-guide/components/workbench.ipynb) sections\n", "in the Core User Guide.\n", "```\n", "\n", "By default, when {py:class}`~autogen_agentchat.agents.AssistantA<PERSON>` executes a tool,\n", "it will return the tool's output as a string in {py:class}`~autogen_agentchat.messages.ToolCallSummaryMessage` in its response.\n", "If your tool does not return a well-formed string in natural language, you\n", "can add a reflection step to have the model summarize the tool's output,\n", "by setting the `reflect_on_tool_use=True` parameter in the {py:class}`~autogen_agentchat.agents.AssistantAgent` constructor.\n", "\n", "### Built-in Tools and Workbench\n", "\n", "AutoGen Extension provides a set of built-in tools that can be used with the Assistant Agent.\n", "Head over to the [API documentation](../../../reference/index.md) for all the available tools\n", "under the `autogen_ext.tools` namespace. For example, you can find the following tools:\n", "\n", "- {py:mod}`~autogen_ext.tools.graphrag`: Tools for using GraphRAG index.\n", "- {py:mod}`~autogen_ext.tools.http`: Tools for making HTTP requests.\n", "- {py:mod}`~autogen_ext.tools.langchain`: Adaptor for using LangChain tools.\n", "- {py:mod}`~autogen_ext.tools.mcp`: Tools and workbench for using Model Chat Protocol (MCP) servers."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Function Tool\n", "\n", "The {py:class}`~autogen_agentchat.agents.AssistantAgent` automatically\n", "converts a Python function into a {py:class}`~autogen_core.tools.FunctionTool`\n", "which can be used as a tool by the agent and automatically generates the tool schema\n", "from the function signature and docstring.\n", "\n", "The `web_search_func` tool is an example of a function tool.\n", "The schema is automatically generated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'web_search_func',\n", " 'description': 'Find information on the web',\n", " 'parameters': {'type': 'object',\n", "  'properties': {'query': {'description': 'query',\n", "    'title': 'Query',\n", "    'type': 'string'}},\n", "  'required': ['query'],\n", "  'additionalProperties': False},\n", " 'strict': False}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from autogen_core.tools import FunctionTool\n", "\n", "\n", "# Define a tool using a Python function.\n", "async def web_search_func(query: str) -> str:\n", "    \"\"\"Find information on the web\"\"\"\n", "    return \"AutoGen is a programming framework for building multi-agent applications.\"\n", "\n", "\n", "# This step is automatically performed inside the AssistantAgent if the tool is a Python function.\n", "web_search_function_tool = FunctionTool(web_search_func, description=\"Find information on the web\")\n", "# The schema is provided to the model during AssistantAgent's on_messages call.\n", "web_search_function_tool.schema"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Context Protocol (MCP) Workbench\n", "\n", "The {py:class}`~autogen_agentchat.agents.AssistantAgent` can also use tools that are\n", "served from a Model Context Protocol (MCP) server\n", "using {py:func}`~autogen_ext.tools.mcp.McpWorkbench`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Seattle is a major city located in the state of Washington, United States. It was founded on November 13, 1851, and incorporated as a town on January 14, 1865, and later as a city on December 2, 1869. The city is named after Chief <PERSON>. It covers an area of approximately 142 square miles, with a population of around 737,000 as of the 2020 Census, and an estimated 755,078 residents in 2023. Seattle is known by nicknames such as The Emerald City, Jet City, and Rain City, and has mottos including The City of Flowers and The City of Goodwill. The city operates under a mayor–council government system, with <PERSON> serving as mayor. Key landmarks include the Space Needle, Pike Place Market, Amazon Spheres, and the Seattle Great Wheel. It is situated on the U.S. West Coast, with a diverse urban and metropolitan area that extends to a population of over 4 million in the greater metropolitan region.\n"]}], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.messages import TextMessage\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "from autogen_ext.tools.mcp import McpWorkbench, StdioServerParams\n", "\n", "# Get the fetch tool from mcp-server-fetch.\n", "fetch_mcp_server = StdioServerParams(command=\"uvx\", args=[\"mcp-server-fetch\"])\n", "\n", "# Create an MCP workbench which provides a session to the mcp server.\n", "async with McpWorkbench(fetch_mcp_server) as workbench:  # type: ignore\n", "    # Create an agent that can use the fetch tool.\n", "    model_client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\")\n", "    fetch_agent = AssistantAgent(\n", "        name=\"fetcher\", model_client=model_client, workbench=workbench, reflect_on_tool_use=True\n", "    )\n", "\n", "    # Let the agent fetch the content of a URL and summarize it.\n", "    result = await fetch_agent.run(task=\"Summarize the content of https://en.wikipedia.org/wiki/Seattle\")\n", "    assert isinstance(result.messages[-1], TextMessage)\n", "    print(result.messages[-1].content)\n", "\n", "    # Close the connection to the model client.\n", "    await model_client.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> Calls\n", "\n", "Some models support parallel tool calls, which can be useful for tasks that require multiple tools to be called simultaneously.\n", "By default, if the model client produces multiple tool calls, {py:class}`~autogen_agentchat.agents.AssistantAgent`\n", "will call the tools in parallel.\n", "\n", "You may want to disable parallel tool calls when the tools have side effects that may interfere with each other, or,\n", "when agent behavior needs to be consistent across different models.\n", "This should be done at the model client level.\n", "\n", "For {py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient` and {py:class}`~autogen_ext.models.openai.AzureOpenAIChatCompletionClient`,\n", "set `parallel_tool_calls=False` to disable parallel tool calls."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_client_no_parallel_tool_call = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o\",\n", "    parallel_tool_calls=False,  # type: ignore\n", ")\n", "agent_no_parallel_tool_call = AssistantAgent(\n", "    name=\"assistant\",\n", "    model_client=model_client_no_parallel_tool_call,\n", "    tools=[web_search],\n", "    system_message=\"Use tools to solve tasks.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> Error Handling\n", "\n", "When tools encounter errors during execution, AutoGen automatically handles them\n", "and communicates the error information to the model. This allows the model to\n", "understand what went wrong and potentially take corrective action.\n", "\n", "#### Basic Error Handling\n", "\n", "The simplest way to handle errors in tools is to **raise an exception**.\n", "AutoGen will automatically catch the exception and pass the error message\n", "to the model with the `is_error=True` flag set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example of a tool that can fail\n", "async def weather_api(city: str) -> str:\n", "    \"\"\"Get weather information for a city.\"\"\"\n", "\n", "    # Input validation\n", "    if not city or not city.strip():\n", "        raise ValueError(\"City name cannot be empty\")\n", "\n", "    # Simulate different types of failures\n", "    if city.lower() == \"unknown\":\n", "        raise ValueError(f\"City '{city}' not found\")\n", "\n", "    if city.lower() == \"timeout\":\n", "        raise TimeoutError(\"Weather service is currently unavailable\")\n", "\n", "    if city.lower() == \"unauthorized\":\n", "        raise PermissionError(\"API key is invalid or expired\")\n", "\n", "    # Simulate successful response\n", "    return f\"The weather in {city} is sunny with a temperature of 22°C.\"\n", "\n", "\n", "# Create an agent with the weather tool\n", "weather_agent = AssistantAgent(\n", "    name=\"weather_assistant\",\n", "    model_client=model_client,\n", "    tools=[weather_api],\n", "    system_message=\"You are a helpful weather assistant. When tools fail, explain the error to the user and suggest alternatives.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test the error handling with different scenarios:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test successful case\n", "result = await weather_agent.run(task=\"What's the weather in Paris?\")\n", "print(\"=== Successful Case ===\")\n", "print(result.messages[-1].content)\n", "print()\n", "\n", "# Test error cases\n", "error_cases = [\n", "    \"What's the weather in unknown?\",  # City not found\n", "    \"What's the weather in timeout?\",  # Service timeout\n", "    \"What's the weather in ?\",  # Empty city name\n", "]\n", "\n", "for i, task in enumerate(error_cases, 1):\n", "    result = await weather_agent.run(task=task)\n", "    print(f\"=== Error Case {i} ===\")\n", "    print(f\"Task: {task}\")\n", "    print(f\"Response: {result.messages[-1].content}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON> Handling Best Practices\n", "\n", "1. **Use Specific Exception Types**: Different exception types help the model understand the nature of the error:\n", "   - `ValueError`: Invalid input or data\n", "   - `FileNotFoundError`: Missing files or resources\n", "   - `ConnectionError`: Network or API issues\n", "   - `PermissionError`: Authentication or authorization failures\n", "   - `TimeoutError`: Service timeouts\n", "\n", "2. **Provide Clear Error Messages**: Include helpful context that can guide the model or user:\n", "   ```python\n", "   raise ValueError(f\"Invalid email format: '{email}'. Please provide a valid email address.\")\n", "   ```\n", "\n", "3. **Validate Inputs Early**: Check for invalid inputs at the beginning of your tool function:\n", "   ```python\n", "   if not isinstance(amount, (int, float)) or amount <= 0:\n", "       raise ValueError(\"Amount must be a positive number\")\n", "   ```\n", "\n", "4. **Handle External Dependencies**: Wrap calls to external services in try-catch blocks:\n", "   ```python\n", "   try:\n", "       response = requests.get(url, timeout=10)\n", "       response.raise_for_status()\n", "   except requests.exceptions.Timeout:\n", "       raise TimeoutError(\"Request timed out\")\n", "   except requests.exceptions.HTTPError as e:\n", "       raise ConnectionError(f\"HTTP error: {e}\")\n", "   ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Advanced Error Handling Example\n", "\n", "Here's a more comprehensive example showing robust error handling:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Any, Dict\n", "\n", "\n", "async def database_query(table: str, query: str) -> str:\n", "    \"\"\"Query a database table with comprehensive error handling.\"\"\"\n", "\n", "    # Input validation\n", "    if not table or not table.strip():\n", "        raise ValueError(\"Table name cannot be empty\")\n", "\n", "    if not query or not query.strip():\n", "        raise ValueError(\"Query cannot be empty\")\n", "\n", "    # Validate table name (simple validation)\n", "    allowed_tables = [\"users\", \"products\", \"orders\"]\n", "    if table not in allowed_tables:\n", "        raise ValueError(f\"Table '{table}' not found. Available tables: {', '.join(allowed_tables)}\")\n", "\n", "    # Simulate different database errors\n", "    if \"syntax_error\" in query.lower():\n", "        raise ValueError(\"SQL syntax error: Invalid query format\")\n", "\n", "    if \"permission_denied\" in query.lower():\n", "        raise PermissionError(f\"Access denied to table '{table}'. Insufficient permissions.\")\n", "\n", "    if \"connection_failed\" in query.lower():\n", "        raise ConnectionError(\"Database connection failed. Please try again later.\")\n", "\n", "    if \"timeout\" in query.lower():\n", "        raise TimeoutError(\"Query execution timed out. Try a simpler query.\")\n", "\n", "    # Simulate successful query\n", "    mock_results = {\n", "        \"users\": [{\"id\": 1, \"name\": \"<PERSON>\", \"email\": \"<EMAIL>\"}],\n", "        \"products\": [{\"id\": 1, \"name\": \"Laptop\", \"price\": 999.99}],\n", "        \"orders\": [{\"id\": 1, \"user_id\": 1, \"product_id\": 1, \"quantity\": 1}],\n", "    }\n", "\n", "    results = mock_results.get(table, [])\n", "    return f\"Query executed successfully. Results: {json.dumps(results, indent=2)}\"\n", "\n", "\n", "# Create an agent with the database tool\n", "db_agent = AssistantAgent(\n", "    name=\"database_assistant\",\n", "    model_client=model_client,\n", "    tools=[database_query],\n", "    system_message=\"You are a database assistant. Help users query the database safely. When errors occur, explain what went wrong and suggest corrections.\",\n", ")\n", "\n", "# Test various scenarios\n", "test_queries = [\n", "    \"Show me all users from the users table\",\n", "    \"Query the invalid_table with any query\",  # Invalid table\n", "    \"Run syntax_error query on users table\",  # SQL syntax error\n", "    \"Execute permission_denied query on orders\",  # Permission error\n", "]\n", "\n", "for query in test_queries:\n", "    print(f\"\\n=== Testing: {query} ===\")\n", "    result = await db_agent.run(task=query)\n", "    print(result.messages[-1].content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running an Agent in a Loop\n", "\n", "The {py:class}`~autogen_agentchat.agents.AssistantAgent` executes one\n", "step at a time: one model call, followed by one tool call (or parallel tool calls), and then\n", "an optional reflection.\n", "\n", "To run it in a loop, for example, running it until it stops producing\n", "tool calls, please refer to [Single-Agent Team](./teams.ipynb#single-agent-team)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Structured Output\n", "\n", "Structured output allows models to return structured JSON text with pre-defined schema\n", "provided by the application. Different from JSON-mode, the schema can be provided\n", "as a [Pydantic BaseModel](https://docs.pydantic.dev/latest/concepts/models/)\n", "class, which can also be used to validate the output.\n", "\n", "Once you specify the base model class in the `output_content_type` parameter\n", "of the {py:class}`~autogen_agentchat.agents.AssistantAgent` constructor,\n", "the agent will respond with a {py:class}`~autogen_agentchat.messages.StructuredMessage`\n", "whose `content`'s type is the type of the base model class.\n", "\n", "This way, you can integrate agent's response directly into your application\n", "and use the model's output as a structured object.\n", "\n", "```{note}\n", "When the `output_content_type` is set, it by default requires the agent to reflect on the tool use\n", "and return the a structured output message based on the tool call result.\n", "You can disable this behavior by setting `reflect_on_tool_use=False` explictly.\n", "```\n", "\n", "Structured output is also useful for incorporating Chain-of-Thought\n", "reasoning in the agent's responses.\n", "See the example below for how to use structured output with the assistant agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "I am happy.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["---------- assistant ----------\n", "{\n", "  \"thoughts\": \"The user explicitly states they are happy.\",\n", "  \"response\": \"happy\"\n", "}\n", "Thought:  The user explicitly states they are happy.\n", "Response:  happy\n"]}], "source": ["from typing import Literal\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "# The response format for the agent as a Pydantic base model.\n", "class AgentResponse(BaseModel):\n", "    thoughts: str\n", "    response: Literal[\"happy\", \"sad\", \"neutral\"]\n", "\n", "\n", "# Create an agent that uses the OpenAI GPT-4o model.\n", "model_client = OpenAIChatCompletionClient(model=\"gpt-4o\")\n", "agent = AssistantAgent(\n", "    \"assistant\",\n", "    model_client=model_client,\n", "    system_message=\"Categorize the input as happy, sad, or neutral following the JSON format.\",\n", "    # Define the output content type of the agent.\n", "    output_content_type=AgentResponse,\n", ")\n", "\n", "result = await <PERSON><PERSON><PERSON>(agent.run_stream(task=\"I am happy.\"))\n", "\n", "# Check the last message in the result, validate its type, and print the thoughts and response.\n", "assert isinstance(result.messages[-1], StructuredMessage)\n", "assert isinstance(result.messages[-1].content, AgentResponse)\n", "print(\"Thought: \", result.messages[-1].content.thoughts)\n", "print(\"Response: \", result.messages[-1].content.response)\n", "await model_client.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming Tokens\n", "\n", "You can stream the tokens generated by the model client by setting `model_client_stream=True`.\n", "This will cause the agent to yield {py:class}`~autogen_agentchat.messages.ModelClientStreamingChunkEvent` messages\n", "in {py:meth}`~autogen_agentchat.agents.BaseChatAgent.run_stream`.\n", "\n", "The underlying model API must support streaming tokens for this to work.\n", "Please check with your model provider to see if this is supported."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["source='user' models_usage=None metadata={} content='Name two cities in South America' type='TextMessage'\n", "source='assistant' models_usage=None metadata={} content='Two' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' cities' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' in' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' South' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' America' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' are' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' Buenos' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' Aires' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' in' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' Argentina' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' and' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' São' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' Paulo' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' in' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content=' Brazil' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=None metadata={} content='.' type='ModelClientStreamingChunkEvent'\n", "source='assistant' models_usage=RequestUsage(prompt_tokens=0, completion_tokens=0) metadata={} content='Two cities in South America are Buenos Aires in Argentina and São Paulo in Brazil.' type='TextMessage'\n", "messages=[TextMessage(source='user', models_usage=None, metadata={}, content='Name two cities in South America', type='TextMessage'), TextMessage(source='assistant', models_usage=RequestUsage(prompt_tokens=0, completion_tokens=0), metadata={}, content='Two cities in South America are Buenos Aires in Argentina and São Paulo in Brazil.', type='TextMessage')] stop_reason=None\n"]}], "source": ["model_client = OpenAIChatCompletionClient(model=\"gpt-4o\")\n", "\n", "streaming_assistant = AssistantAgent(\n", "    name=\"assistant\",\n", "    model_client=model_client,\n", "    system_message=\"You are a helpful assistant.\",\n", "    model_client_stream=True,  # Enable streaming tokens.\n", ")\n", "\n", "# Use an async function and asyncio.run() in a script.\n", "async for message in streaming_assistant.run_stream(task=\"Name two cities in South America\"):  # type: ignore\n", "    print(message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see the streaming chunks in the output above.\n", "The chunks are generated by the model client and are yielded by the agent as they are received.\n", "The final response, the concatenation of all the chunks, is yielded right after the last chunk."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Model Context\n", "\n", "{py:class}`~autogen_agentchat.agents.AssistantAgent` has a `model_context`\n", "parameter that can be used to pass in a {py:class}`~autogen_core.model_context.ChatCompletionContext`\n", "object. This allows the agent to use different model contexts, such as\n", "{py:class}`~autogen_core.model_context.BufferedChatCompletionContext` to\n", "limit the context sent to the model.\n", "\n", "By default, {py:class}`~autogen_agentchat.agents.AssistantAgent` uses\n", "the {py:class}`~autogen_core.model_context.UnboundedChatCompletionContext`\n", "which sends the full conversation history to the model. To limit the context\n", "to the last `n` messages, you can use the {py:class}`~autogen_core.model_context.BufferedChatCompletionContext`.\n", "To limit the context by token count, you can use the\n", "{py:class}`~autogen_core.model_context.TokenLimitedChatCompletionContext`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autogen_core.model_context import BufferedChatCompletionContext\n", "\n", "# Create an agent that uses only the last 5 messages in the context to generate responses.\n", "agent = AssistantAgent(\n", "    name=\"assistant\",\n", "    model_client=model_client,\n", "    tools=[web_search],\n", "    system_message=\"Use tools to solve tasks.\",\n", "    model_context=BufferedChatCompletionContext(buffer_size=5),  # Only use the last 5 messages in the context.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other Preset Agents\n", "\n", "The following preset agents are available:\n", "\n", "- {py:class}`~autogen_agentchat.agents.UserProxyAgent`: An agent that takes user input returns it as responses.\n", "- {py:class}`~autogen_agentchat.agents.CodeExecutorAgent`: An agent that can execute code.\n", "- {py:class}`~autogen_ext.agents.openai.OpenAIAssistantAgent`: An agent that is backed by an OpenAI Assistant, with ability to use custom tools.\n", "- {py:class}`~autogen_ext.agents.web_surfer.MultimodalWebSurfer`: A multi-modal agent that can search the web and visit web pages for information.\n", "- {py:class}`~autogen_ext.agents.file_surfer.FileSurfer`: An agent that can search and browse local files for information.\n", "- {py:class}`~autogen_ext.agents.video_surfer.VideoSurfer`: An agent that can watch videos for information."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Step\n", "\n", "Having explored the usage of the {py:class}`~autogen_agentchat.agents.AssistantAgent`, we can now proceed to the next section to learn about the teams feature in AgentChat.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- ## CodingAssistantAgent\n", "\n", "Generates responses (text and code) using an LLM upon receipt of a message. It takes a `system_message` argument that defines or sets the tone for how the agent's LLM should respond. \n", "\n", "```python\n", "\n", "writing_assistant_agent = CodingAssistantAgent(\n", "    name=\"writing_assistant_agent\",\n", "    system_message=\"You are a helpful assistant that solve tasks by generating text responses and code.\",\n", "    model_client=model_client,\n", ")\n", "`\n", "\n", "We can explore or test the behavior of the agent by sending a message to it using the  {py:meth}`~autogen_agentchat.agents.BaseChatAgent.on_messages`  method. \n", "\n", "```python\n", "result = await writing_assistant_agent.on_messages(\n", "    messages=[\n", "        TextMessage(content=\"What is the weather right now in France?\", source=\"user\"),\n", "    ],\n", "    cancellation_token=CancellationToken(),\n", ")\n", "print(result) -->"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}