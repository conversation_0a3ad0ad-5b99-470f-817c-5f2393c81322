﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" />
    <PackageReference Include="Microsoft.Extensions.AI" />
    <PackageReference Include="Octokit.Webhooks.AspNetCore" />
    <PackageReference Include="Octokit" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Microsoft.Extensions.Azure" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="Azure.ResourceManager.ContainerInstance" />
    <PackageReference Include="Azure.Storage.Files.Shares" />
    <PackageReference Include="Azure.Data.Tables" />
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="Google.Protobuf" />
    <PackageReference Include="Grpc.Tools" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Microsoft.AutoGen\Core.Grpc\Microsoft.AutoGen.Core.Grpc.csproj" />
    <ProjectReference Include="..\..\..\src\Microsoft.AutoGen\Core\Microsoft.AutoGen.Core.csproj" />
    <ProjectReference Include="..\DevTeam.ServiceDefaults\DevTeam.ServiceDefaults.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="..\Protos\messages.proto" Link="Protos\messages.proto" />
    <Protobuf Include="..\Protos\states.proto" Link="Protos\states.proto" />
  </ItemGroup>

</Project>
