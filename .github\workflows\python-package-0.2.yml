name: AgentChat 0.2 Pypi Package

on:
  push:
    tags:
      - "0.2.*"
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to deploy the package'
        required: true
permissions: {}
jobs:
  deploy:
    strategy:
      matrix:
        os: ["ubuntu-latest"]
        python-version: [3.10]
    runs-on: ${{ matrix.os }}
    environment:
      name: package
      url: https://pypi.org/p/autogen-agentchat
    permissions:
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.tag }}
      - name: Build
        shell: pwsh
        run: |
          pip install twine
          python setup.py sdist bdist_wheel
      - name: Publish package to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
