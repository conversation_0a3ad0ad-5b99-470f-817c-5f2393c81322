<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <IsTestProject>True</IsTestProject>
    <NoWarn>$(NoWarn);xUnit1013;SKEXP0110;CA1829;CA1826</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\samples\AgentChat\AutoGen.Basic.Sample\AutoGen.Basic.Sample.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.Anthropic\AutoGen.Anthropic.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\src\AutoGen\AutoGen.csproj" />
    <ProjectReference Include="..\AutoGen.Test.Share\AutoGen.Tests.Share.csproj" />
    <PackageReference Include="Microsoft.Extensions.AI" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ApprovalTests\square.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
