schedules:
- cron: "0 0 * * *"
  displayName: 'Daily midnight build (including CodeQL)'
  branches:
    include:
    - main
  always: true

parameters:
  - name: build_configuration
    displayName: Build configuration
    type: string
    default: Release
    values:
    - Release
    - Debug
  - name: include_suffix
    displayName: Append version suffix
    type: boolean
    default: true
  - name: version_suffix
    displayName: Version suffix
    type: string
    default: dev.$(Build.BuildNumber)
  - name: codesign
    displayName: Enable code signing
    type: boolean
    default: false
  - name: skip_test
    displayName: Skip tests
    type: boolean
    default: false
  - name: publish_nuget
    displayName: Publish to nuget.org
    type: boolean
    default: false
  - name: publish_nightly
    displayName: Publish to autogen-nightly
    type: boolean
    default: true
  - name: publish_artifacts
    displayName: Publish artifacts
    type: boolean
    default: false
  - name: runCodeQL3000
    default: false
    displayName: Run CodeQL3000 tasks
    type: boolean

variables:
- template: templates/vars.yaml

resources:
  repositories:
  - repository: 1ESPipelineTemplates
    type: git
    name: 1ESPipelineTemplates/1ESPipelineTemplates
    ref: refs/tags/release

extends:
  ${{ if eq(variables['System.TeamProject'], 'GitHub - PR Builds') }}:
    template: v1/1ES.Unofficial.PipelineTemplate.yml@1ESPipelineTemplates
  ${{ else }}:
    template: v1/1ES.Official.PipelineTemplate.yml@1ESPipelineTemplates
  parameters:
    settings:
      skipBuildTagsForGitHubPullRequests: true
    pool:
      name: $(pool_name)
      image: $(pool_image)
      os: windows
    stages:
    - stage: build_test
      displayName: Build and Tests
      jobs:
      - template: /.azure/pipelines/templates/build.yaml@self
        parameters:
          build_configuration: ${{ parameters.build_configuration }}
          include_suffix: ${{ parameters.include_suffix }}
          version_suffix: ${{ parameters.version_suffix }}
          codesign: ${{ parameters.codesign }}
          skip_test: ${{ parameters.skip_test }}
          publish_nightly: ${{ parameters.publish_nightly }}
          publish_nuget: ${{ parameters.publish_nuget }}
          runCodeQL3000: ${{ parameters.runCodeQL3000 }}
          publish_artifacts: ${{ parameters.publish_artifacts }}