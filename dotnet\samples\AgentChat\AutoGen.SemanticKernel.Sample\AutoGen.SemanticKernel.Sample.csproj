<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS8981;CS8600;CS8602;CS8604;CS8618;CS0219;SKEXP0054;SKEXP0050;SKEXP0110</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\AutoGen.OpenAI\AutoGen.OpenAI.csproj" />
    <ProjectReference Include="..\..\..\src\AutoGen.SemanticKernel\AutoGen.SemanticKernel.csproj" />
    <ProjectReference Include="..\..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Web" />
  </ItemGroup>

</Project>
