<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <IsPackable>false</IsPackable>
    <IsTestProject>True</IsTestProject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CA1829;CA1826</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\samples\AgentChat\AutoGen.OpenAI.Sample\AutoGen.OpenAI.Sample.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.OpenAI\AutoGen.OpenAI.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\AutoGen.Test.Share\AutoGen.Tests.Share.csproj" />
  </ItemGroup>
</Project>
