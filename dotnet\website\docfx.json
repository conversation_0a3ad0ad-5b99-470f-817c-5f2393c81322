{"metadata": [{"src": [{"files": ["src/**/*.csproj"], "src": "../"}], "dest": "api", "includePrivateMembers": false, "disableGitFeatures": false, "disableDefaultFilter": false, "noRestore": false, "namespaceLayout": "flattened", "memberLayout": "samePage", "allowCompilationErrors": false, "filter": "filterConfig.yml"}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "tutorial/**.md", "tutorial/**/toc.yml", "release_note/**.md", "release_note/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "output": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern", "template"], "globalMetadata": {"_appTitle": "AutoGen for .NET", "_appName": "AutoGen for .NET", "_appLogoPath": "images/ag.ico", "_appFooter": "AutoGen for .NET", "_appFaviconPath": "images/ag.ico", "_gitContribute": {"repo": "https://github.com/microsoft/autogen.git", "branch": "dotnet"}}, "postProcessors": [], "keepFileLink": false, "disableGitFeatures": false}}