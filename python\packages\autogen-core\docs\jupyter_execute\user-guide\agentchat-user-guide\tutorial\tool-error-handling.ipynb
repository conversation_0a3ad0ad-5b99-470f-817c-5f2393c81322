{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tool Error Handling Guide\n", "\n", "This guide provides comprehensive examples and best practices for handling errors in tools\n", "when using AutoGen AgentChat. Proper error handling ensures that agents can gracefully\n", "handle failures and provide meaningful feedback to users.\n", "\n", "## Overview\n", "\n", "When tools encounter errors during execution, AutoGen automatically:\n", "\n", "1. **Catches exceptions** raised by tool functions\n", "2. **Sets the `is_error=True` flag** in the `FunctionExecutionResult`\n", "3. **Passes the error message** to the model as the tool result\n", "4. **Allows the model** to understand what went wrong and potentially take corrective action\n", "\n", "This automatic error handling means you can simply **raise exceptions** in your tools,\n", "and AutoGen will handle the rest.\n", "\n", "## Basic Error Handling\n", "\n", "The simplest approach is to raise exceptions when errors occur:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "# Set up the model client\n", "model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    # api_key=\"YOUR_API_KEY\",  # Set your API key\n", ")\n", "\n", "\n", "async def simple_calculator(operation: str, a: float, b: float) -> str:\n", "    \"\"\"Perform basic arithmetic operations.\"\"\"\n", "\n", "    # Input validation - simply raise exceptions\n", "    if operation not in [\"add\", \"subtract\", \"multiply\", \"divide\"]:\n", "        raise ValueError(f\"Unsupported operation: {operation}. Supported: add, subtract, multiply, divide\")\n", "\n", "    if operation == \"divide\" and b == 0:\n", "        raise ZeroDivisionError(\"Cannot divide by zero\")\n", "\n", "    # Perform calculation\n", "    if operation == \"add\":\n", "        result = a + b\n", "    elif operation == \"subtract\":\n", "        result = a - b\n", "    elif operation == \"multiply\":\n", "        result = a * b\n", "    elif operation == \"divide\":\n", "        result = a / b\n", "\n", "    return f\"Result: {a} {operation} {b} = {result}\"\n", "\n", "\n", "# Create an agent with the calculator tool\n", "calculator_agent = AssistantAgent(\n", "    name=\"calculator\",\n", "    model_client=model_client,\n", "    tools=[simple_calculator],\n", "    system_message=\"You are a helpful calculator assistant. When calculations fail, explain the error clearly.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test the error handling:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test successful calculation\n", "result = await calculator_agent.run(task=\"Calculate 10 + 5\")\n", "print(\"=== Successful Calculation ===\")\n", "print(result.messages[-1].content)\n", "print()\n", "\n", "# Test division by zero error\n", "result = await calculator_agent.run(task=\"Divide 10 by 0\")\n", "print(\"=== Division by Zero Error ===\")\n", "print(result.messages[-1].content)\n", "print()\n", "\n", "# Test invalid operation error\n", "result = await calculator_agent.run(task=\"Calculate the square root of 16\")\n", "print(\"=== Invalid Operation Error ===\")\n", "print(result.messages[-1].content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exception Types and Their Meanings\n", "\n", "Using specific exception types helps models understand the nature of errors:\n", "\n", "| Exception Type | Use Case | Example |\n", "|----------------|----------|----------|\n", "| `ValueError` | Invalid input or data | Invalid email format, negative age |\n", "| `FileNotFoundError` | Missing files or resources | File doesn't exist, missing configuration |\n", "| `ConnectionError` | Network or API issues | API unreachable, network timeout |\n", "| `PermissionError` | Authentication/authorization | Invalid API key, insufficient permissions |\n", "| `TimeoutError` | Service timeouts | Slow API response, database timeout |\n", "| `RuntimeError` | General runtime issues | Service unavailable, unexpected state |\n", "\n", "## Real-World Error Handling Examples\n", "\n", "### File Operations Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "\n", "async def file_reader(file_path: str) -> str:\n", "    \"\"\"Read content from a file with comprehensive error handling.\"\"\"\n", "\n", "    # Input validation\n", "    if not file_path or not file_path.strip():\n", "        raise ValueError(\"File path cannot be empty\")\n", "\n", "    # Convert to Path object for better handling\n", "    path = Path(file_path)\n", "\n", "    # Check if file exists\n", "    if not path.exists():\n", "        raise FileNotFoundError(f\"File not found: {file_path}\")\n", "\n", "    # Check if it's actually a file (not a directory)\n", "    if not path.is_file():\n", "        raise ValueError(f\"Path is not a file: {file_path}\")\n", "\n", "    try:\n", "        # Attempt to read the file using aiofiles for async compatibility\n", "        import aiofiles\n", "\n", "        async with aiofiles.open(path, \"r\", encoding=\"utf-8\") as f:\n", "            content = await f.read()\n", "\n", "        if not content.strip():\n", "            return f\"File '{file_path}' is empty\"\n", "\n", "        return f\"Content of '{file_path}':\\n{content[:500]}{'...' if len(content) > 500 else ''}\"\n", "\n", "    except PermissionError as e:\n", "        raise PermissionError(f\"Permission denied reading file: {file_path}\") from e\n", "    except UnicodeDecodeError as e:\n", "        raise ValueError(f\"File '{file_path}' contains invalid text encoding\") from e\n", "    except OSError as e:\n", "        raise RuntimeError(f\"System error reading file '{file_path}': {e}\") from e\n", "\n", "\n", "# Create file operations agent\n", "file_agent = AssistantAgent(\n", "    name=\"file_assistant\",\n", "    model_client=model_client,\n", "    tools=[file_reader],\n", "    system_message=\"You help users read files. When file operations fail, explain the issue and suggest solutions.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### API Integration Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Any, Dict\n", "\n", "\n", "async def api_client(endpoint: str, method: str = \"GET\", data: str = \"\") -> str:\n", "    \"\"\"Make API calls with robust error handling.\"\"\"\n", "\n", "    # Input validation\n", "    if not endpoint or not endpoint.strip():\n", "        raise ValueError(\"API endpoint cannot be empty\")\n", "\n", "    if method.upper() not in [\"GET\", \"POST\", \"PUT\", \"DELETE\"]:\n", "        raise ValueError(f\"Unsupported HTTP method: {method}. Supported: GET, POST, PUT, DELETE\")\n", "\n", "    # Validate JSON data for POST/PUT requests\n", "    if method.upper() in [\"POST\", \"PUT\"] and data:\n", "        try:\n", "            json.loads(data)\n", "        except json.JSONDecodeError as e:\n", "            raise ValueError(f\"Invalid JSON data: {e}\") from e\n", "\n", "    # Simulate different API scenarios\n", "    if \"notfound\" in endpoint.lower():\n", "        raise FileNotFoundError(f\"API endpoint not found: {endpoint}\")\n", "\n", "    if \"unauthorized\" in endpoint.lower():\n", "        raise PermissionError(\"API authentication failed. Please check your API key.\")\n", "\n", "    if \"timeout\" in endpoint.lower():\n", "        raise TimeoutError(f\"Request to {endpoint} timed out. The service may be slow or unavailable.\")\n", "\n", "    if \"ratelimit\" in endpoint.lower():\n", "        raise RuntimeError(\"API rate limit exceeded. Please wait before making more requests.\")\n", "\n", "    if \"servererror\" in endpoint.lower():\n", "        raise ConnectionError(f\"Server error from {endpoint}. The service may be temporarily unavailable.\")\n", "\n", "    # Simulate successful response\n", "    mock_response = {\n", "        \"status\": \"success\",\n", "        \"endpoint\": endpoint,\n", "        \"method\": method.upper(),\n", "        \"data\": json.loads(data) if data else None,\n", "        \"result\": \"API call completed successfully\",\n", "    }\n", "\n", "    return f\"API Response: {json.dumps(mock_response, indent=2)}\"\n", "\n", "\n", "# Create API client agent\n", "api_agent = AssistantAgent(\n", "    name=\"api_assistant\",\n", "    model_client=model_client,\n", "    tools=[api_client],\n", "    system_message=\"You help users make API calls. When API calls fail, explain the error and suggest troubleshooting steps.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing Error <PERSON>\n", "\n", "Let's test various error scenarios to see how the agents handle them:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test API error scenarios\n", "api_test_cases = [\n", "    \"Make a GET request to /api/users\",  # Success\n", "    \"Call the /api/notfound endpoint\",  # 404 error\n", "    \"Access /api/unauthorized\",  # Auth error\n", "    \"Request /api/timeout data\",  # Timeout error\n", "    \"Call /api/ratelimit endpoint\",  # Rate limit error\n", "]\n", "\n", "print(\"=== API Error Handling Tests ===\")\n", "for i, task in enumerate(api_test_cases, 1):\n", "    print(f\"\\n--- Test {i}: {task} ---\")\n", "    result = await api_agent.run(task=task)\n", "    print(\n", "        result.messages[-1].content[:200] + \"...\"\n", "        if len(result.messages[-1].content) > 200\n", "        else result.messages[-1].content\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Best Practices Summary\n", "\n", "### 1. Exception Types\n", "Use appropriate exception types to help models understand error categories:\n", "\n", "```python\n", "# Good: Specific exception types\n", "if not email_is_valid(email):\n", "    raise ValueError(f\"Invalid email format: {email}\")\n", "\n", "if api_key_expired():\n", "    raise PermissionError(\"API key has expired\")\n", "\n", "# Avoid: Generic exceptions\n", "if something_wrong:\n", "    raise Exception(\"Something went wrong\")  # Too vague\n", "```\n", "\n", "### 2. <PERSON> Error Messages\n", "Provide actionable error messages:\n", "\n", "```python\n", "# Good: Actionable message\n", "raise ValueError(\"File size exceeds 10MB limit. Please use a smaller file.\")\n", "\n", "# Avoid: Vague message\n", "raise ValueError(\"Invalid file\")  # What's invalid about it?\n", "```\n", "\n", "### 3. Input Validation\n", "Validate inputs early and provide helpful feedback:\n", "\n", "```python\n", "async def process_age(age: int) -> str:\n", "    # Validate early\n", "    if not isinstance(age, int):\n", "        raise TypeError(\"Age must be an integer\")\n", "    \n", "    if age < 0:\n", "        raise ValueError(\"Age cannot be negative\")\n", "    \n", "    if age > 150:\n", "        raise ValueError(\"Age seems unrealistic (>150). Please check the input.\")\n", "    \n", "    return f\"Processing age: {age}\"\n", "```\n", "\n", "### 4. External Service Handling\n", "Wrap external calls with specific error handling:\n", "\n", "```python\n", "import requests\n", "\n", "async def call_external_api(url: str) -> str:\n", "    try:\n", "        response = requests.get(url, timeout=10)\n", "        response.raise_for_status()\n", "        return response.text\n", "    except requests.exceptions.Timeout:\n", "        raise TimeoutError(f\"Request to {url} timed out\")\n", "    except requests.exceptions.ConnectionError:\n", "        raise ConnectionError(f\"Failed to connect to {url}\")\n", "    except requests.exceptions.HTTPError as e:\n", "        if e.response.status_code == 401:\n", "            raise PermissionError(\"Authentication failed\")\n", "        elif e.response.status_code == 404:\n", "            raise FileNotFoundError(f\"Endpoint not found: {url}\")\n", "        else:\n", "            raise ConnectionError(f\"HTTP error {e.response.status_code}: {e}\")\n", "```\n", "\n", "### 5. Don't Suppress Errors\n", "Let exceptions bubble up rather than returning error strings:\n", "\n", "```python\n", "# Good: Let AutoGen handle the error\n", "async def divide_numbers(a: float, b: float) -> str:\n", "    if b == 0:\n", "        raise ZeroDivisionError(\"Cannot divide by zero\")\n", "    return str(a / b)\n", "\n", "# Avoid: Returning error strings\n", "async def divide_numbers_bad(a: float, b: float) -> str:\n", "    if b == 0:\n", "        return \"Error: Cannot divide by zero\"  # Model won't know this is an error\n", "    return str(a / b)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model-Specific Error Handling\n", "\n", "Different model providers handle tool errors differently:\n", "\n", "- **OpenAI Models**: Receive error information and can reason about failures\n", "- **Anthropic Models**: Have explicit support for tool error indication via the `is_error` field\n", "- **Other Models**: Behavior may vary, but the `is_error` field provides a standard way to indicate failures\n", "\n", "The `is_error` field in `FunctionExecutionResult` ensures consistent error communication across all model providers.\n", "\n", "## Conclusion\n", "\n", "Proper error handling in tools is essential for building robust AI applications. By following these practices:\n", "\n", "1. **Simply raise exceptions** when errors occur\n", "2. **Use specific exception types** to categorize errors\n", "3. **Provide clear, actionable error messages**\n", "4. **Validate inputs early** in your tool functions\n", "5. **Handle external dependencies** with appropriate error catching\n", "\n", "Your agents will be able to gracefully handle failures and provide meaningful feedback to users, making your applications more reliable and user-friendly."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}