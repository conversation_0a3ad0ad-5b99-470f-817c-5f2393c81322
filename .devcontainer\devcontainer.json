// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-outside-of-docker-compose
{
	"name": "AutoGen devcontainer",
	"dockerComposeFile": "docker-compose.yml",
	"service": "devcontainer",
	"workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",

	// Use this environment variable if you need to bind mount your local source code into a new container.
	"remoteEnv": {
		"LOCAL_WORKSPACE_FOLDER": "${localWorkspaceFolder}"
	},

	"features": {
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {
			"moby": true,
			"installDockerBuildx": true,
			"version": "latest",
			"dockerDashComposeVersion": "none"
		},
		"ghcr.io/elanhasson/devcontainer-features/dotnet-aspire-daily:1": {},
		"ghcr.io/devcontainers/features/azure-cli:1": {},
		"ghcr.io/devcontainers/features/git:1": {},
		"ghcr.io/devcontainers/features/dotnet:2": {},
		"ghcr.io/azure/azure-dev/azd:0": {},
		"ghcr.io/devcontainers/features/python:1": {}
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "bash .devcontainer/startup.sh",

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	"remoteUser": "root",
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.debugpy",
				"GitHub.copilot",
				"ms-dotnettools.csdevkit",
				"ms-dotnettools.vscodeintellicode-csharp",
				"github.vscode-github-actions"
			]
		}
	}
}