<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591;CS1573;CA1852</NoWarn>
  </PropertyGroup>

  
  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>AutoGen.WebAPI</Title>
    <Description>
      Turn an `AutoGen.Core.IAgent` into a RESTful API.
    </Description>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoGen.Core\AutoGen.Core.csproj" />
  </ItemGroup>
</Project>
