syntax = "proto3";

package HelloAgents;

option csharp_namespace = "Microsoft.AutoGen.Contracts";
message TextMessage {
    string textMessage = 1;
    string source = 2;
}
message Input {
      string message = 1;
}
message InputProcessed {
      string route = 1;
}
message Output {
      string message = 1;
}
message OutputWritten {
      string route = 1;
}
message IOError {
      string message = 1;
}
message NewMessageReceived {
      string message = 1;
}
message ResponseGenerated {
      string response = 1;
}
message GoodBye {
      string message = 1;
}
message MessageStored {
      string message = 1;
}
message ConversationClosed {
  string user_id = 1;
  string user_message = 2;
}
message Shutdown {
      string message = 1;
}
