﻿﻿//----------------------
// <auto-generated>
//     This code was generated by a tool.
// </auto-generated>
//----------------------
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System;
using AutoGen.Core;

namespace AutoGen.SourceGenerator.Tests
{
    public partial class FunctionExamples
    {

        private class AddAsyncSchema
        {
            [JsonPropertyName(@"a")]
			public System.Int32 a {get; set;}
            [JsonPropertyName(@"b")]
			public System.Int32 b {get; set;}
        }

        public System.Threading.Tasks.Task`1[System.String] AddAsyncWrapper(string arguments)
        {
            var schema = JsonSerializer.Deserialize<AddAsyncSchema>(
                arguments, 
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                });

            return AddAsync(schema.a, schema.b);
        }

        public FunctionContract AddAsyncFunctionContract
        {
            get => new FunctionContract
            {
                Name = @"AddAsync",
                Description = @"Add two numbers.",
                ReturnType = typeof(System.Threading.Tasks.Task`1[System.String]),
                Parameters = new global::AutoGen.Core.FunctionParameterContract[]
                {
                    new FunctionParameterContract
                    {
                        Name = @"a",
                        Description = @"The first number.",
                        ParameterType = typeof(System.Int32),
                        IsRequired = true,
                    },
                    new FunctionParameterContract
                    {
                        Name = @"b",
                        Description = @"The second number.",
                        ParameterType = typeof(System.Int32),
                        IsRequired = true,
                    },
                },
            };
        }
    }
}

