<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
    <IsPackable>false</IsPackable>
    <IsTestProject>True</IsTestProject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CA1829;CA1826</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\AutoGen.OpenAI.V1\AutoGen.OpenAI.V1.csproj" />
    <ProjectReference Include="..\..\src\AutoGen.SourceGenerator\AutoGen.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\AutoGen.Test.Share\AutoGen.Tests.Share.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ApprovalTests\OpenAIMessageTests.BasicMessageTest.approved.txt">
      <ParentFile>$([System.String]::Copy('%(FileName)').Split('.')[0])</ParentFile>
      <ParentExtension>$(ProjectExt.Replace('proj', ''))</ParentExtension>
      <DependentUpon>%(ParentFile)%(ParentExtension)</DependentUpon>
    </None>
  </ItemGroup>
</Project>
