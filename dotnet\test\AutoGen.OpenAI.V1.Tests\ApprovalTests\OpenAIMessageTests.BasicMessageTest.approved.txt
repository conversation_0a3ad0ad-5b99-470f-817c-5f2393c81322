[{"OriginalMessage": "TextMessage(system, You are a helpful AI assistant, )", "ConvertedMessages": [{"Name": null, "Role": "system", "Content": "You are a helpful AI assistant"}]}, {"OriginalMessage": "TextMessage(user, Hello, user)", "ConvertedMessages": [{"Role": "user", "Content": "Hello", "Name": "user", "MultiModaItem": null}]}, {"OriginalMessage": "TextM<PERSON>age(assistant, How can I help you?, assistant)", "ConvertedMessages": [{"Role": "assistant", "Content": "How can I help you?", "Name": "assistant", "TooCall": [], "FunctionCallName": null, "FunctionCallArguments": null}]}, {"OriginalMessage": "ImageMessage(user, https://example.com/image.png, user)", "ConvertedMessages": [{"Role": "user", "Content": null, "Name": "user", "MultiModaItem": [{"Type": "Image", "ImageUrl": {"Url": "https://example.com/image.png", "Detail": null}}]}]}, {"OriginalMessage": "MultiModalMessage(assistant, user)\n\tTextMessage(user, Hello, user)\n\tImageMessage(user, https://example.com/image.png, user)", "ConvertedMessages": [{"Role": "user", "Content": null, "Name": "user", "MultiModaItem": [{"Type": "Text", "Text": "Hello"}, {"Type": "Image", "ImageUrl": {"Url": "https://example.com/image.png", "Detail": null}}]}]}, {"OriginalMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(assistant)\n\t<PERSON><PERSON><PERSON><PERSON>(test, test, )", "ConvertedMessages": [{"Role": "assistant", "Content": "", "Name": null, "TooCall": [{"Type": "Function", "Name": "test", "Arguments": "test", "Id": "test"}], "FunctionCallName": null, "FunctionCallArguments": null}]}, {"OriginalMessage": "ToolCallResultMessage(user)\n\tToolCall(test, test, result)", "ConvertedMessages": [{"Role": "tool", "Content": "result", "ToolCallId": "test"}]}, {"OriginalMessage": "ToolCallResultMessage(user)\n\tToolCall(result, test, test)\n\tToolCall(result, test, test)", "ConvertedMessages": [{"Role": "tool", "Content": "test", "ToolCallId": "result_0"}, {"Role": "tool", "Content": "test", "ToolCallId": "result_1"}]}, {"OriginalMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(assistant)\n\t<PERSON><PERSON><PERSON><PERSON>(test, test, )\n\t<PERSON><PERSON><PERSON><PERSON>(test, test, )", "ConvertedMessages": [{"Role": "assistant", "Content": "", "Name": null, "TooCall": [{"Type": "Function", "Name": "test", "Arguments": "test", "Id": "test_0"}, {"Type": "Function", "Name": "test", "Arguments": "test", "Id": "test_1"}], "FunctionCallName": null, "FunctionCallArguments": null}]}, {"OriginalMessage": "Aggregate<PERSON><PERSON><PERSON>(assistant)\n\t<PERSON>l<PERSON>allM<PERSON>age(assistant)\n\t<PERSON><PERSON><PERSON><PERSON>(test, test, )\n\tToolCallResultM<PERSON>age(assistant)\n\t<PERSON><PERSON><PERSON><PERSON>(test, test, result)", "ConvertedMessages": [{"Role": "assistant", "Content": "", "Name": null, "TooCall": [{"Type": "Function", "Name": "test", "Arguments": "test", "Id": "test"}], "FunctionCallName": null, "FunctionCallArguments": null}, {"Role": "tool", "Content": "result", "ToolCallId": "test"}]}]