{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Model Clients\n", "\n", "AutoGen provides a suite of built-in model clients for using ChatCompletion API.\n", "All model clients implement the {py:class}`~autogen_core.models.ChatCompletionClient` protocol class."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Currently we support the following built-in model clients:\n", "* {py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient`: for OpenAI models and models with OpenAI API compatibility (e.g., Gemini).\n", "* {py:class}`~autogen_ext.models.openai.AzureOpenAIChatCompletionClient`: for Azure OpenAI models.\n", "* {py:class}`~autogen_ext.models.azure.AzureAIChatCompletionClient`: for GitHub models and models hosted on Azure.\n", "* {py:class}`~autogen_ext.models.ollama.OllamaChatCompletionClient` (Experimental): for local models hosted on Ollama.\n", "* {py:class}`~autogen_ext.models.anthropic.AnthropicChatCompletionClient` (Experimental): for models hosted on Anthropic.\n", "* {py:class}`~autogen_ext.models.semantic_kernel.SKChatCompletionAdapter`: adapter for Semantic Kernel AI connectors.\n", "\n", "For more information on how to use these model clients, please refer to the documentation of each client."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Log Model Calls\n", "\n", "AutoGen uses standard Python logging module to log events like model calls and responses.\n", "The logger name is {py:attr}`autogen_core.EVENT_LOGGER_NAME`, and the event type is `LLMCall`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "from autogen_core import EVENT_LOGGER_NAME\n", "\n", "logging.basicConfig(level=logging.WARNING)\n", "logger = logging.getLogger(EVENT_LOGGER_NAME)\n", "logger.add<PERSON><PERSON><PERSON>(logging.StreamHandler())\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Call Model Client\n", "\n", "To call a model client, you can use the {py:meth}`~autogen_core.models.ChatCompletionClient.create` method.\n", "This example uses the {py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient` to call an OpenAI model."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["finish_reason='stop' content='The capital of France is Paris.' usage=RequestUsage(prompt_tokens=15, completion_tokens=8) cached=False logprobs=None thought=None\n"]}], "source": ["from autogen_core.models import UserMessage\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4\", temperature=0.3\n", ")  # assuming OPENAI_API_KEY is set in the environment.\n", "\n", "result = await model_client.create([UserMessage(content=\"What is the capital of France?\", source=\"user\")])\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming Tokens\n", "\n", "You can use the {py:meth}`~autogen_core.models.ChatCompletionClient.create_stream` method to create a\n", "chat completion request with streaming token chunks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Streamed responses:\n", "In the heart of an ancient forest, beneath the shadow of snow-capped peaks, a dragon named <PERSON><PERSON> lived secretly for centuries. <PERSON><PERSON> was unlike any dragon from the old tales; her scales shimmered with a deep emerald hue, each scale engraved with symbols of lost wisdom. The villagers in the nearby valley spoke of mysterious lights dancing across the night sky, but none dared venture close enough to solve the enigma.\n", "\n", "One cold winter's eve, a young girl named <PERSON><PERSON>, brimming with curiosity and armed with the innocence of youth, wandered into <PERSON><PERSON>’s domain. Instead of fire and fury, she found warmth and a gentle gaze. The dragon shared stories of a world long forgotten and in return, <PERSON><PERSON> gifted her simple stories of human life, rich in laughter and scent of earth.\n", "\n", "From that night on, the villagers noticed subtle changes—the crops grew taller, and the air seemed sweeter. <PERSON><PERSON> had infused the valley with ancient magic, a guardian of balance, watching quietly as her new friend thrived under the stars. And so, <PERSON><PERSON> and <PERSON><PERSON>’s bond marked the beginning of a timeless friendship that spun tales of hope whispered through the leaves of the ever-verdant forest.\n", "\n", "------------\n", "\n", "The complete response:\n", "In the heart of an ancient forest, beneath the shadow of snow-capped peaks, a dragon named <PERSON><PERSON> lived secretly for centuries. <PERSON><PERSON> was unlike any dragon from the old tales; her scales shimmered with a deep emerald hue, each scale engraved with symbols of lost wisdom. The villagers in the nearby valley spoke of mysterious lights dancing across the night sky, but none dared venture close enough to solve the enigma.\n", "\n", "One cold winter's eve, a young girl named <PERSON><PERSON>, brimming with curiosity and armed with the innocence of youth, wandered into <PERSON><PERSON>’s domain. Instead of fire and fury, she found warmth and a gentle gaze. The dragon shared stories of a world long forgotten and in return, <PERSON><PERSON> gifted her simple stories of human life, rich in laughter and scent of earth.\n", "\n", "From that night on, the villagers noticed subtle changes—the crops grew taller, and the air seemed sweeter. <PERSON><PERSON> had infused the valley with ancient magic, a guardian of balance, watching quietly as her new friend thrived under the stars. And so, <PERSON><PERSON> and <PERSON><PERSON>’s bond marked the beginning of a timeless friendship that spun tales of hope whispered through the leaves of the ever-verdant forest.\n", "\n", "\n", "------------\n", "\n", "The token usage was:\n", "RequestUsage(prompt_tokens=0, completion_tokens=0)\n"]}], "source": ["from autogen_core.models import CreateResult, UserMessage\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "model_client = OpenAIChatCompletionClient(model=\"gpt-4o\")  # assuming OPENAI_API_KEY is set in the environment.\n", "\n", "messages = [\n", "    UserMessage(content=\"Write a very short story about a dragon.\", source=\"user\"),\n", "]\n", "\n", "# Create a stream.\n", "stream = model_client.create_stream(messages=messages)\n", "\n", "# Iterate over the stream and print the responses.\n", "print(\"Streamed responses:\")\n", "async for chunk in stream:  # type: ignore\n", "    if isinstance(chunk, str):\n", "        # The chunk is a string.\n", "        print(chunk, flush=True, end=\"\")\n", "    else:\n", "        # The final chunk is a CreateResult object.\n", "        assert isinstance(chunk, CreateResult) and isinstance(chunk.content, str)\n", "        # The last response is a CreateResult object with the complete message.\n", "        print(\"\\n\\n------------\\n\")\n", "        print(\"The complete response:\", flush=True)\n", "        print(chunk.content, flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{note}\n", "The last response in the streaming response is always the final response\n", "of the type {py:class}`~autogen_core.models.CreateResult`.\n", "```\n", "\n", "```{note}\n", "The default usage response is to return zero values. To enable usage, \n", "see {py:meth}`~autogen_ext.models.openai.BaseOpenAIChatCompletionClient.create_stream`\n", "for more details.\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Structured Output\n", "\n", "Structured output can be enabled by setting the `response_format` field in\n", "{py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient` and {py:class}`~autogen_ext.models.openai.AzureOpenAIChatCompletionClient` to\n", "as a [Pydantic BaseModel](https://docs.pydantic.dev/latest/concepts/models/) class.\n", "\n", "```{note}\n", "Structured output is only available for models that support it. It also\n", "requires the model client to support structured output as well.\n", "Currently, the {py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient`\n", "and {py:class}`~autogen_ext.models.openai.AzureOpenAIChatCompletionClient`\n", "support structured output.\n", "```"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I'm glad to hear that you're feeling happy! It's such a great emotion that can brighten your whole day. Is there anything in particular that's bringing you joy today? 😊\n", "happy\n"]}], "source": ["from typing import Literal\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "# The response format for the agent as a Pydantic base model.\n", "class AgentResponse(BaseModel):\n", "    thoughts: str\n", "    response: Literal[\"happy\", \"sad\", \"neutral\"]\n", "\n", "\n", "# Create an agent that uses the OpenAI GPT-4o model with the custom response format.\n", "model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o\",\n", "    response_format=AgentResponse,  # type: ignore\n", ")\n", "\n", "# Send a message list to the model and await the response.\n", "messages = [\n", "    UserMessage(content=\"I am happy.\", source=\"user\"),\n", "]\n", "response = await model_client.create(messages=messages)\n", "assert isinstance(response.content, str)\n", "parsed_response = AgentResponse.model_validate_json(response.content)\n", "print(parsed_response.thoughts)\n", "print(parsed_response.response)\n", "\n", "# Close the connection to the model client.\n", "await model_client.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You also use the `extra_create_args` parameter in the {py:meth}`~autogen_ext.models.openai.BaseOpenAIChatCompletionClient.create` method\n", "to set the `response_format` field so that the structured output can be configured for each request."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Caching Model Responses\n", "\n", "`autogen_ext` implements {py:class}`~autogen_ext.models.cache.ChatCompletionCache` that can wrap any {py:class}`~autogen_core.models.ChatCompletionClient`. Using this wrapper avoids incurring token usage when querying the underlying client with the same prompt multiple times.\n", "\n", "{py:class}`~autogen_core.models.ChatCompletionCache` uses a {py:class}`~autogen_core.CacheStore` protocol. We have implemented some useful variants of {py:class}`~autogen_core.CacheStore` including {py:class}`~autogen_ext.cache_store.diskcache.DiskCacheStore` and {py:class}`~autogen_ext.cache_store.redis.RedisStore`.\n", "\n", "Here's an example of using `diskcache` for local caching:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# pip install -U \"autogen-ext[openai, diskcache]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["import asyncio\n", "import tempfile\n", "\n", "from autogen_core.models import UserMessage\n", "from autogen_ext.cache_store.diskcache import DiskCacheStore\n", "from autogen_ext.models.cache import CHAT_CACHE_VALUE_TYPE, ChatCompletionCache\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "from diskcache import Cache\n", "\n", "\n", "async def main() -> None:\n", "    with tempfile.TemporaryDirectory() as tmpdirname:\n", "        # Initialize the original client\n", "        openai_model_client = OpenAIChatCompletionClient(model=\"gpt-4o\")\n", "\n", "        # Then initialize the CacheStore, in this case with diskcache.Cache.\n", "        # You can also use redis like:\n", "        # from autogen_ext.cache_store.redis import RedisStore\n", "        # import redis\n", "        # redis_instance = redis.Redis()\n", "        # cache_store = RedisCacheStore[CHAT_CACHE_VALUE_TYPE](redis_instance)\n", "        cache_store = DiskCacheStore[CHAT_CACHE_VALUE_TYPE](Cache(tmpdirname))\n", "        cache_client = ChatCompletionCache(openai_model_client, cache_store)\n", "\n", "        response = await cache_client.create([UserMessage(content=\"Hello, how are you?\", source=\"user\")])\n", "        print(response)  # Should print response from OpenAI\n", "        response = await cache_client.create([UserMessage(content=\"Hello, how are you?\", source=\"user\")])\n", "        print(response)  # Should print cached response\n", "\n", "        await openai_model_client.close()\n", "        await cache_client.close()\n", "\n", "\n", "asyncio.run(main())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inspecting `cached_client.total_usage()` (or `model_client.total_usage()`) before and after a cached response should yield idential counts.\n", "\n", "Note that the caching is sensitive to the exact arguments provided to `cached_client.create` or `cached_client.create_stream`, so changing `tools` or `json_output` arguments might lead to a cache miss."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build an Agent with a Model Client\n", "\n", "Let's create a simple AI agent that can respond to messages using the ChatCompletion API."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "from autogen_core import MessageContext, RoutedAgent, SingleThreadedAgentRuntime, message_handler\n", "from autogen_core.models import ChatCompletionClient, SystemMessage, UserMessage\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "\n", "@dataclass\n", "class Message:\n", "    content: str\n", "\n", "\n", "class SimpleAgent(RoutedAgent):\n", "    def __init__(self, model_client: ChatCompletionClient) -> None:\n", "        super().__init__(\"A simple agent\")\n", "        self._system_messages = [SystemMessage(content=\"You are a helpful AI assistant.\")]\n", "        self._model_client = model_client\n", "\n", "    @message_handler\n", "    async def handle_user_message(self, message: Message, ctx: MessageContext) -> Message:\n", "        # Prepare input to the chat completion model.\n", "        user_message = UserMessage(content=message.content, source=\"user\")\n", "        response = await self._model_client.create(\n", "            self._system_messages + [user_message], cancellation_token=ctx.cancellation_token\n", "        )\n", "        # Return with the model's response.\n", "        assert isinstance(response.content, str)\n", "        return Message(content=response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `SimpleAgent` class is a subclass of the\n", "{py:class}`autogen_core.RoutedAgent` class for the convenience of automatically routing messages to the appropriate handlers.\n", "It has a single handler, `handle_user_message`, which handles message from the user. It uses the `ChatCompletionClient` to generate a response to the message.\n", "It then returns the response to the user, following the direct communication model.\n", "\n", "```{note}\n", "The `cancellation_token` of the type {py:class}`autogen_core.CancellationToken` is used to cancel\n", "asynchronous operations. It is linked to async calls inside the message handlers\n", "and can be used by the caller to cancel the handlers.\n", "```"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Seattle is a vibrant city with a wide range of activities and attractions. Here are some fun things to do in Seattle:\n", "\n", "1. **Space Needle**: Visit this iconic observation tower for stunning views of the city and surrounding mountains.\n", "\n", "2. **Pike Place Market**: Explore this historic market where you can see the famous fish toss, buy local produce, and find unique crafts and eateries.\n", "\n", "3. **Museum of Pop Culture (MoPOP)**: Dive into the world of contemporary culture, music, and science fiction at this interactive museum.\n", "\n", "4. **Chihuly Garden and Glass**: <PERSON> at the beautiful glass art installations by artist <PERSON>, located right next to the Space Needle.\n", "\n", "5. **Seattle Aquarium**: Discover the diverse marine life of the Pacific Northwest at this engaging aquarium.\n", "\n", "6. **Seattle Art Museum**: Explore a vast collection of art from around the world, including contemporary and indigenous art.\n", "\n", "7. **Kerry Park**: For one of the best views of the Seattle skyline, head to this small park on Queen Anne Hill.\n", "\n", "8. **Ballard Locks**: Watch boats pass through the locks and observe the salmon ladder to see salmon migrating.\n", "\n", "9. **Ferry to Bainbridge Island**: Take a scenic ferry ride across Puget Sound to enjoy charming shops, restaurants, and beautiful natural scenery.\n", "\n", "10. **Olympic Sculpture Park**: Stroll through this outdoor park with large-scale sculptures and stunning views of the waterfront and mountains.\n", "\n", "11. **Underground Tour**: Discover Seattle's history on this quirky tour of the city's underground passageways in Pioneer Square.\n", "\n", "12. **Seattle Waterfront**: Enjoy the shops, restaurants, and attractions along the waterfront, including the Seattle Great Wheel and the aquarium.\n", "\n", "13. **Discovery Park**: Explore the largest green space in Seattle, featuring trails, beaches, and views of Puget Sound.\n", "\n", "14. **Food Tours**: Try out Seattle’s diverse culinary scene, including fresh seafood, international cuisines, and coffee culture (don’t miss the original Starbucks!).\n", "\n", "15. **Attend a Sports Game**: Catch a Seahawks (NFL), Mariners (MLB), or Sounders (MLS) game for a lively local experience.\n", "\n", "Whether you're interested in culture, nature, food, or history, Seattle has something for everyone to enjoy!\n"]}], "source": ["# Create the runtime and register the agent.\n", "from autogen_core import AgentId\n", "\n", "model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o-mini\",\n", "    # api_key=\"sk-...\", # Optional if you have an OPENAI_API_KEY set in the environment.\n", ")\n", "\n", "runtime = SingleThreadedAgentRuntime()\n", "await SimpleAgent.register(\n", "    runtime,\n", "    \"simple_agent\",\n", "    lambda: SimpleAgent(model_client=model_client),\n", ")\n", "# Start the runtime processing messages.\n", "runtime.start()\n", "# Send a message to the agent and get the response.\n", "message = Message(\"Hello, what are some fun things to do in Seattle?\")\n", "response = await runtime.send_message(message, AgentId(\"simple_agent\", \"default\"))\n", "print(response.content)\n", "# Stop the runtime processing messages.\n", "await runtime.stop()\n", "await model_client.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The above `SimpleAgent` always responds with a fresh context that contains only\n", "the system message and the latest user's message.\n", "We can use model context classes from {py:mod}`autogen_core.model_context`\n", "to make the agent \"remember\" previous conversations.\n", "See the [Model Context](./model-context.ipynb) page for more details."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API Keys From Environment Variables\n", "\n", "In the examples above, we show that you can provide the API key through the `api_key` argument. Importantly, the OpenAI and Azure OpenAI clients use the [openai package](https://github.com/openai/openai-python/blob/3f8d8205ae41c389541e125336b0ae0c5e437661/src/openai/__init__.py#L260), which will automatically read an api key from the environment variable if one is not provided.\n", "\n", "- For OpenAI, you can set the `OPENAI_API_KEY` environment variable.  \n", "- For Azure OpenAI, you can set the `AZURE_OPENAI_API_KEY` environment variable. \n", "\n", "In addition, for Gemini (Beta), you can set the `GEMINI_API_KEY` environment variable.\n", "\n", "This is a good practice to explore, as it avoids including sensitive api keys in your code. \n", "\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}