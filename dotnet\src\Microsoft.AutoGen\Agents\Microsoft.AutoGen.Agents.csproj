<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <ItemGroup>
		<ProjectReference Include="..\Contracts\Microsoft.AutoGen.Contracts.csproj" />
		<ProjectReference Include="..\Core\Microsoft.AutoGen.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
   	<PackageReference Include="Microsoft.Extensions.AI.Abstractions" />
    <PackageReference Include="Google.Protobuf" />
    <PackageReference Include="Grpc.Tools" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
      <Protobuf Include="protos\agent_events.proto" GrpcServices="Client;Server" Link="Protos\agent_events.proto" />
  </ItemGroup>
</Project>
