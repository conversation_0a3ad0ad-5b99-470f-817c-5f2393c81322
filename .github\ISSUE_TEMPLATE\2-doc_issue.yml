name: 📘 Doc Issue
description: Report an issue in the documentation, including missing or incorrect information.
type: "bug"
labels:
  - needs-triage
  - documentation

body:
  - type: markdown
    attributes:
      value: |
        ## Please Read the following before submitting an issue.

        ### Have you read the docs?
        - [Python AgentChat User Guide and Tutorial](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/index.html)
        - [Python Core API User Guide](https://microsoft.github.io/autogen/stable/user-guide/core-user-guide/index.html)
        - [Python API Doc](https://microsoft.github.io/autogen/stable/reference/index.html)
        - [.NET Doc](https://microsoft.github.io/autogen/dotnet/)

        ### Have you searched for related issues?
        - Some other users might have the same issue as yours.

  - type: textarea
    attributes:
      label: What is the doc issue?
      description: Please provide as much information as possible, this helps us address the issue. Use Markdown to format your text.
      value: |
        **Describe the issue**
        A clear and concise description of what the issue is. What is missing or incorrect?

        **What do you want to see in the doc?**

        **Screenshots**
        If applicable, add screenshots to help explain your problem.

        **Additional context**
        Add any other context about the problem here.
    validations:
      required: true
  - type: input
    id: doc-link
    attributes:
      label: Link to the doc page, if applicable
      description: Please provide a link to the doc page that has the issue.
      placeholder: https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/index.html
    validations:
      required: true
