{"metadata": [{"src": [{"files": ["src/Microsoft.AutoGen/Core/**/*.csproj", "src/Microsoft.AutoGen/Contracts/**/*.csproj", "src/Microsoft.AutoGen/Core.Grpc/**/*.csproj", "src/Microsoft.AutoGen/AgentHost/**/*.csproj", "src/Microsoft.AutoGen/RuntimeGateway.Grpc/**/*.csproj", "src/Microsoft.AutoGen/Extensions/**/*.csproj"], "src": "../../dotnet/"}], "dest": "api", "includePrivateMembers": false, "disableGitFeatures": false, "disableDefaultFilter": false, "noRestore": false, "namespaceLayout": "flattened", "memberLayout": "samePage", "allowCompilationErrors": false}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["core/**.md", "core/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "output": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "modern", "template"], "globalMetadata": {"_appTitle": "AutoGen .NET", "_appName": "AutoGen .NET", "_appLogoPath": "images/logo.svg", "_appFooter": "AutoGen", "_appFaviconPath": "images/favicon.ico", "_gitContribute": {"repo": "https://github.com/microsoft/autogen.git"}}, "postProcessors": ["ExtractSearchIndex"], "_enableSearch": true, "keepFileLink": false, "disableGitFeatures": false}}