<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <TargetFrameworks>$(TestTargetFrameworks)</TargetFrameworks>
      <ImplicitUsings>enable</ImplicitUsings>
      <IsPackable>false</IsPackable>
      <IsTestProject>True</IsTestProject>
      <NoWarn>$(NoWarn);CA1829;CA1826</NoWarn>
      <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\AutoGen.Ollama\AutoGen.Ollama.csproj" />
      <ProjectReference Include="..\AutoGen.Tests\AutoGen.Tests.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="images\image.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="images\square.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
