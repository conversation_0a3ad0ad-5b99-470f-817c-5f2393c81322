<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.Testing" />
    <PackageReference Include="coverlet.collector" >
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="GitHubActionsTestLogger">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="MartinCostello.Logging.XUnit" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.Testing" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="System.Net" />
    <Using Include="Microsoft.Extensions.DependencyInjection" />
    <Using Include="Aspire.Hosting.ApplicationModel" />
    <Using Include="Aspire.Hosting.Testing" />
    <Using Include="Xunit" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
    <PackageReference Include="Aspire.Hosting" />
    <PackageReference Include="Aspire.Hosting.Python" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../src/Microsoft.AutoGen/AgentHost/Microsoft.AutoGen.AgentHost.csproj" />
    <ProjectReference Include="../Microsoft.AutoGen.Integration.Tests.AppHosts/HelloAgentTests/HelloAgentTests.csproj" />
    <ProjectReference Include="../Microsoft.AutoGen.Integration.Tests.AppHosts/InMemoryTests.AppHost/InMemoryTests.AppHost.csproj" />
  </ItemGroup>

  <!-- Properties, Items, and targets to ensure Python apps are initialized -->
  <PropertyGroup>
    <PythonVirtualEnvironmentName>.venv</PythonVirtualEnvironmentName>
    <PythonRoot>$(RepoRoot)..\python</PythonRoot>

  </PropertyGroup>

  <Target Name="CreatePythonVirtualEnvironments" AfterTargets="PrepareForBuild">
    <PropertyGroup>
      <VenvDir>$(PythonVenvRoot)\$(PythonVirtualEnvironmentName)\</VenvDir>
      <CreateVenv Condition="!Exists('$(VenvDir)')">True</CreateVenv>
      <Uv>~/.local/bin/uv</Uv>
      <CheckUvPath Condition="!Exists('$(Uv)')">True</CheckUvPath>
      <UvPath Condition="'$(CheckUvPath)' == 'True'">uv</UvPath>
      <UvPath Condition="'$(CheckUvPath)' == 'False'">$(Uv)</UvPath>
    </PropertyGroup>
    <Message Importance="Normal" Text="Initializing virtual environment for $(PythonVenvRoot)" />

    <Exec Command="$(UvPath) sync --all-extras --no-install-package llama-cpp-python" WorkingDirectory="$(PythonRoot)" Condition=" '$(CreateVenv)' == 'True' " />
      <!--Output TaskParameter="ConsoleOutput" PropertyName="OutputOfExec" />
    </Exec>
    <Message Importance="Normal" Text="$(OutputOfExec)" />-->
  </Target>


</Project>
