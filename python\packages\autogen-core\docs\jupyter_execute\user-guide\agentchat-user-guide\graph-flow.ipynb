{"cells": [{"cell_type": "markdown", "id": "b3b5a600", "metadata": {}, "source": ["# GraphFlow (Workflows)\n", "\n", "In this section you'll learn how to create an _multi-agent workflow_ using {py:class}`~autogen_agentchat.teams.GraphFlow`, or simply \"flow\" for short.\n", "It uses structured execution and precisely controls how agents interact to accomplish a task.\n", "\n", "We'll first show you how to create and run a flow. We'll then explain how to observe and debug flow behavior, \n", "and discuss important operations for managing execution.\n", "\n", "AutoGen AgentChat provides a team for directed graph execution:\n", "\n", "- {py:class}`~autogen_agentchat.teams.GraphFlow`: A team that follows a {py:class}`~autogen_agentchat.teams.DiGraph`\n", "to control the execution flow between agents. \n", "Supports sequential, parallel, conditional, and looping behaviors.\n", "\n", "```{note}\n", "**When should you use {py:class}`~autogen_agentchat.teams.GraphFlow`?**\n", "\n", "Use Graph when you need strict control over the order in which agents act, or when different outcomes must lead to different next steps.\n", "Start with a simple team such as {py:class}`~autogen_agentchat.teams.RoundRobinGroupChat` or {py:class}`~autogen_agentchat.teams.SelectorGroupChat`\n", "if ad-hoc conversation flow is sufficient. \n", "Transition to a structured workflow when your task requires deterministic control,\n", "conditional branching, or handling complex multi-step processes with cycles.\n", "```\n", "\n", "> **Warning:** {py:class}`~autogen_agentchat.teams.GraphFlow` is an **experimental feature**. \n", "Its API, behavior, and capabilities are **subject to change** in future releases."]}, {"cell_type": "markdown", "id": "f04e4271", "metadata": {}, "source": ["## Creating and Running a Flow\n", "\n", "{py:class}`~autogen_agentchat.teams.DiGraphBuilder` is a fluent utility that lets you easily construct execution graphs for workflows. It supports building:\n", "\n", "- Sequential chains\n", "- <PERSON><PERSON><PERSON> fan-outs\n", "- Conditional branching\n", "- Loops with safe exit conditions\n", "\n", "Each node in the graph represents an agent, and edges define the allowed execution paths. Edges can optionally have conditions based on agent messages."]}, {"cell_type": "markdown", "id": "760ee783", "metadata": {}, "source": ["### Sequential Flow\n", "\n", "We will begin by creating a simple workflow where a **writer** drafts a paragraph and a **reviewer** provides feedback. This graph terminates after the reviewer comments on the writer. \n", "\n", "Note, the flow automatically computes all the source and leaf nodes of the graph and the execution starts at all the source nodes in the graph and completes execution when no nodes are left to execute."]}, {"cell_type": "code", "execution_count": 6, "id": "c6dd0386", "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.teams import DiGraphBuilder, GraphFlow\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "# Create an OpenAI model client\n", "client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\")\n", "\n", "# Create the writer agent\n", "writer = AssistantAgent(\"writer\", model_client=client, system_message=\"Draft a short paragraph on climate change.\")\n", "\n", "# Create the reviewer agent\n", "reviewer = AssistantAgent(\"reviewer\", model_client=client, system_message=\"Review the draft and suggest improvements.\")\n", "\n", "# Build the graph\n", "builder = DiGraphBuilder()\n", "builder.add_node(writer).add_node(reviewer)\n", "builder.add_edge(writer, reviewer)\n", "\n", "# Build and validate the graph\n", "graph = builder.build()\n", "\n", "# Create the flow\n", "flow = GraphFlow([writer, reviewer], graph=graph)"]}, {"cell_type": "code", "execution_count": null, "id": "1b19f9a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["source='user' models_usage=None metadata={} content='Write a short paragraph about climate change.' type='TextMessage'\n", "source='writer' models_usage=RequestUsage(prompt_tokens=28, completion_tokens=95) metadata={} content='Climate change refers to long-term shifts in temperature, precipitation, and other atmospheric patterns, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These changes contribute to rising global temperatures, melting ice caps, more frequent and severe weather events, and adverse impacts on ecosystems and human communities. Addressing climate change requires global cooperation to reduce greenhouse gas emissions, transition to renewable energy sources, and implement sustainable practices to protect the planet for future generations.' type='TextMessage'\n", "source='reviewer' models_usage=RequestUsage(prompt_tokens=127, completion_tokens=144) metadata={} content=\"The paragraph provides a clear overview of climate change, its causes, and its impacts. To enhance clarity and engagement, consider adding specific examples or emphasizing the urgency of action. Here's a revised version:\\n\\nClimate change is a long-term alteration of Earth's climate patterns caused primarily by human activities such as burning fossil fuels, deforestation, and industrial emissions. These actions increase greenhouse gases in the atmosphere, leading to rising global temperatures, melting ice caps, and more frequent extreme weather events like hurricanes and droughts. The effects threaten ecosystems, disrupt agriculture, and endanger communities worldwide. Addressing this crisis requires urgent, coordinated global efforts to reduce emissions, adopt renewable energy, and promote sustainable practices to safeguard the planet for future generations.\" type='TextMessage'\n", "source='DiGraphStopAgent' models_usage=None metadata={} content='Digraph execution is complete' type='StopMessage'\n", "messages=[TextMessage(source='user', models_usage=None, metadata={}, content='Write a short paragraph about climate change.', type='TextMessage'), TextMessage(source='writer', models_usage=RequestUsage(prompt_tokens=28, completion_tokens=95), metadata={}, content='Climate change refers to long-term shifts in temperature, precipitation, and other atmospheric patterns, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These changes contribute to rising global temperatures, melting ice caps, more frequent and severe weather events, and adverse impacts on ecosystems and human communities. Addressing climate change requires global cooperation to reduce greenhouse gas emissions, transition to renewable energy sources, and implement sustainable practices to protect the planet for future generations.', type='TextMessage'), TextMessage(source='reviewer', models_usage=RequestUsage(prompt_tokens=127, completion_tokens=144), metadata={}, content=\"The paragraph provides a clear overview of climate change, its causes, and its impacts. To enhance clarity and engagement, consider adding specific examples or emphasizing the urgency of action. Here's a revised version:\\n\\nClimate change is a long-term alteration of Earth's climate patterns caused primarily by human activities such as burning fossil fuels, deforestation, and industrial emissions. These actions increase greenhouse gases in the atmosphere, leading to rising global temperatures, melting ice caps, and more frequent extreme weather events like hurricanes and droughts. The effects threaten ecosystems, disrupt agriculture, and endanger communities worldwide. Addressing this crisis requires urgent, coordinated global efforts to reduce emissions, adopt renewable energy, and promote sustainable practices to safeguard the planet for future generations.\", type='TextMessage'), StopMessage(source='DiGraphStopAgent', models_usage=None, metadata={}, content='Digraph execution is complete', type='StopMessage')] stop_reason='Stop message received'\n"]}], "source": ["# Use `asyncio.run(...)` and wrap the below in a async function when running in a script.\n", "stream = flow.run_stream(task=\"Write a short paragraph about climate change.\")\n", "async for event in stream:  # type: ignore\n", "    print(event)\n", "# Use Console(flow.run_stream(...)) for better formatting in console."]}, {"cell_type": "markdown", "id": "5157cfbf", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> Flow with Join\n", "\n", "We now create a slightly more complex flow:\n", "\n", "- A **writer** drafts a paragraph.\n", "- Two **editors** independently edit for grammar and style (parallel fan-out).\n", "- A **final reviewer** consolidates their edits (join).\n", "\n", "Execution starts at the **writer**, fans out to **editor1** and **editor2** simultaneously, and then both feed into the **final reviewer**.\n"]}, {"cell_type": "code", "execution_count": null, "id": "a54d2454", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- TextMessage (user) ----------\n", "Write a short paragraph about climate change.\n", "---------- <PERSON><PERSON><PERSON><PERSON> (writer) ----------\n", "Climate change refers to long-term shifts in weather patterns and global temperatures, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase concentrations of greenhouse gases like carbon dioxide and methane in the atmosphere, leading to global warming. The impacts of climate change include more frequent and severe weather events, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires international cooperation, significant shifts toward renewable energy sources, and sustainable practices to reduce our carbon footprint and protect the planet for future generations.\n", "---------- Text<PERSON><PERSON><PERSON> (editor1) ----------\n", "Climate change refers to long-term shifts in weather patterns and global temperatures, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase concentrations of greenhouse gases like carbon dioxide and methane in the atmosphere, leading to global warming. The impacts of climate change include more frequent and severe weather events, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires international cooperation, significant shifts toward renewable energy sources, and sustainable practices to reduce our carbon footprint and protect the planet for future generations.\n", "---------- Text<PERSON><PERSON><PERSON> (editor2) ----------\n", "Climate change involves long-term alterations in weather patterns and global temperatures, primarily caused by human activities like burning fossil fuels, deforestation, and industrial processes. These actions elevate levels of greenhouse gases such as carbon dioxide and methane, resulting in global warming. Its consequences are widespread, including more frequent and intense storms, rising sea levels, melting glaciers, and disturbances to ecosystems and agriculture. Combating this crisis demands international collaboration, a swift transition to renewable energy, and sustainable practices to cut carbon emissions, ensuring a healthier planet for future generations.\n", "---------- TextMessage (final_reviewer) ----------\n", "Climate change involves long-term alterations in weather patterns and global temperatures, primarily caused by human activities such as burning fossil fuels, deforestation, and industrial processes. These actions increase levels of greenhouse gases like carbon dioxide and methane, leading to global warming. Its consequences include more frequent and intense storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this crisis requires international collaboration, a swift transition to renewable energy, and sustainable practices to reduce carbon emissions, ensuring a healthier planet for future generations.\n", "---------- StopMessage (DiGraphStopAgent) ----------\n", "Digraph execution is complete\n"]}, {"data": {"text/plain": ["TaskResult(messages=[TextMessage(source='user', models_usage=None, metadata={}, content='Write a short paragraph about climate change.', type='TextMessage'), TextMessage(source='writer', models_usage=RequestUsage(prompt_tokens=28, completion_tokens=113), metadata={}, content='Climate change refers to long-term shifts in weather patterns and global temperatures, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase concentrations of greenhouse gases like carbon dioxide and methane in the atmosphere, leading to global warming. The impacts of climate change include more frequent and severe weather events, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires international cooperation, significant shifts toward renewable energy sources, and sustainable practices to reduce our carbon footprint and protect the planet for future generations.', type='TextMessage'), TextMessage(source='editor1', models_usage=RequestUsage(prompt_tokens=144, completion_tokens=113), metadata={}, content='Climate change refers to long-term shifts in weather patterns and global temperatures, largely driven by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase concentrations of greenhouse gases like carbon dioxide and methane in the atmosphere, leading to global warming. The impacts of climate change include more frequent and severe weather events, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires international cooperation, significant shifts toward renewable energy sources, and sustainable practices to reduce our carbon footprint and protect the planet for future generations.', type='TextMessage'), TextMessage(source='editor2', models_usage=RequestUsage(prompt_tokens=263, completion_tokens=107), metadata={}, content='Climate change involves long-term alterations in weather patterns and global temperatures, primarily caused by human activities like burning fossil fuels, deforestation, and industrial processes. These actions elevate levels of greenhouse gases such as carbon dioxide and methane, resulting in global warming. Its consequences are widespread, including more frequent and intense storms, rising sea levels, melting glaciers, and disturbances to ecosystems and agriculture. Combating this crisis demands international collaboration, a swift transition to renewable energy, and sustainable practices to cut carbon emissions, ensuring a healthier planet for future generations.', type='TextMessage'), TextMessage(source='final_reviewer', models_usage=RequestUsage(prompt_tokens=383, completion_tokens=104), metadata={}, content='Climate change involves long-term alterations in weather patterns and global temperatures, primarily caused by human activities such as burning fossil fuels, deforestation, and industrial processes. These actions increase levels of greenhouse gases like carbon dioxide and methane, leading to global warming. Its consequences include more frequent and intense storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this crisis requires international collaboration, a swift transition to renewable energy, and sustainable practices to reduce carbon emissions, ensuring a healthier planet for future generations.', type='TextMessage'), StopMessage(source='DiGraphStopAgent', models_usage=None, metadata={}, content='Digraph execution is complete', type='StopMessage')], stop_reason='Stop message received')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.teams import DiGraphBuilder, GraphFlow\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "# Create an OpenAI model client\n", "client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\")\n", "\n", "# Create the writer agent\n", "writer = AssistantAgent(\"writer\", model_client=client, system_message=\"Draft a short paragraph on climate change.\")\n", "\n", "# Create two editor agents\n", "editor1 = AssistantAgent(\"editor1\", model_client=client, system_message=\"Edit the paragraph for grammar.\")\n", "\n", "editor2 = AssistantAgent(\"editor2\", model_client=client, system_message=\"Edit the paragraph for style.\")\n", "\n", "# Create the final reviewer agent\n", "final_reviewer = AssistantAgent(\n", "    \"final_reviewer\",\n", "    model_client=client,\n", "    system_message=\"Consolidate the grammar and style edits into a final version.\",\n", ")\n", "\n", "# Build the workflow graph\n", "builder = DiGraphBuilder()\n", "builder.add_node(writer).add_node(editor1).add_node(editor2).add_node(final_reviewer)\n", "\n", "# Fan-out from writer to editor1 and editor2\n", "builder.add_edge(writer, editor1)\n", "builder.add_edge(writer, editor2)\n", "\n", "# Fan-in both editors into final reviewer\n", "builder.add_edge(editor1, final_reviewer)\n", "builder.add_edge(editor2, final_reviewer)\n", "\n", "# Build and validate the graph\n", "graph = builder.build()\n", "\n", "# Create the flow\n", "flow = GraphFlow(\n", "    participants=builder.get_participants(),\n", "    graph=graph,\n", ")\n", "\n", "# Run the workflow\n", "await Console(flow.run_stream(task=\"Write a short paragraph about climate change.\"))"]}, {"cell_type": "markdown", "id": "0343182c", "metadata": {}, "source": ["## Message Filtering\n", "\n", "### Execution Graph vs. Message Graph\n", "\n", "In {py:class}`~autogen_agentchat.teams.GraphFlow`, the **execution graph** is defined using \n", "{py:class}`~autogen_agentchat.teams.DiGraph`, which controls the order in which agents execute.\n", "However, the execution graph does not control what messages an agent receives from other agents.\n", "By default, all messages are sent to all agents in the graph.\n", "\n", "**Message filtering** is a separate feature that allows you to filter the messages\n", "received by each agent and limiting their model context to only the relevant information.\n", "The set of message filters defines the **message graph** in the flow.\n", "\n", "Specifying the message graph can help with:\n", "- Reduce hallucinations\n", "- Control memory load\n", "- Focus agents only on relevant information\n", "\n", "You can use {py:class}`~autogen_agentchat.agents.MessageFilterAgent` together with {py:class}`~autogen_agentchat.agents.MessageFilterConfig` and {py:class}`~autogen_agentchat.agents.PerSourceFilter` to define these rules."]}, {"cell_type": "code", "execution_count": null, "id": "2a59af03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- TextMessage (user) ----------\n", "Summarize key facts about climate change.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["---------- <PERSON><PERSON><PERSON><PERSON> (researcher) ----------\n", "Certainly! Here are some key facts about climate change:\n", "\n", "1. **Global Warming**: Earth's average surface temperature has increased significantly over the past century, primarily due to human activities.\n", "2. **Greenhouse Gas Emissions**: The main contributors are carbon dioxide (CO₂), methane (CH₄), and nitrous oxide (N₂O), resulting from burning fossil fuels, deforestation, and industrial processes.\n", "3. **Impacts on Weather and Climate**: Climate change leads to more frequent and severe heatwaves, storms, droughts, and heavy rainfall.\n", "4. **Rising Sea Levels**: Melting polar ice caps and glaciers, along with thermal expansion of seawater, are causing sea levels to rise.\n", "5. **Effects on Ecosystems**: Altered habitats threaten plant and animal species, leading to biodiversity loss.\n", "6. **Human Health and Societies**: Climate change contributes to health issues, food and water insecurity, and displacement of populations.\n", "7. **Global Response**: International efforts like the Paris Agreement aim to limit temperature rise, promote renewable energy, and reduce emissions.\n", "8. **Urgency**: Addressing climate change requires immediate, concerted actions to mitigate further damage and adapt to changes.\n", "\n", "Let me know if you want more detailed information on any of these points!\n", "---------- <PERSON><PERSON><PERSON><PERSON> (analyst) ----------\n", "Your summary effectively covers the fundamental aspects of climate change and presents them clearly. Here are some suggestions to improve clarity, depth, and engagement:\n", "\n", "1. Enhance structure with subheadings: Organize points into thematic sections (e.g., Causes, Effects, Responses) for easier navigation.\n", "2. Add recent context or data: Incorporate the latest statistics or notable recent events to emphasize urgency.\n", "3. Emphasize solutions: Briefly mention specific mitigation and adaptation strategies beyond international agreements.\n", "4. Use more precise language: For example, specify the amount of temperature increase globally (~1.2°C since pre-industrial times).\n", "5. Incorporate the importance of individual actions: Highlight how personal choices contribute to climate efforts.\n", "6. Mention climate feedback loops: <PERSON>riefly note how certain effects (like melting ice) can accelerate warming.\n", "\n", "**Improved Version:**\n", "\n", "---\n", "\n", "**Overview of Climate Change**\n", "\n", "**Causes:**\n", "- Human activities, especially burning fossil fuels, deforestation, and industrial processes, have led to increased concentrations of greenhouse gases such as carbon dioxide (CO₂), methane (CH₄), and nitrous oxide (N₂O).\n", "- Since the late 19th century, Earth's average surface temperature has risen by approximately 1.2°C, with the past decade being the warmest on record.\n", "\n", "**Impacts:**\n", "- The changing climate causes more frequent and intense heatwaves, storms, droughts, and heavy rainfall events.\n", "- Melting polar ice caps and glaciers, along with thermal expansion, are raising sea levels, threatening coastal communities.\n", "- Ecosystems are shifting, leading to habitat loss and risking biodiversity, with some species facing extinction.\n", "- Human health and societies are affected through increased heat-related illnesses, food and water insecurity, and displacement due to extreme weather events.\n", "\n", "**Global Response and Solutions:**\n", "- International agreements like the Paris Agreement aim to limit global temperature rise well below 2°C.\n", "- Strategies include transitioning to renewable energy sources, increasing energy efficiency, reforestation, and sustainable land use.\n", "- Community and individual actions—reducing carbon footprints, supporting sustainable policies, and raising awareness—are essential components.\n", "\n", "**Urgency and Call to Action:**\n", "- Immediate, coordinated efforts are critical to mitigate irreversible damage and adapt to ongoing changes.\n", "- Every sector, from government to individual, has a role to play in creating a sustainable future.\n", "\n", "---\n", "\n", "Let me know if you'd like a more detailed explanation of any section or additional statistical data!\n", "---------- <PERSON><PERSON><PERSON><PERSON> (presenter) ----------\n", "**Slide Title:**  \n", "**Climate Change: Causes, Impacts & Solutions**\n", "\n", "**Causes:**  \n", "- Emissions from burning fossil fuels, deforestation, industrial activities  \n", "- Greenhouse gases (CO₂, CH₄, N₂O) have increased significantly  \n", "- Global temperature has risen by ~1.2°C since pre-industrial times  \n", "\n", "**Impacts:**  \n", "- More frequent heatwaves, storms, droughts, and heavy rainfall  \n", "- Melting ice caps and rising sea levels threaten coastal areas  \n", "- Habitat loss and decreased biodiversity  \n", "- Health risks and societal disruptions  \n", "\n", "**Responses & Solutions:**  \n", "- International efforts like the Paris Agreement aim to limit warming  \n", "- Transitioning to renewable energy, energy efficiency, reforestation  \n", "- Community and individual actions: reducing carbon footprints and raising awareness  \n", "\n", "**Urgency:**  \n", "- Immediate, coordinated action is essential to prevent irreversible damage  \n", "- Everyone has a role in building a sustainable future\n", "---------- StopMessage (DiGraphStopAgent) ----------\n", "Digraph execution is complete\n"]}, {"data": {"text/plain": ["TaskResult(messages=[TextMessage(source='user', models_usage=None, metadata={}, content='Summarize key facts about climate change.', type='TextMessage'), TextMessage(source='researcher', models_usage=RequestUsage(prompt_tokens=30, completion_tokens=267), metadata={}, content=\"Certainly! Here are some key facts about climate change:\\n\\n1. **Global Warming**: Earth's average surface temperature has increased significantly over the past century, primarily due to human activities.\\n2. **Greenhouse Gas Emissions**: The main contributors are carbon dioxide (CO₂), methane (CH₄), and nitrous oxide (N₂O), resulting from burning fossil fuels, deforestation, and industrial processes.\\n3. **Impacts on Weather and Climate**: Climate change leads to more frequent and severe heatwaves, storms, droughts, and heavy rainfall.\\n4. **Rising Sea Levels**: Melting polar ice caps and glaciers, along with thermal expansion of seawater, are causing sea levels to rise.\\n5. **Effects on Ecosystems**: Altered habitats threaten plant and animal species, leading to biodiversity loss.\\n6. **Human Health and Societies**: Climate change contributes to health issues, food and water insecurity, and displacement of populations.\\n7. **Global Response**: International efforts like the Paris Agreement aim to limit temperature rise, promote renewable energy, and reduce emissions.\\n8. **Urgency**: Addressing climate change requires immediate, concerted actions to mitigate further damage and adapt to changes.\\n\\nLet me know if you want more detailed information on any of these points!\", type='TextMessage'), TextMessage(source='analyst', models_usage=RequestUsage(prompt_tokens=287, completion_tokens=498), metadata={}, content=\"Your summary effectively covers the fundamental aspects of climate change and presents them clearly. Here are some suggestions to improve clarity, depth, and engagement:\\n\\n1. Enhance structure with subheadings: Organize points into thematic sections (e.g., Causes, Effects, Responses) for easier navigation.\\n2. Add recent context or data: Incorporate the latest statistics or notable recent events to emphasize urgency.\\n3. Emphasize solutions: Briefly mention specific mitigation and adaptation strategies beyond international agreements.\\n4. Use more precise language: For example, specify the amount of temperature increase globally (~1.2°C since pre-industrial times).\\n5. Incorporate the importance of individual actions: Highlight how personal choices contribute to climate efforts.\\n6. Mention climate feedback loops: Briefly note how certain effects (like melting ice) can accelerate warming.\\n\\n**Improved Version:**\\n\\n---\\n\\n**Overview of Climate Change**\\n\\n**Causes:**\\n- Human activities, especially burning fossil fuels, deforestation, and industrial processes, have led to increased concentrations of greenhouse gases such as carbon dioxide (CO₂), methane (CH₄), and nitrous oxide (N₂O).\\n- Since the late 19th century, Earth's average surface temperature has risen by approximately 1.2°C, with the past decade being the warmest on record.\\n\\n**Impacts:**\\n- The changing climate causes more frequent and intense heatwaves, storms, droughts, and heavy rainfall events.\\n- Melting polar ice caps and glaciers, along with thermal expansion, are raising sea levels, threatening coastal communities.\\n- Ecosystems are shifting, leading to habitat loss and risking biodiversity, with some species facing extinction.\\n- Human health and societies are affected through increased heat-related illnesses, food and water insecurity, and displacement due to extreme weather events.\\n\\n**Global Response and Solutions:**\\n- International agreements like the Paris Agreement aim to limit global temperature rise well below 2°C.\\n- Strategies include transitioning to renewable energy sources, increasing energy efficiency, reforestation, and sustainable land use.\\n- Community and individual actions—reducing carbon footprints, supporting sustainable policies, and raising awareness—are essential components.\\n\\n**Urgency and Call to Action:**\\n- Immediate, coordinated efforts are critical to mitigate irreversible damage and adapt to ongoing changes.\\n- Every sector, from government to individual, has a role to play in creating a sustainable future.\\n\\n---\\n\\nLet me know if you'd like a more detailed explanation of any section or additional statistical data!\", type='TextMessage'), TextMessage(source='presenter', models_usage=RequestUsage(prompt_tokens=521, completion_tokens=192), metadata={}, content='**Slide Title:**  \\n**Climate Change: Causes, Impacts & Solutions**\\n\\n**Causes:**  \\n- Emissions from burning fossil fuels, deforestation, industrial activities  \\n- Greenhouse gases (CO₂, CH₄, N₂O) have increased significantly  \\n- Global temperature has risen by ~1.2°C since pre-industrial times  \\n\\n**Impacts:**  \\n- More frequent heatwaves, storms, droughts, and heavy rainfall  \\n- Melting ice caps and rising sea levels threaten coastal areas  \\n- Habitat loss and decreased biodiversity  \\n- Health risks and societal disruptions  \\n\\n**Responses & Solutions:**  \\n- International efforts like the Paris Agreement aim to limit warming  \\n- Transitioning to renewable energy, energy efficiency, reforestation  \\n- Community and individual actions: reducing carbon footprints and raising awareness  \\n\\n**Urgency:**  \\n- Immediate, coordinated action is essential to prevent irreversible damage  \\n- Everyone has a role in building a sustainable future', type='TextMessage'), StopMessage(source='DiGraphStopAgent', models_usage=None, metadata={}, content='Digraph execution is complete', type='StopMessage')], stop_reason='Stop message received')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from autogen_agentchat.agents import AssistantAgent, MessageFilterAgent, MessageFilterConfig, PerSourceFilter\n", "from autogen_agentchat.teams import DiGraphBuilder, GraphFlow\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "# Model client\n", "client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\")\n", "\n", "# Create agents\n", "researcher = AssistantAgent(\n", "    \"researcher\", model_client=client, system_message=\"Summarize key facts about climate change.\"\n", ")\n", "analyst = AssistantAgent(\"analyst\", model_client=client, system_message=\"Review the summary and suggest improvements.\")\n", "presenter = Assistant<PERSON><PERSON>(\n", "    \"presenter\", model_client=client, system_message=\"Prepare a presentation slide based on the final summary.\"\n", ")\n", "\n", "# Apply message filtering\n", "filtered_analyst = MessageFilterAgent(\n", "    name=\"analyst\",\n", "    wrapped_agent=analyst,\n", "    filter=MessageFilterConfig(per_source=[PerSourceFilter(source=\"researcher\", position=\"last\", count=1)]),\n", ")\n", "\n", "filtered_presenter = MessageFilterAgent(\n", "    name=\"presenter\",\n", "    wrapped_agent=presenter,\n", "    filter=MessageFilterConfig(per_source=[PerSourceFilter(source=\"analyst\", position=\"last\", count=1)]),\n", ")\n", "\n", "# Build the flow\n", "builder = DiGraphBuilder()\n", "builder.add_node(researcher).add_node(filtered_analyst).add_node(filtered_presenter)\n", "builder.add_edge(researcher, filtered_analyst).add_edge(filtered_analyst, filtered_presenter)\n", "\n", "# Create the flow\n", "flow = GraphFlow(\n", "    participants=builder.get_participants(),\n", "    graph=builder.build(),\n", ")\n", "\n", "# Run the flow\n", "await Console(flow.run_stream(task=\"Summarize key facts about climate change.\"))"]}, {"cell_type": "markdown", "id": "f7309529", "metadata": {}, "source": ["## 🔁 Advanced Example: Conditional Loop + Filtered Summary\n", "\n", "This example demonstrates:\n", "\n", "- A loop between generator and reviewer (which exits when reviewer says \"APPROVE\")\n", "- A summarizer agent that only sees the first user input and the last reviewer message\n"]}, {"cell_type": "code", "execution_count": 1, "id": "af297db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- TextMessage (user) ----------\n", "Brainstorm ways to reduce plastic waste.\n", "---------- TextMessage (generator) ----------\n", "1. **Refill Stations**: Set up refill stations for common household products such as laundry detergent, shampoo, and cleaning supplies in grocery stores and public spaces.\n", "\n", "2. **Bulk Buying Incentives**: Create incentives for consumers to buy in bulk, reducing the amount of packaging waste.\n", "\n", "3. **Eco-friendly Packaging**: Encourage companies to develop biodegradable or compostable packaging for their products.\n", "\n", "4. **Upcycling Workshops**: Organize community workshops that teach people how to repurpose plastic items into practical or decorative objects.\n", "\n", "5. **Community Clean-Up Events**: Host regular community clean-up events focused on collecting plastic waste from parks, beaches, and roadsides.\n", "\n", "6. **Education Campaigns**: Launch educational campaigns in schools and communities about the impact of plastic waste and sustainable alternatives.\n", "\n", "7. **Plastic-Free Challenges**: Initiate a monthly plastic-free challenge encouraging individuals and families to reduce their plastic consumption.\n", "\n", "8. **Support Local Artisan Products**: Promote local artisans who create products from recycled materials, fostering a circular economy.\n", "\n", "9. **Rewards Programs**: Develop a rewards program for individuals and businesses who actively reduce their plastic usage, such as discounts or points for using reusable bags and containers.\n", "\n", "10. **Zero-Waste Grocery Stores**: Support the establishment of zero-waste grocery stores where customers can bring their own containers and buy loose, unpackaged products.\n", "\n", "11. **Legislation and Policy Changes**: Advocate for policies that ban single-use plastics and promote alternatives in both public and private sectors.\n", "\n", "12. **Environmentally Friendly Innovations**: Invest in and promote innovations in materials science that create alternatives to plastic from natural materials.\n", "\n", "13. **Plastic Bank Initiatives**: Start or support initiatives that allow people to exchange plastic waste for products or services, incentivizing recycling.\n", "\n", "14. **DIY Tutorials**: Create and share DIY tutorials for making your own household products to reduce reliance on plastic-packaged goods.\n", "\n", "15. **Corporate Sustainability Partnerships**: Encourage collaboration between businesses to share best practices in reducing plastic use and waste.\n", "\n", "16. **Public Art Installations**: Organize public art projects using collected plastic waste to raise awareness and promote discussions on plastic pollution.\n", "\n", "17. **Sustainable Fashion Initiatives**: Promote clothing and accessories made from recycled plastics, highlighting those companies that commit to eco-friendly practices.\n", "\n", "18. **Digital Outreach**: Use social media and digital platforms to raise awareness about the impact of plastic waste and promote sustainable lifestyle choices.\n", "\n", "19. **Composting Services**: Establish services that help compost food waste and other biodegradable materials, reducing reliance on plastic bags for disposal.\n", "\n", "20. **Sustainable Event Planning**: Collaborate with event planners to create guidelines for low-plastic events, encouraging the use of sustainable materials and practices.\n", "---------- <PERSON><PERSON><PERSON><PERSON> (reviewer) ----------\n", "These ideas provide a comprehensive approach to reducing plastic waste, addressing various aspects such as consumer behavior, community involvement, and corporate responsibility. Here are some feedbacks and suggestions for improvement:\n", "\n", "1. **Refill Stations**: Provide clear labeling and education about the environmental benefits of using refill stations to encourage more participation.\n", "\n", "2. **Bulk Buying Incentives**: Consider incorporating a loyalty program that rewards frequent bulk buyers with additional discounts or promotions.\n", "\n", "3. **Eco-friendly Packaging**: Collaborate with universities and research institutions to foster innovation in eco-friendly packaging designs.\n", "\n", "4. **Upcycling Workshops**: Expand on this idea by partnering with local artists who can lead sessions, making it more appealing and engaging for the community.\n", "\n", "5. **Community Clean-Up Events**: Provide incentive programs for participants, such as raffle entries or recognition awards, to boost engagement.\n", "\n", "6. **Education Campaigns**: Tailor campaigns for different age groups, using age-appropriate messaging and methods to enhance understanding and retention.\n", "\n", "7. **Plastic-Free Challenges**: Involve local businesses by offering discounts or promotions to participants, creating a community effort with tangible rewards.\n", "\n", "8. **Support Local Artisan Products**: Connect artisans with online marketplaces to broaden their reach and sales, promoting a circular economy.\n", "\n", "9. **Rewards Programs**: Build a mobile app dedicated to tracking individuals' sustainable habits, providing insights and rewards for achievements.\n", "\n", "10. **Zero-Waste Grocery Stores**: Consider including workshops in these stores to educate customers on zero-waste practices and encourage community engagement.\n", "\n", "11. **Legislation and Policy Changes**: Engage with policymakers and present real data on plastic waste reduction impacts to advocate for meaningful changes.\n", "\n", "12. **Environmentally Friendly Innovations**: Host hackathons or competitions to stimulate creativity and collaboration in developing sustainable alternatives.\n", "\n", "13. **Plastic Bank Initiatives**: Collaborate with local businesses to create a network where people can exchange more types of waste for various community services.\n", "\n", "14. **DIY Tutorials**: Offer a platform where community members can share their own tutorials and experiences, fostering a supportive network for sustainable living.\n", "\n", "15. **Corporate Sustainability Partnerships**: Encourage companies to share their sustainability stories, motivating others while boosting their brand image.\n", "\n", "16. **Public Art Installations**: Use social media campaigns to further engage the public in these projects, encouraging them to participate and share their own creations.\n", "\n", "17. **Sustainable Fashion Initiatives**: Create pop-up shops or fashion shows featuring sustainable lines, helping to raise awareness in a fun and engaging way.\n", "\n", "18. **Digital Outreach**: Utilize influencers or local celebrities to amplify your message and reach wider audiences, particularly younger populations.\n", "\n", "19. **Composting Services**: Develop an educational program on composting benefits to accompany the service, helping customers understand its environmental importance.\n", "\n", "20. **Sustainable Event Planning**: Include a checklist for event planners detailing low-plastic options and provide resources to make the transition easier.\n", "\n", "Overall, these ideas are solid and can significantly contribute to reducing plastic waste, but implementing them effectively will require collaboration, education, and community involvement. Great job! APPROVE.\n", "---------- TextMessage (summary) ----------\n", "The user requested brainstorming ideas to reduce plastic waste. The final feedback provided by the reviewer highlighted that the ideas were comprehensive, covering various facets such as consumer behavior, community involvement, and corporate responsibility. Specific suggestions for improvement included enhancing refill stations with clear labeling, incentivizing bulk buying through loyalty programs, collaborating on eco-friendly packaging, expanding upcycling workshops with local artists, and engaging in educational campaigns tailored to different age groups. The reviewer approved the ideas overall, noting the importance of collaboration, education, and community involvement in effective implementation.\n", "---------- StopMessage (DiGraphStopAgent) ----------\n", "Digraph execution is complete\n"]}, {"data": {"text/plain": ["TaskResult(messages=[TextMessage(source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 6, 5, 4, 16, 39, 836409, tzinfo=datetime.timezone.utc), content='Brainstorm ways to reduce plastic waste.', type='TextMessage'), TextMessage(source='generator', models_usage=RequestUsage(prompt_tokens=27, completion_tokens=555), metadata={}, created_at=datetime.datetime(2025, 6, 5, 4, 16, 48, 788854, tzinfo=datetime.timezone.utc), content='1. **Refill Stations**: Set up refill stations for common household products such as laundry detergent, shampoo, and cleaning supplies in grocery stores and public spaces.\\n\\n2. **Bulk Buying Incentives**: Create incentives for consumers to buy in bulk, reducing the amount of packaging waste.\\n\\n3. **Eco-friendly Packaging**: Encourage companies to develop biodegradable or compostable packaging for their products.\\n\\n4. **Upcycling Workshops**: Organize community workshops that teach people how to repurpose plastic items into practical or decorative objects.\\n\\n5. **Community Clean-Up Events**: Host regular community clean-up events focused on collecting plastic waste from parks, beaches, and roadsides.\\n\\n6. **Education Campaigns**: Launch educational campaigns in schools and communities about the impact of plastic waste and sustainable alternatives.\\n\\n7. **Plastic-Free Challenges**: Initiate a monthly plastic-free challenge encouraging individuals and families to reduce their plastic consumption.\\n\\n8. **Support Local Artisan Products**: Promote local artisans who create products from recycled materials, fostering a circular economy.\\n\\n9. **Rewards Programs**: Develop a rewards program for individuals and businesses who actively reduce their plastic usage, such as discounts or points for using reusable bags and containers.\\n\\n10. **Zero-Waste Grocery Stores**: Support the establishment of zero-waste grocery stores where customers can bring their own containers and buy loose, unpackaged products.\\n\\n11. **Legislation and Policy Changes**: Advocate for policies that ban single-use plastics and promote alternatives in both public and private sectors.\\n\\n12. **Environmentally Friendly Innovations**: Invest in and promote innovations in materials science that create alternatives to plastic from natural materials.\\n\\n13. **Plastic Bank Initiatives**: Start or support initiatives that allow people to exchange plastic waste for products or services, incentivizing recycling.\\n\\n14. **DIY Tutorials**: Create and share DIY tutorials for making your own household products to reduce reliance on plastic-packaged goods.\\n\\n15. **Corporate Sustainability Partnerships**: Encourage collaboration between businesses to share best practices in reducing plastic use and waste.\\n\\n16. **Public Art Installations**: Organize public art projects using collected plastic waste to raise awareness and promote discussions on plastic pollution.\\n\\n17. **Sustainable Fashion Initiatives**: Promote clothing and accessories made from recycled plastics, highlighting those companies that commit to eco-friendly practices.\\n\\n18. **Digital Outreach**: Use social media and digital platforms to raise awareness about the impact of plastic waste and promote sustainable lifestyle choices.\\n\\n19. **Composting Services**: Establish services that help compost food waste and other biodegradable materials, reducing reliance on plastic bags for disposal.\\n\\n20. **Sustainable Event Planning**: Collaborate with event planners to create guidelines for low-plastic events, encouraging the use of sustainable materials and practices.', type='TextMessage'), TextMessage(source='reviewer', models_usage=RequestUsage(prompt_tokens=599, completion_tokens=625), metadata={}, created_at=datetime.datetime(2025, 6, 5, 4, 16, 58, 552656, tzinfo=datetime.timezone.utc), content=\"These ideas provide a comprehensive approach to reducing plastic waste, addressing various aspects such as consumer behavior, community involvement, and corporate responsibility. Here are some feedbacks and suggestions for improvement:\\n\\n1. **Refill Stations**: Provide clear labeling and education about the environmental benefits of using refill stations to encourage more participation.\\n\\n2. **Bulk Buying Incentives**: Consider incorporating a loyalty program that rewards frequent bulk buyers with additional discounts or promotions.\\n\\n3. **Eco-friendly Packaging**: Collaborate with universities and research institutions to foster innovation in eco-friendly packaging designs.\\n\\n4. **Upcycling Workshops**: Expand on this idea by partnering with local artists who can lead sessions, making it more appealing and engaging for the community.\\n\\n5. **Community Clean-Up Events**: Provide incentive programs for participants, such as raffle entries or recognition awards, to boost engagement.\\n\\n6. **Education Campaigns**: Tailor campaigns for different age groups, using age-appropriate messaging and methods to enhance understanding and retention.\\n\\n7. **Plastic-Free Challenges**: Involve local businesses by offering discounts or promotions to participants, creating a community effort with tangible rewards.\\n\\n8. **Support Local Artisan Products**: Connect artisans with online marketplaces to broaden their reach and sales, promoting a circular economy.\\n\\n9. **Rewards Programs**: Build a mobile app dedicated to tracking individuals' sustainable habits, providing insights and rewards for achievements.\\n\\n10. **Zero-Waste Grocery Stores**: Consider including workshops in these stores to educate customers on zero-waste practices and encourage community engagement.\\n\\n11. **Legislation and Policy Changes**: Engage with policymakers and present real data on plastic waste reduction impacts to advocate for meaningful changes.\\n\\n12. **Environmentally Friendly Innovations**: Host hackathons or competitions to stimulate creativity and collaboration in developing sustainable alternatives.\\n\\n13. **Plastic Bank Initiatives**: Collaborate with local businesses to create a network where people can exchange more types of waste for various community services.\\n\\n14. **DIY Tutorials**: Offer a platform where community members can share their own tutorials and experiences, fostering a supportive network for sustainable living.\\n\\n15. **Corporate Sustainability Partnerships**: Encourage companies to share their sustainability stories, motivating others while boosting their brand image.\\n\\n16. **Public Art Installations**: Use social media campaigns to further engage the public in these projects, encouraging them to participate and share their own creations.\\n\\n17. **Sustainable Fashion Initiatives**: Create pop-up shops or fashion shows featuring sustainable lines, helping to raise awareness in a fun and engaging way.\\n\\n18. **Digital Outreach**: Utilize influencers or local celebrities to amplify your message and reach wider audiences, particularly younger populations.\\n\\n19. **Composting Services**: Develop an educational program on composting benefits to accompany the service, helping customers understand its environmental importance.\\n\\n20. **Sustainable Event Planning**: Include a checklist for event planners detailing low-plastic options and provide resources to make the transition easier.\\n\\nOverall, these ideas are solid and can significantly contribute to reducing plastic waste, but implementing them effectively will require collaboration, education, and community involvement. Great job! APPROVE.\", type='TextMessage'), TextMessage(source='summary', models_usage=RequestUsage(prompt_tokens=663, completion_tokens=106), metadata={}, created_at=datetime.datetime(2025, 6, 5, 4, 17, 0, 865057, tzinfo=datetime.timezone.utc), content='The user requested brainstorming ideas to reduce plastic waste. The final feedback provided by the reviewer highlighted that the ideas were comprehensive, covering various facets such as consumer behavior, community involvement, and corporate responsibility. Specific suggestions for improvement included enhancing refill stations with clear labeling, incentivizing bulk buying through loyalty programs, collaborating on eco-friendly packaging, expanding upcycling workshops with local artists, and engaging in educational campaigns tailored to different age groups. The reviewer approved the ideas overall, noting the importance of collaboration, education, and community involvement in effective implementation.', type='TextMessage'), StopMessage(source='DiGraphStopAgent', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 6, 5, 4, 17, 0, 866337, tzinfo=datetime.timezone.utc), content='Digraph execution is complete', type='StopMessage')], stop_reason='Stop message received')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from autogen_agentchat.agents import AssistantAgent, MessageFilterAgent, MessageFilterConfig, PerSourceFilter\n", "from autogen_agentchat.teams import (\n", "    DiGraphBuilder,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "model_client = OpenAIChatCompletionClient(model=\"gpt-4o-mini\")\n", "\n", "# Agents\n", "generator = AssistantAgent(\"generator\", model_client=model_client, system_message=\"Generate a list of creative ideas.\")\n", "reviewer = AssistantAgent(\n", "    \"reviewer\",\n", "    model_client=model_client,\n", "    system_message=\"Review ideas and provide feedbacks, or just 'APPROVE' for final approval.\",\n", ")\n", "summarizer_core = AssistantAgent(\n", "    \"summary\", model_client=model_client, system_message=\"Summarize the user request and the final feedback.\"\n", ")\n", "\n", "# Filtered summarizer\n", "filtered_summarizer = MessageFilterAgent(\n", "    name=\"summary\",\n", "    wrapped_agent=summarizer_core,\n", "    filter=MessageFilterConfig(\n", "        per_source=[\n", "            PerSourceFilter(source=\"user\", position=\"first\", count=1),\n", "            PerSourceFilter(source=\"reviewer\", position=\"last\", count=1),\n", "        ]\n", "    ),\n", ")\n", "\n", "# Build graph with conditional loop\n", "builder = DiGraphBuilder()\n", "builder.add_node(generator).add_node(reviewer).add_node(filtered_summarizer)\n", "builder.add_edge(generator, reviewer)\n", "builder.add_edge(reviewer, filtered_summarizer, condition=lambda msg: \"APPROVE\" in msg.to_model_text())\n", "builder.add_edge(reviewer, generator, condition=lambda msg: \"APPROVE\" not in msg.to_model_text())\n", "builder.set_entry_point(generator)  # Set entry point to generator. Required if there are no source nodes.\n", "graph = builder.build()\n", "\n", "# Create the flow\n", "flow = GraphFlow(\n", "    participants=builder.get_participants(),\n", "    graph=graph,\n", ")\n", "\n", "# Run the flow and pretty print the output in the console\n", "await Console(flow.run_stream(task=\"Brainstorm ways to reduce plastic waste.\"))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}