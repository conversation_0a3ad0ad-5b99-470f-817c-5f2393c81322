{"cells": [{"cell_type": "markdown", "id": "b2a89a30", "metadata": {}, "source": ["# Workbench (and MCP)\n", "\n", "A {py:class}`~autogen_core.tools.Workbench` provides a collection of tools that share state and resources.\n", "Different from {py:class}`~autogen_core.tools.Tool`, which provides an interface\n", "to a single tool, a workbench provides an interface to call different tools\n", "and receive results as the same types."]}, {"cell_type": "markdown", "id": "f6aa6692", "metadata": {}, "source": ["## Using Workbench\n", "\n", "Here is an example of how to create an agent using {py:class}`~autogen_core.tools.Workbench`."]}, {"cell_type": "code", "execution_count": 17, "id": "e8a489ec", "metadata": {}, "outputs": [], "source": ["import json\n", "from dataclasses import dataclass\n", "from typing import List\n", "\n", "from autogen_core import (\n", "    FunctionCall,\n", "    MessageContext,\n", "    RoutedAgent,\n", "    message_handler,\n", ")\n", "from autogen_core.model_context import ChatCompletionContext\n", "from autogen_core.models import (\n", "    AssistantMessage,\n", "    ChatCompletionClient,\n", "    FunctionExecutionResult,\n", "    FunctionExecutionResultMessage,\n", "    LLMMessage,\n", "    SystemMessage,\n", "    UserMessage,\n", ")\n", "from autogen_core.tools import ToolResult, Workbench"]}, {"cell_type": "code", "execution_count": 18, "id": "66674f0d", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class Message:\n", "    content: str\n", "\n", "\n", "class WorkbenchAgent(RoutedAgent):\n", "    def __init__(\n", "        self, model_client: ChatCompletionClient, model_context: ChatCompletionContext, workbench: Workbench\n", "    ) -> None:\n", "        super().__init__(\"An agent with a workbench\")\n", "        self._system_messages: List[LLMMessage] = [SystemMessage(content=\"You are a helpful AI assistant.\")]\n", "        self._model_client = model_client\n", "        self._model_context = model_context\n", "        self._workbench = workbench\n", "\n", "    @message_handler\n", "    async def handle_user_message(self, message: Message, ctx: MessageContext) -> Message:\n", "        # Add the user message to the model context.\n", "        await self._model_context.add_message(UserMessage(content=message.content, source=\"user\"))\n", "        print(\"---------User Message-----------\")\n", "        print(message.content)\n", "\n", "        # Run the chat completion with the tools.\n", "        create_result = await self._model_client.create(\n", "            messages=self._system_messages + (await self._model_context.get_messages()),\n", "            tools=(await self._workbench.list_tools()),\n", "            cancellation_token=ctx.cancellation_token,\n", "        )\n", "\n", "        # Run tool call loop.\n", "        while isinstance(create_result.content, list) and all(\n", "            isinstance(call, FunctionCall) for call in create_result.content\n", "        ):\n", "            print(\"---------Function Calls-----------\")\n", "            for call in create_result.content:\n", "                print(call)\n", "\n", "            # Add the function calls to the model context.\n", "            await self._model_context.add_message(AssistantMessage(content=create_result.content, source=\"assistant\"))\n", "\n", "            # Call the tools using the workbench.\n", "            print(\"---------Function Call Results-----------\")\n", "            results: List[ToolResult] = []\n", "            for call in create_result.content:\n", "                result = await self._workbench.call_tool(\n", "                    call.name, arguments=json.loads(call.arguments), cancellation_token=ctx.cancellation_token\n", "                )\n", "                results.append(result)\n", "                print(result)\n", "\n", "            # Add the function execution results to the model context.\n", "            await self._model_context.add_message(\n", "                FunctionExecutionResultMessage(\n", "                    content=[\n", "                        FunctionExecutionResult(\n", "                            call_id=call.id,\n", "                            content=result.to_text(),\n", "                            is_error=result.is_error,\n", "                            name=result.name,\n", "                        )\n", "                        for call, result in zip(create_result.content, results, strict=False)\n", "                    ]\n", "                )\n", "            )\n", "\n", "            # Run the chat completion again to reflect on the history and function execution results.\n", "            create_result = await self._model_client.create(\n", "                messages=self._system_messages + (await self._model_context.get_messages()),\n", "                tools=(await self._workbench.list_tools()),\n", "                cancellation_token=ctx.cancellation_token,\n", "            )\n", "\n", "        # Now we have a single message as the result.\n", "        assert isinstance(create_result.content, str)\n", "\n", "        print(\"---------Final Response-----------\")\n", "        print(create_result.content)\n", "\n", "        # Add the assistant message to the model context.\n", "        await self._model_context.add_message(AssistantMessage(content=create_result.content, source=\"assistant\"))\n", "\n", "        # Return the result as a message.\n", "        return Message(content=create_result.content)"]}, {"cell_type": "markdown", "id": "1361cce4", "metadata": {}, "source": ["In this example, the agent calls the tools provided by the workbench\n", "in a loop until the model returns a final answer."]}, {"cell_type": "markdown", "id": "c7f78834", "metadata": {}, "source": ["## MCP Workbench\n", "\n", "[Model Context Protocol (MCP)](https://modelcontextprotocol.io/) is a protocol\n", "for providing tools and resources\n", "to language models. An MCP server hosts a set of tools and manages their state,\n", "while an MCP client operates from the side of the language model and\n", "communicates with the server to access the tools, and to provide the\n", "language model with the context it needs to use the tools effectively.\n", "\n", "In AutoGen, we provide {py:class}`~autogen_ext.tools.mcp.McpWorkbench`\n", "that implements an MCP client. You can use it to create an agent that\n", "uses tools provided by MCP servers."]}, {"cell_type": "markdown", "id": "ff304136", "metadata": {}, "source": ["## Web Browsing Agent using Playwright MCP\n", "\n", "Here is an example of how we can use the [Playwright MCP server](https://github.com/microsoft/playwright-mcp)\n", "and the `WorkbenchAgent` class to create a web browsing agent.\n", "\n", "You may need to install the browser dependencies for <PERSON>wright."]}, {"cell_type": "code", "execution_count": 19, "id": "8500959b", "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# npx playwright install chrome"]}, {"cell_type": "markdown", "id": "103fa5f2", "metadata": {}, "source": ["Start the Playwright MCP server in a terminal."]}, {"cell_type": "code", "execution_count": 20, "id": "6d1250cc", "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["# npx @playwright/mcp@latest --port 8931"]}, {"cell_type": "markdown", "id": "da1dcb26", "metadata": {}, "source": ["Then, create the agent using the `WorkbenchAgent` class and\n", "{py:class}`~autogen_ext.tools.mcp.McpWorkbench` with the Playwright MCP server URL."]}, {"cell_type": "code", "execution_count": null, "id": "578420c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------User Message-----------\n", "Use Bing to find out the address of Microsoft Building 99\n", "---------Function Calls-----------\n", "FunctionCall(id='call_oJl0E0hWvmKZrzAM7huiIyus', arguments='{\"url\": \"https://www.bing.com\"}', name='browser_navigate')\n", "FunctionCall(id='call_Qfab5bAsveZIVg2v0aHl4Kgv', arguments='{}', name='browser_snapshot')\n", "---------<PERSON><PERSON> Call Results-----------\n", "type='ToolResult' name='browser_navigate' result=[TextResultContent(type='TextResultContent', content='- Ran Playwright code:\\n```js\\n// Navigate to https://www.bing.com\\nawait page.goto(\\'https://www.bing.com\\');\\n```\\n\\n- Page URL: https://www.bing.com/\\n- Page Title: Search - Microsoft Bing\\n- Page Snapshot\\n```yaml\\n- generic [ref=s1e2]:\\n  - generic [ref=s1e4]:\\n    - generic:\\n      - generic [ref=s1e6]:\\n        - generic [ref=s1e7]\\n        - generic [ref=s1e10]:\\n          - img \"Background image\" [ref=s1e12]\\n      - generic [ref=s1e14]:\\n        - generic [ref=s1e17]\\n        - generic [ref=s1e18]:\\n          - img \"Background image\" [ref=s1e20]\\n    - main [ref=s1e23]:\\n      - generic [ref=s1e24]:\\n        - generic [ref=s1e25]:\\n          - heading \"Trending Now on Bing\" [level=1] [ref=s1e26]\\n          - navigation [ref=s1e27]:\\n            - menubar [ref=s1e28]:\\n              - menuitem \"Copilot\" [ref=s1e29]:\\n                - link \"Copilot\" [ref=s1e30]:\\n                  - /url: /chat?FORM=hpcodx\\n                  - text: Copilot\\n              - menuitem \"Images\" [ref=s1e34]:\\n                - link \"Images\" [ref=s1e35]:\\n                  - /url: /images?FORM=Z9LH\\n              - menuitem \"Videos\" [ref=s1e36]:\\n                - link \"Videos\" [ref=s1e37]:\\n                  - /url: /videos?FORM=Z9LH1\\n              - menuitem \"Shopping\" [ref=s1e38]:\\n                - link \"Shopping\" [ref=s1e39]:\\n                  - /url: /shop?FORM=Z9LHS4\\n              - menuitem \"Maps\" [ref=s1e40]:\\n                - link \"Maps\" [ref=s1e41]:\\n                  - /url: /maps?FORM=Z9LH2\\n              - menuitem \"News\" [ref=s1e42]:\\n                - link \"News\" [ref=s1e43]:\\n                  - /url: /news/search?q=Top+stories&nvaug=%5bNewsVertical+Category%3d%22rt_MaxClass%22%5d&FORM=Z9LH3\\n              - menuitem \". . . More\" [ref=s1e44]:\\n                - text: . . .\\n                - tooltip \"More\" [ref=s1e45]\\n        - generic\\n      - generic [ref=s1e49]:\\n        - search [ref=s1e50]:\\n          - generic [ref=s1e52]:\\n            - textbox \"0 characters out of 2000\" [ref=s1e53]\\n          - button \"Search using voice\" [ref=s1e55]:\\n            - img [ref=s1e56]\\n            - text: Search using voice\\n        - link \"Open Copilot\" [ref=s1e61]:\\n          - /url: /chat?FORM=hpcodx\\n          - generic [ref=s1e63]\\n    - generic\\n    - generic [ref=s1e67]:\\n      - generic [ref=s1e69]:\\n        - generic [ref=s1e71]:\\n          - generic:\\n            - link \"Get the new Bing Wallpaper app\":\\n              - /url: https://go.microsoft.com/fwlink/?linkid=2127455\\n              - text: Get the new Bing Wallpaper app\\n            - \\'heading \"Image of the day: Spire Cove in Kenai Fjords National Park, Seward, Alaska\" [level=3]\\':\\n              - \\'link \"Image of the day: Spire Cove in Kenai Fjords National Park, Seward, Alaska\"\\':\\n                - /url: /search?q=Kenai+Fjords+National+Park+Alaska&form=hpcapt&filters=HpDate:\"20250424_0700\"\\n                - text: Spire Cove in Kenai Fjords National Park, Seward, Alaska\\n            - generic:\\n              - text: © Wander Photography/Getty Images\\n              - list:\\n                - listitem:\\n                  - button \"Download this image. Use of this image is restricted\\n                    to wallpaper only.\"\\n          - generic [ref=s1e84]:\\n            - link \"Rugged peaks and wild waters\" [ref=s1e86]:\\n              - /url: /search?q=Kenai+Fjords+National+Park+Alaska&form=hpcapt&filters=HpDate:\"20250424_0700\"\\n              - heading \"Rugged peaks and wild waters\" [level=2] [ref=s1e88]\\n            - generic [ref=s1e89]:\\n              - button \"Previous image\" [disabled] [ref=s1e90]\\n              - button \"Next image\" [disabled] [ref=s1e91]\\n        - button \"Feedback\" [ref=s1e92]:\\n          - img [ref=s1e93]\\n          - text: Feedback\\n        - complementary\\n```')] is_error=False\n", "type='ToolResult' name='browser_snapshot' result=[TextResultContent(type='TextResultContent', content='- Ran Playwright code:\\n```js\\n// <internal code to capture accessibility snapshot>\\n```\\n\\n- Page URL: https://www.bing.com/\\n- Page Title: Search - Microsoft Bing\\n- Page Snapshot\\n```yaml\\n- generic [ref=s2e2]:\\n  - generic [ref=s2e4]:\\n    - generic:\\n      - generic [ref=s2e6]:\\n        - generic [ref=s2e7]\\n        - generic [ref=s2e10]:\\n          - img \"Background image\" [ref=s2e12]\\n      - generic [ref=s2e14]:\\n        - generic [ref=s2e17]\\n        - generic [ref=s2e18]:\\n          - img \"Background image\" [ref=s2e20]\\n    - main [ref=s2e23]:\\n      - generic [ref=s2e24]:\\n        - generic [ref=s2e25]:\\n          - heading \"Trending Now on Bing\" [level=1] [ref=s2e26]\\n          - navigation [ref=s2e27]:\\n            - menubar [ref=s2e28]:\\n              - menuitem \"Copilot\" [ref=s2e29]:\\n                - link \"Copilot\" [ref=s2e30]:\\n                  - /url: /chat?FORM=hpcodx\\n                  - text: Copilot\\n              - menuitem \"Images\" [ref=s2e34]:\\n                - link \"Images\" [ref=s2e35]:\\n                  - /url: /images?FORM=Z9LH\\n              - menuitem \"Videos\" [ref=s2e36]:\\n                - link \"Videos\" [ref=s2e37]:\\n                  - /url: /videos?FORM=Z9LH1\\n              - menuitem \"Shopping\" [ref=s2e38]:\\n                - link \"Shopping\" [ref=s2e39]:\\n                  - /url: /shop?FORM=Z9LHS4\\n              - menuitem \"Maps\" [ref=s2e40]:\\n                - link \"Maps\" [ref=s2e41]:\\n                  - /url: /maps?FORM=Z9LH2\\n              - menuitem \"News\" [ref=s2e42]:\\n                - link \"News\" [ref=s2e43]:\\n                  - /url: /news/search?q=Top+stories&nvaug=%5bNewsVertical+Category%3d%22rt_MaxClass%22%5d&FORM=Z9LH3\\n              - menuitem \". . . More\" [ref=s2e44]:\\n                - text: . . .\\n                - tooltip \"More\" [ref=s2e45]\\n        - generic\\n      - generic [ref=s2e49]:\\n        - search [ref=s2e50]:\\n          - generic [ref=s2e52]:\\n            - textbox \"0 characters out of 2000\" [ref=s2e53]\\n          - button \"Search using voice\" [ref=s2e55]:\\n            - img [ref=s2e56]\\n            - text: Search using voice\\n        - link \"Open Copilot\" [ref=s2e61]:\\n          - /url: /chat?FORM=hpcodx\\n          - generic [ref=s2e63]\\n    - generic\\n    - generic [ref=s2e67]:\\n      - generic [ref=s2e69]:\\n        - generic [ref=s2e71]:\\n          - generic:\\n            - link \"Get the new Bing Wallpaper app\":\\n              - /url: https://go.microsoft.com/fwlink/?linkid=2127455\\n              - text: Get the new Bing Wallpaper app\\n            - \\'heading \"Image of the day: Spire Cove in Kenai Fjords National Park, Seward, Alaska\" [level=3]\\':\\n              - \\'link \"Image of the day: Spire Cove in Kenai Fjords National Park, Seward, Alaska\"\\':\\n                - /url: /search?q=Kenai+Fjords+National+Park+Alaska&form=hpcapt&filters=HpDate:\"20250424_0700\"\\n                - text: Spire Cove in Kenai Fjords National Park, Seward, Alaska\\n            - generic:\\n              - text: © Wander Photography/Getty Images\\n              - list:\\n                - listitem:\\n                  - button \"Download this image. Use of this image is restricted\\n                    to wallpaper only.\"\\n          - generic [ref=s2e84]:\\n            - link \"Rugged peaks and wild waters\" [ref=s2e86]:\\n              - /url: /search?q=Kenai+Fjords+National+Park+Alaska&form=hpcapt&filters=HpDate:\"20250424_0700\"\\n              - heading \"Rugged peaks and wild waters\" [level=2] [ref=s2e88]\\n            - generic [ref=s2e89]:\\n              - button \"Previous image\" [disabled] [ref=s2e90]\\n              - button \"Next image\" [disabled] [ref=s2e91]\\n        - button \"Feedback\" [ref=s2e92]:\\n          - img [ref=s2e93]\\n          - text: Feedback\\n        - complementary\\n```')] is_error=False\n", "---------Function Calls-----------\n", "FunctionCall(id='call_D1X5emmqqTxiaRtCsZiGHuBr', arguments='{\"url\":\"https://www.microsoft.com\"}', name='browser_navigate')\n", "---------<PERSON><PERSON> Call Results-----------\n", "type='ToolResult' name='browser_navigate' result=[TextResultContent(type='TextResultContent', content='- Ran Playwright code:\\n```js\\n// Navigate to https://www.microsoft.com\\nawait page.goto(\\'https://www.microsoft.com\\');\\n```\\n\\n- Page URL: https://www.microsoft.com/en-us/\\n- Page Title: Microsoft – AI, Cloud, Productivity, Computing, Gaming & Apps\\n- Page Snapshot\\n```yaml\\n- generic [ref=s1e2]:\\n  - generic [ref=s1e5]:\\n    - generic [ref=s1e7]:\\n      - generic [ref=s1e8]:\\n        - generic\\n        - link \"Skip to main content\" [ref=s1e12]:\\n          - /url: javascript:void(0)\\n        - banner [ref=s1e13]:\\n          - generic [ref=s1e15]:\\n            - link \"Microsoft\" [ref=s1e16]:\\n              - /url: https://www.microsoft.com\\n            - navigation \"Contextual menu\" [ref=s1e17]:\\n              - list [ref=s1e18]:\\n                - listitem [ref=s1e19]:\\n                  - link \"Microsoft 365\" [ref=s1e20]:\\n                    - /url: https://www.microsoft.com/microsoft-365\\n                - listitem [ref=s1e21]:\\n                  - link \"Teams\" [ref=s1e22]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-teams/group-chat-software\\n                - listitem [ref=s1e23]:\\n                  - link \"Copilot\" [ref=s1e24]:\\n                    - /url: https://copilot.microsoft.com/\\n                - listitem [ref=s1e25]:\\n                  - link \"Windows\" [ref=s1e26]:\\n                    - /url: https://www.microsoft.com/en-us/windows/\\n                - listitem [ref=s1e27]:\\n                  - link \"Surface\" [ref=s1e28]:\\n                    - /url: https://www.microsoft.com/surface\\n                - listitem [ref=s1e29]:\\n                  - link \"Xbox\" [ref=s1e30]:\\n                    - /url: https://www.xbox.com/\\n                - listitem [ref=s1e31]:\\n                  - link \"Deals\" [ref=s1e32]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/sale?icid=gm_nav_L0_salepage\\n                - listitem [ref=s1e33]:\\n                  - link \"Small Business\" [ref=s1e34]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/business\\n                - listitem [ref=s1e35]:\\n                  - link \"Support\" [ref=s1e36]:\\n                    - /url: https://support.microsoft.com/en-us\\n            - generic [ref=s1e37]:\\n              - navigation \"All Microsoft menu\" [ref=s1e39]:\\n                - list [ref=s1e40]:\\n                  - listitem [ref=s1e41]:\\n                    - button \"All Microsoft \\ue70d\" [ref=s1e43]:\\n                      - text: All Microsoft\\n                      - text: \\ue70d\\n              - search [ref=s1e45]:\\n                - button \"Search Microsoft.com\" [ref=s1e47]: \\ue721\\n              - link \"0 items in shopping cart\" [ref=s1e48]:\\n                - /url: https://www.microsoft.com/en-us/store/cart\\n                - text: \\ue7bf\\n              - generic [ref=s1e50]:\\n                - link \"Sign in to your account\" [ref=s1e51]:\\n                  - /url: https://www.microsoft.com/cascadeauth/store/account/signin?ru=https%3A%2F%2Fwww.microsoft.com%2Fen-us%2F\\n                  - text: Sign in to your account\\n  - generic [ref=s1e56]:\\n    - main [ref=s1e58]:\\n      - generic [ref=s1e59]:\\n        - generic [ref=s1e62]:\\n          - generic [ref=s1e64]:\\n            - region \"Announcement banner\" [ref=s1e65]:\\n              - paragraph [ref=s1e67]:\\n                - link \"Trade in and you could get cash back. Learn more\" [ref=s1e68]:\\n                  - /url: https://www.microsoft.com/en-us/store/b/why-microsoft-store?icid=mscom_marcom_TS1a_WhyBuy\\n        - generic [ref=s1e70]:\\n          - generic [ref=s1e71]:\\n            - \\'region \"featured products and announcements slideshow: navigate using the previous and next: navigate using the slide tabs\" [ref=s1e72]\\':\\n              - generic [ref=s1e74]: Slide 1 of 2. Meet Surface Pro\\n              - generic [ref=s1e75]:\\n                - \\'link \"Skip featured products and announcements slideshow: navigate using the previous and next: navigate using the slide tabs\" [ref=s1e76]\\':\\n                  - /url: \"#bd5bedab-7048-4f7d-8564-09f30af30317\"\\n                - generic [ref=s1e77]:\\n                  - generic [ref=s1e78]:\\n                    - button \"Pause\" [ref=s1e79]:\\n                      - text: Pause\\n                      - text: \\uf2d9\\n                    - button \"Previous \\ue76b\" [ref=s1e81]:\\n                      - text: Previous\\n                      - text: \\ue76b\\n                    - button \"Next \\ue76c\" [ref=s1e83]:\\n                      - text: Next\\n                      - text: \\ue76c\\n                  - region \"1 of 2\" [ref=s1e86]:\\n                    - generic [ref=s1e88]:\\n                      - generic [ref=s1e90]:\\n                        - img \"A Surface Pro Flex Keyboard and a Surface Pro,\\n                          11th Edition, a Copilot+ PC, in the color Sapphire.\"\\n                          [ref=s1e95]\\n                      - generic [ref=s1e97]:\\n                        - generic [ref=s1e99]:\\n                          - generic [ref=s1e101]:\\n                            - heading \"Meet Surface Pro\" [level=1] [ref=s1e103]\\n                            - text: This laptop\\'s unrivalled flexibility and AI features like Live Captions\\n                                and Cocreator enable you to do more than you\\n                                ever imagined.\\n                            - link \"Shop Surface Pro now\" [ref=s1e106]:\\n                              - /url: https://www.microsoft.com/en-us/surface/devices/surface-pro-11th-edition?icid=mscom_marcom_H1a_SurfacePro11Edition_FY24SpringSurface\\n                              - text: Shop now\\n            - text: \"End of featured products and announcements slideshow: navigate using the\\n                previous and next: navigate using the slide tabs section\"\\n        - generic [ref=s1e109]:\\n          - generic [ref=s1e111]:\\n            - generic [ref=s1e113]:\\n              - navigation \"product categories\" [ref=s1e114]:\\n                - list [ref=s1e115]:\\n                  - listitem [ref=s1e116]:\\n                    - link \"Shop Surface devices\" [ref=s1e118]:\\n                      - /url: https://www.microsoft.com/en-us/store/b/shop-all-microsoft-surface?icid=MSCOM_QL_Surface\\n                  - listitem [ref=s1e119]:\\n                    - link \"Shop Xbox games and consoles\" [ref=s1e121]:\\n                      - /url: https://www.microsoft.com/en-us/store/b/xbox?icid=MSCOM_QL_Xbox\\n                  - listitem [ref=s1e122]:\\n                    - link \"Shop for accessories\" [ref=s1e124]:\\n                      - /url: https://www.microsoft.com/en-us/store/b/accessories?icid=MSCOM_QL_Accessories\\n                  - listitem [ref=s1e125]:\\n                    - link \"Shop business products\" [ref=s1e127]:\\n                      - /url: https://www.microsoft.com/en-us/store/b/business?icid=MSCOM_QL_Business\\n                      - text: Shop for your business\\n                  - listitem [ref=s1e128]:\\n                    - link \"Find your next PC\" [ref=s1e130]:\\n                      - /url: https://www.microsoft.com/en-us/windows/help-me-choose?icid=MSCOM_QL_FindPC\\n                  - listitem [ref=s1e131]:\\n                    - link \"Choose your Microsoft 365\" [ref=s1e133]:\\n                      - /url: https://www.microsoft.com/EN-US/microsoft-365/compare-all-microsoft-365-products?icid=MSCOM_QL_M365\\n        - generic [ref=s1e135]:\\n          - generic [ref=s1e137]:\\n            - generic [ref=s1e139]:\\n              - generic [ref=s1e141]:\\n                - generic [ref=s1e143]:\\n                  - generic [ref=s1e144]:\\n                    - img \"A side view of Surface Laptop for Business in the\\n                      color Platinum.\" [ref=s1e149]\\n                  - generic [ref=s1e151]: New\\n                  - generic [ref=s1e152]:\\n                    - heading \"Surface Laptop for Business, Copilot+ PC | Intel\"\\n                      [level=2] [ref=s1e153]\\n                    - text: Uncompromising power, all-day battery life,* and unique AI\\n                        experiences—featuring Intel® Core™ Ultra processors\\n                        (Series 2).\\n                  - generic [ref=s1e156]:\\n                    - link \"Shop Surface Laptop for Business.\" [ref=s1e157]:\\n                      - /url: https://www.microsoft.com/en-us/d/surface-laptop-for-business-copilot-pc-intel/93dzmw6q4w2b?icid=mscom_marcom_CPH1a_SurfaceLaptopForBusinessCopilotPCIntel\\n                      - text: Shop now\\n                - generic [ref=s1e159]:\\n                  - generic [ref=s1e160]:\\n                    - img \"Red, white, blue, and black Xbox Wireless\\n                      Controllers\" [ref=s1e165]\\n                  - generic [ref=s1e167]:\\n                    - heading \"Xbox controllers\" [level=2] [ref=s1e168]\\n                    - text: Elite, wireless, adaptive—find the controller that fits your style of\\n                        play.\\n                  - generic [ref=s1e171]:\\n                    - link \"Shop Xbox controllers\" [ref=s1e172]:\\n                      - /url: https://www.microsoft.com/en-us/store/collections/XboxControllers?icid=mscom_marcom_CPH2a_XboxControllers\\n                      - text: Shop now\\n                - generic [ref=s1e174]:\\n                  - generic [ref=s1e175]:\\n                    - img \"An Xbox Series X 2 TB Galaxy Black Special Edition, a\\n                      White Xbox Series X 1 TB Digital Edition and a White Xbox\\n                      Series S 1 TB.\" [ref=s1e180]\\n                  - generic [ref=s1e182]:\\n                    - heading \"Trade in and get up to $150 for your used\\n                      console\" [level=2] [ref=s1e183]\\n                    - text: Buy a new Xbox Series X or S and get cash back on an eligible trade-in.\\n                        Limited-time offer.\\n                  - generic [ref=s1e186]:\\n                    - link \"Shop Xbox consoles\" [ref=s1e187]:\\n                      - /url: https://www.microsoft.com/en-us/store/collections/xboxconsoles?icid=mscom_marcom_CPH3a_XboxTradeInOffer\\n                    - link \"Check your device\\'s eligibility\" [ref=s1e188]:\\n                      - /url: https://www.microsoft.com/en-us/store/b/microsoft-trade-in?icid=mscom_marcom_CPH3b_XboxTradeInOffer\\n                - generic [ref=s1e190]:\\n                  - generic [ref=s1e191]:\\n                    - img \"Fresh new Xbox games featuring Dragon Ball Sparking\\n                      Zero, WWE2k25 and FC25.\" [ref=s1e196]\\n                  - generic [ref=s1e198]:\\n                    - heading \"Up to 70% off games\" [level=2] [ref=s1e199]\\n                    - text: Score spring savings on select Xbox and PC games. Sale ends April 30.\\n                  - generic [ref=s1e202]:\\n                    - link \"Shop the Xbox and PC game sale.\" [ref=s1e203]:\\n                      - /url: https://www.xbox.com/en-US/games/browse/spring-sale?icid=mscom_marcom_CPH4a_XboxGameSale2025\\n                      - text: Shop the sale\\n        - generic [ref=s1e205]:\\n          - generic [ref=s1e207]:\\n            - generic [ref=s1e209]:\\n              - generic [ref=s1e210]:\\n                - generic [ref=s1e212]:\\n                  - img \"A Surface Pro Signature Keyboard in Sapphire with an\\n                    Arc Mouse in Light Grey and Slim Pen 2.\" [ref=s1e217]\\n                - generic [ref=s1e219]:\\n                  - generic [ref=s1e221]:\\n                    - generic [ref=s1e223]:\\n                      - heading \"Made for Surface\" [level=2] [ref=s1e225]\\n                      - text: Find keyboards, pens, and other essentials designed to work seamlessly\\n                          with your Surface device.\\n                      - link \"Shop Surface accessories\" [ref=s1e228]:\\n                        - /url: https://www.microsoft.com/en-us/store/b/surface-accessories?icid=mscom_marcom_MPH1a_SurfaceAccessories\\n        - generic [ref=s1e230]:\\n          - generic [ref=s1e232]:\\n            - generic [ref=s1e233]:\\n              - heading \"For business\" [level=2] [ref=s1e235]\\n              - generic [ref=s1e237]:\\n                - generic [ref=s1e238]:\\n                  - generic [ref=s1e240]:\\n                    - generic [ref=s1e241]:\\n                      - img \"A side view of Surface Pro for Business in the\\n                        color Platinum.\"\\n                    - generic [ref=s1e248]: New\\n                    - generic [ref=s1e249]:\\n                      - heading \"Surface Pro for Business, Copilot+ PC | Intel\"\\n                        [level=3] [ref=s1e250]\\n                      - text: Ultra-versatile and built with Intel® Core™ Ultra processors (Series 2)\\n                          that power AI experiences to amplify your team’s\\n                          productivity.\\n                    - generic [ref=s1e253]:\\n                      - link \"Shop Surface Pro for Business.\" [ref=s1e254]:\\n                        - /url: https://www.microsoft.com/en-us/d/surface-pro-for-business-copilot-pc-intel/8qfmn9xp1rl9?icid=mscom_marcom_CPW1a_SurfaceProForBusinessCopilotPCIntel\\n                        - text: Shop now\\n                  - generic [ref=s1e256]:\\n                    - generic [ref=s1e257]\\n                    - generic [ref=s1e264]:\\n                      - heading \"Microsoft 365 Copilot\" [level=3] [ref=s1e265]\\n                      - text: Save time and focus on the things that matter most with AI in Microsoft\\n                          365 for business.\\n                    - generic [ref=s1e268]:\\n                      - link \"Learn more about Microsoft 365 Copilot\" [ref=s1e269]:\\n                        - /url: https://www.microsoft.com/en-us/microsoft-365/copilot/business?icid=mscom_marcom_CPW2a_M365forBusiness_Copilot\\n                        - text: Learn more\\n                  - generic [ref=s1e271]:\\n                    - generic [ref=s1e272]:\\n                      - img \"A Microsoft Teams video call.\"\\n                    - generic [ref=s1e279]:\\n                      - heading \"Get Microsoft Teams for your business\"\\n                        [level=3] [ref=s1e280]\\n                      - text: Online meetings, chat, real-time collaboration, and shared cloud\\n                          storage—all in one place.\\n                    - generic [ref=s1e283]:\\n                      - link \"Find the right Teams plan for your business.\" [ref=s1e284]:\\n                        - /url: https://www.microsoft.com/en-us/microsoft-teams/small-medium-business?icid=mscom_marcom_CPW3a_TeamsForBusiness\\n                        - text: Find the right plan for your business\\n                  - generic [ref=s1e286]:\\n                    - generic [ref=s1e287]\\n                    - generic [ref=s1e294]:\\n                      - heading \"Join the era of AI\" [level=3] [ref=s1e295]\\n                      - text: Create, communicate, and code with the latest Microsoft AI solutions.\\n                    - generic [ref=s1e298]:\\n                      - link \"Explore AI solutions\" [ref=s1e299]:\\n                        - /url: https://www.microsoft.com/en-us/ai?icid=mscom_marcom_CPW4a_AzureAI\\n        - generic [ref=s1e301]:\\n          - generic [ref=s1e303]:\\n            - generic [ref=s1e304]:\\n              - heading \"Explore more about AI and Copilot\" [level=2]\\n                [ref=s1e306]\\n              - generic [ref=s1e308]:\\n                - generic [ref=s1e309]:\\n                  - generic [ref=s1e311]:\\n                    - generic [ref=s1e312]:\\n                      - img \"collaged illustration of a woman running up an\\n                        escalator surrounded by stylized charts.\"\\n                    - generic [ref=s1e318]:\\n                      - heading \"How AI makes hard work easier\" [level=3]\\n                        [ref=s1e319]\\n                      - text: Dive into the surprising ways that Copilot reduces the mental effort of\\n                          complex tasks and enhances quality of work.\\n                    - generic [ref=s1e322]:\\n                      - link \"Uncover the details of how AI makes hard work easier.\" [ref=s1e323]:\\n                        - /url: https://www.microsoft.com/en-us/worklab/ai-data-drop-the-surprising-way-ai-makes-hard-work-easier?icid=mscom_marcom_CPAI1a_AIHardWorkEasier\\n                        - text: Uncover the details\\n                  - generic [ref=s1e325]:\\n                    - generic [ref=s1e326]:\\n                      - img \"Azeem Azhar.\"\\n                    - generic [ref=s1e332]:\\n                      - heading \"How AI agents are transforming work\" [level=3]\\n                        [ref=s1e333]\\n                      - text: On the WorkLab podcast, Azeem Azhar—a global thought leader—shares\\n                          insights on the power of deep research AI and building\\n                          a \"brain trust\" of agents.\\n                    - generic [ref=s1e336]:\\n                      - link \"Learn more about how AI agents are transforming work.\" [ref=s1e337]:\\n                        - /url: https://www.microsoft.com/en-us/worklab/podcast/azeem-azhar-on-how-ai-agents-are-transforming-work?icid=mscom_marcom_CPAI2a_WorkLabAIAgents\\n                        - text: Learn more\\n                  - generic [ref=s1e339]:\\n                    - generic [ref=s1e340]:\\n                      - img \"A multifaceted gem reflects the possibilities of\\n                        AI.\"\\n                    - generic [ref=s1e346]:\\n                      - heading \"Why multimodal AI matters\" [level=3]\\n                        [ref=s1e347]\\n                      - text: AI models are using images, audio, and video to solve real-world\\n                          challenges—like helping doctors diagnose patients or\\n                          meteorologists predict storms.\\n                    - generic [ref=s1e350]:\\n                      - link \"Find out more about multimodal AI.\" [ref=s1e351]:\\n                        - /url: https://news.microsoft.com/source/features/ai/beyond-words-ai-goes-multimodal-to-meet-you-where-you-are/?icid=mscom_marcom_CPAI3a_MultimodalAI\\n                        - text: Find out more\\n        - generic [ref=s1e353]:\\n          - generic [ref=s1e355]:\\n            - generic [ref=s1e357]:\\n              - \\'region \"human-interest articles and stories slideshow: navigate using the slide tabs\" [ref=s1e358]\\':\\n                - generic [ref=s1e360]: Slide 1 of 2. Earth’s future in 3D\\n                - generic [ref=s1e361]:\\n                  - \\'link \"Skip human-interest articles and stories slideshow: navigate using the slide tabs\" [ref=s1e362]\\':\\n                    - /url: \"#c3c99f7a-0722-484c-9b77-b90c15e84fe1\"\\n                  - generic [ref=s1e363]:\\n                    - generic [ref=s1e364]:\\n                      - button \"Pause\" [ref=s1e365]:\\n                        - text: Pause\\n                        - text: \\uf2d9\\n                      - button \"Previous \\ue76b\" [ref=s1e367]:\\n                        - text: Previous\\n                        - text: \\ue76b\\n                      - button \"Next \\ue76c\" [ref=s1e369]:\\n                        - text: Next\\n                        - text: \\ue76c\\n                    - region \"1 of 2\" [ref=s1e372]:\\n                      - generic [ref=s1e374]:\\n                        - generic [ref=s1e376]:\\n                          - img \"A boy wearing a Hololens, a mixed reality\\n                            headset, comes face to face with a sea turtle in a\\n                            museum hall.\" [ref=s1e381]\\n                        - generic [ref=s1e383]:\\n                          - generic [ref=s1e385]:\\n                            - generic [ref=s1e387]:\\n                              - heading \"Earth’s future in 3D\" [level=2]\\n                                [ref=s1e389]\\n                              - text: Microsoft and the Natural History Museum London are imagining what’s\\n                                  possible for the planet in 2125 through an\\n                                  innovative exhibit.\\n                              - \\'link \"Explore Visions of Nature: A Mixed Reality Experience.\" [ref=s1e392]\\':\\n                                - /url: https://unlocked.microsoft.com/nhm-visions-of-nature/?icid=mscom_marcom_SAM1a_NaturalHistoryMuseum\\n                                - text: Explore Visions of Nature\\n              - text: \"End of human-interest articles and stories slideshow: navigate using the\\n                  slide tabs section\"\\n        - generic [ref=s1e395]:\\n          - generic [ref=s1e397]:\\n            - generic [ref=s1e399]:\\n              - region \"follow us on social media\" [ref=s1e400]:\\n                - heading \"Follow Microsoft\" [level=2] [ref=s1e401]\\n                - list [ref=s1e402]:\\n                  - listitem [ref=s1e403]:\\n                    - link \"Follow Microsoft on Facebook, opens in a new tab\" [ref=s1e404]:\\n                      - /url: https://www.facebook.com/Microsoft\\n                      - img \"Facebook\" [ref=s1e405]\\n                  - listitem [ref=s1e406]:\\n                    - link \"Follow Microsoft on X, opens in a new tab\" [ref=s1e407]:\\n                      - /url: https://twitter.com/microsoft\\n                      - img \"X\" [ref=s1e408]\\n                  - listitem [ref=s1e409]:\\n                    - link \"Follow Microsoft on Linkedin, opens in a new tab\" [ref=s1e410]:\\n                      - /url: https://www.linkedin.com/company/microsoft\\n                      - img \"LinkedIn\" [ref=s1e411]\\n        - generic\\n        - generic:\\n          - generic\\n        - generic [ref=s1e420]:\\n          - generic [ref=s1e421]:\\n            - link \"Back to top\" [ref=s1e424]:\\n              - /url: \"#page-top\"\\n              - generic [ref=s1e425]:\\n                - text: \\ue74a\\n                - text: Back to top\\n  - generic [ref=s1e428]:\\n    - generic [ref=s1e430]:\\n      - contentinfo [ref=s1e431]:\\n        - navigation \"Footer Resource links\" [ref=s1e432]:\\n          - generic:\\n            - generic [ref=s1e434]:\\n              - heading \"What\\'s new\" [level=2] [ref=s1e435]\\n              - list [ref=s1e436]:\\n                - listitem [ref=s1e437]:\\n                  - link \"Surface Pro What\\'s new\" [ref=s1e438]:\\n                    - /url: https://www.microsoft.com/en-us/surface/devices/surface-pro-11th-edition\\n                    - text: Surface Pro\\n                - listitem [ref=s1e439]:\\n                  - link \"Surface Laptop What\\'s new\" [ref=s1e440]:\\n                    - /url: https://www.microsoft.com/en-us/surface/devices/surface-laptop-7th-edition\\n                    - text: Surface Laptop\\n                - listitem [ref=s1e441]:\\n                  - link \"Surface Laptop Studio 2 What\\'s new\" [ref=s1e442]:\\n                    - /url: https://www.microsoft.com/en-us/d/Surface-Laptop-Studio-2/8rqr54krf1dz\\n                    - text: Surface Laptop Studio 2\\n                - listitem [ref=s1e443]:\\n                  - link \"Surface Laptop Go 3 What\\'s new\" [ref=s1e444]:\\n                    - /url: https://www.microsoft.com/en-us/d/Surface-Laptop-Go-3/8p0wwgj6c6l2\\n                    - text: Surface Laptop Go 3\\n                - listitem [ref=s1e445]:\\n                  - link \"Microsoft Copilot What\\'s new\" [ref=s1e446]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-copilot\\n                    - text: Microsoft Copilot\\n                - listitem [ref=s1e447]:\\n                  - link \"AI in Windows What\\'s new\" [ref=s1e448]:\\n                    - /url: https://www.microsoft.com/en-us/windows/copilot-ai-features\\n                    - text: AI in Windows\\n                - listitem [ref=s1e449]:\\n                  - link \"Explore Microsoft products What\\'s new\" [ref=s1e450]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-products-and-apps\\n                    - text: Explore Microsoft products\\n                - listitem [ref=s1e451]:\\n                  - link \"Windows 11 apps What\\'s new\" [ref=s1e452]:\\n                    - /url: https://www.microsoft.com/windows/windows-11-apps\\n                    - text: Windows 11 apps\\n            - generic [ref=s1e453]:\\n              - heading \"Microsoft Store\" [level=2] [ref=s1e454]\\n              - list [ref=s1e455]:\\n                - listitem [ref=s1e456]:\\n                  - link \"Account profile Microsoft Store\" [ref=s1e457]:\\n                    - /url: https://account.microsoft.com/\\n                    - text: Account profile\\n                - listitem [ref=s1e458]:\\n                  - link \"Download Center Microsoft Store\" [ref=s1e459]:\\n                    - /url: https://www.microsoft.com/en-us/download\\n                    - text: Download Center\\n                - listitem [ref=s1e460]:\\n                  - link \"Microsoft Store support Microsoft Store\" [ref=s1e461]:\\n                    - /url: https://go.microsoft.com/fwlink/?linkid=2139749\\n                    - text: Microsoft Store support\\n                - listitem [ref=s1e462]:\\n                  - link \"Returns Microsoft Store\" [ref=s1e463]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/returns\\n                    - text: Returns\\n                - listitem [ref=s1e464]:\\n                  - link \"Order tracking Microsoft Store\" [ref=s1e465]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/order-tracking\\n                    - text: Order tracking\\n                - listitem [ref=s1e466]:\\n                  - link \"Certified Refurbished Microsoft Store\" [ref=s1e467]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/certified-refurbished-products\\n                    - text: Certified Refurbished\\n                - listitem [ref=s1e468]:\\n                  - link \"Microsoft Store Promise Microsoft Store\" [ref=s1e469]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/why-microsoft-store?icid=footer_why-msft-store_7102020\\n                    - text: Microsoft Store Promise\\n                - listitem [ref=s1e470]:\\n                  - link \"Flexible Payments Microsoft Store\" [ref=s1e471]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/payment-financing-options?icid=footer_financing_vcc\\n                    - text: Flexible Payments\\n            - generic [ref=s1e472]:\\n              - heading \"Education\" [level=2] [ref=s1e473]\\n              - list [ref=s1e474]:\\n                - listitem [ref=s1e475]:\\n                  - link \"Microsoft in education Education\" [ref=s1e476]:\\n                    - /url: https://www.microsoft.com/en-us/education\\n                    - text: Microsoft in education\\n                - listitem [ref=s1e477]:\\n                  - link \"Devices for education Education\" [ref=s1e478]:\\n                    - /url: https://www.microsoft.com/en-us/education/devices/overview\\n                    - text: Devices for education\\n                - listitem [ref=s1e479]:\\n                  - link \"Microsoft Teams for Education Education\" [ref=s1e480]:\\n                    - /url: https://www.microsoft.com/en-us/education/products/teams\\n                    - text: Microsoft Teams for Education\\n                - listitem [ref=s1e481]:\\n                  - link \"Microsoft 365 Education Education\" [ref=s1e482]:\\n                    - /url: https://www.microsoft.com/en-us/education/products/microsoft-365\\n                    - text: Microsoft 365 Education\\n                - listitem [ref=s1e483]:\\n                  - link \"How to buy for your school Education\" [ref=s1e484]:\\n                    - /url: https://www.microsoft.com/education/how-to-buy\\n                    - text: How to buy for your school\\n                - listitem [ref=s1e485]:\\n                  - link \"Educator training and development Education\" [ref=s1e486]:\\n                    - /url: https://education.microsoft.com/\\n                    - text: Educator training and development\\n                - listitem [ref=s1e487]:\\n                  - link \"Deals for students and parents Education\" [ref=s1e488]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/education\\n                    - text: Deals for students and parents\\n                - listitem [ref=s1e489]:\\n                  - link \"Azure for students Education\" [ref=s1e490]:\\n                    - /url: https://azure.microsoft.com/en-us/free/students/\\n                    - text: Azure for students\\n          - generic:\\n            - generic [ref=s1e492]:\\n              - heading \"Business\" [level=2] [ref=s1e493]\\n              - list [ref=s1e494]:\\n                - listitem [ref=s1e495]:\\n                  - link \"Microsoft Cloud Business\" [ref=s1e496]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-cloud\\n                    - text: Microsoft Cloud\\n                - listitem [ref=s1e497]:\\n                  - link \"Microsoft Security Business\" [ref=s1e498]:\\n                    - /url: https://www.microsoft.com/en-us/security\\n                    - text: Microsoft Security\\n                - listitem [ref=s1e499]:\\n                  - link \"Dynamics 365 Business\" [ref=s1e500]:\\n                    - /url: https://www.microsoft.com/en-us/dynamics-365\\n                    - text: Dynamics 365\\n                - listitem [ref=s1e501]:\\n                  - link \"Microsoft 365 Business\" [ref=s1e502]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-365/business\\n                    - text: Microsoft 365\\n                - listitem [ref=s1e503]:\\n                  - link \"Microsoft Power Platform Business\" [ref=s1e504]:\\n                    - /url: https://www.microsoft.com/en-us/power-platform\\n                    - text: Microsoft Power Platform\\n                - listitem [ref=s1e505]:\\n                  - link \"Microsoft Teams Business\" [ref=s1e506]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-teams/group-chat-software\\n                    - text: Microsoft Teams\\n                - listitem [ref=s1e507]:\\n                  - link \"Microsoft 365 Copilot Business\" [ref=s1e508]:\\n                    - /url: https://www.microsoft.com/en-us/microsoft-365/copilot/copilot-for-work\\n                    - text: Microsoft 365 Copilot\\n                - listitem [ref=s1e509]:\\n                  - link \"Small Business Business\" [ref=s1e510]:\\n                    - /url: https://www.microsoft.com/en-us/store/b/business?icid=CNavBusinessStore\\n                    - text: Small Business\\n            - generic [ref=s1e511]:\\n              - heading \"Developer & IT\" [level=2] [ref=s1e512]\\n              - list [ref=s1e513]:\\n                - listitem [ref=s1e514]:\\n                  - link \"Azure Developer & IT\" [ref=s1e515]:\\n                    - /url: https://azure.microsoft.com/en-us/\\n                    - text: Azure\\n                - listitem [ref=s1e516]:\\n                  - link \"Microsoft Developer Developer & IT\" [ref=s1e517]:\\n                    - /url: https://developer.microsoft.com/en-us/\\n                    - text: Microsoft Developer\\n                - listitem [ref=s1e518]:\\n                  - link \"Microsoft Learn Developer & IT\" [ref=s1e519]:\\n                    - /url: https://learn.microsoft.com/\\n                    - text: Microsoft Learn\\n                - listitem [ref=s1e520]:\\n                  - link \"Support for AI marketplace apps Developer & IT\" [ref=s1e521]:\\n                    - /url: https://www.microsoft.com/isv/isv-success?ocid=cmm3atxvn98\\n                    - text: Support for AI marketplace apps\\n                - listitem [ref=s1e522]:\\n                  - link \"Microsoft Tech Community Developer & IT\" [ref=s1e523]:\\n                    - /url: https://techcommunity.microsoft.com/\\n                    - text: Microsoft Tech Community\\n                - listitem [ref=s1e524]:\\n                  - link \"Azure Marketplace Developer & IT\" [ref=s1e525]:\\n                    - /url: https://azuremarketplace.microsoft.com/en-us/\\n                    - text: Azure Marketplace\\n                - listitem [ref=s1e526]:\\n                  - link \"AppSource Developer & IT\" [ref=s1e527]:\\n                    - /url: https://appsource.microsoft.com/en-us/\\n                    - text: AppSource\\n                - listitem [ref=s1e528]:\\n                  - link \"Visual Studio Developer & IT\" [ref=s1e529]:\\n                    - /url: https://visualstudio.microsoft.com/\\n                    - text: Visual Studio\\n            - generic [ref=s1e530]:\\n              - heading \"Company\" [level=2] [ref=s1e531]\\n              - list [ref=s1e532]:\\n                - listitem [ref=s1e533]:\\n                  - link \"Careers Company\" [ref=s1e534]:\\n                    - /url: https://careers.microsoft.com/\\n                    - text: Careers\\n                - listitem [ref=s1e535]:\\n                  - link \"About Microsoft Company\" [ref=s1e536]:\\n                    - /url: https://www.microsoft.com/about\\n                    - text: About Microsoft\\n                - listitem [ref=s1e537]:\\n                  - link \"Company news Company\" [ref=s1e538]:\\n                    - /url: https://news.microsoft.com/\\n                    - text: Company news\\n                - listitem [ref=s1e539]:\\n                  - link \"Privacy at Microsoft Company\" [ref=s1e540]:\\n                    - /url: https://privacy.microsoft.com/en-us\\n                    - text: Privacy at Microsoft\\n                - listitem [ref=s1e541]:\\n                  - link \"Investors Company\" [ref=s1e542]:\\n                    - /url: https://www.microsoft.com/investor/default.aspx\\n                    - text: Investors\\n                - listitem [ref=s1e543]:\\n                  - link \"Diversity and inclusion Company\" [ref=s1e544]:\\n                    - /url: https://www.microsoft.com/en-us/diversity/\\n                    - text: Diversity and inclusion\\n                - listitem [ref=s1e545]:\\n                  - link \"Accessibility Company\" [ref=s1e546]:\\n                    - /url: https://www.microsoft.com/en-us/accessibility\\n                    - text: Accessibility\\n                - listitem [ref=s1e547]:\\n                  - link \"Sustainability Company\" [ref=s1e548]:\\n                    - /url: https://www.microsoft.com/en-us/sustainability/\\n                    - text: Sustainability\\n        - generic [ref=s1e549]:\\n          - link \"Content Language Selector. Currently set to English (United States)\" [ref=s1e550]:\\n            - /url: https://www.microsoft.com/en-us/locale\\n            - text: \\ue909 English (United States)\\n          - link \"Your Privacy Choices Opt-Out Icon Your Privacy Choices\" [ref=s1e551]:\\n            - /url: https://aka.ms/yourcaliforniaprivacychoices\\n            - img \"Your Privacy Choices Opt-Out Icon\" [ref=s1e552]\\n            - text: Your Privacy Choices\\n          - link \"Consumer Health Privacy\" [ref=s1e558]:\\n            - /url: https://go.microsoft.com/fwlink/?linkid=2259814\\n            - text: Consumer Health Privacy\\n          - navigation \"Microsoft corporate links\":\\n            - list [ref=s1e561]:\\n              - listitem [ref=s1e562]:\\n                - link \"Sitemap\" [ref=s1e563]:\\n                  - /url: https://www.microsoft.com/en-us/sitemap1.aspx\\n              - listitem [ref=s1e564]:\\n                - link \"Contact Microsoft\" [ref=s1e565]:\\n                  - /url: https://support.microsoft.com/contactus\\n              - listitem [ref=s1e566]:\\n                - link \"Privacy\" [ref=s1e567]:\\n                  - /url: https://go.microsoft.com/fwlink/?LinkId=521839\\n              - listitem [ref=s1e568]:\\n                - link \"Terms of use\" [ref=s1e569]:\\n                  - /url: https://go.microsoft.com/fwlink/?LinkID=206977\\n              - listitem [ref=s1e570]:\\n                - link \"Trademarks\" [ref=s1e571]:\\n                  - /url: https://go.microsoft.com/fwlink/?linkid=2196228\\n              - listitem [ref=s1e572]:\\n                - link \"Safety & eco\" [ref=s1e573]:\\n                  - /url: https://go.microsoft.com/fwlink/?linkid=2196227\\n              - listitem [ref=s1e574]:\\n                - link \"Recycling\" [ref=s1e575]:\\n                  - /url: https://www.microsoft.com/en-us/legal/compliance/recycling\\n              - listitem [ref=s1e576]:\\n                - link \"About our ads\" [ref=s1e577]:\\n                  - /url: https://choice.microsoft.com\\n              - listitem [ref=s1e578]: © Microsoft 2025\\n  - region \"Chat with an Expert\":\\n    - generic:\\n      - generic [ref=s1e587]:\\n        - paragraph [ref=s1e589]:\\n          - text: Need help?\\n          - text: Let\\'s chat\\n        - img \"Need Help? Lets Chat\" [ref=s1e591]\\n        - button \"Need help? Let\\'s chat\" [ref=s1e592]\\n```')] is_error=False\n", "---------Function Calls-----------\n", "FunctionCall(id='call_4AtqCm5GVIRUqgR8LtJ4pGWF', arguments='{\"url\":\"https://www.bing.com/search?q=Microsoft+Building+99+address\"}', name='browser_navigate')\n", "---------<PERSON><PERSON> Call Results-----------\n", "type='ToolResult' name='browser_navigate' result=[TextResultContent(type='TextResultContent', content='- Ran Playwright code:\\n```js\\n// Navigate to https://www.bing.com/search?q=Microsoft+Building+99+address\\nawait page.goto(\\'https://www.bing.com/search?q=Microsoft+Building+99+address\\');\\n```\\n\\n- Page URL: https://www.bing.com/search?q=Microsoft+Building+99+address\\n- Page Title: Microsoft Building 99 address - Search\\n- Page Snapshot\\n```yaml\\n- generic [ref=s1e2]:\\n  - banner [ref=s1e3]:\\n    - button \"Skip to content\" [ref=s1e4]:\\n      - generic [ref=s1e6]: Skip to content\\n    - generic [ref=s1e7]:\\n      - link \"Back to Bing search\" [ref=s1e8]:\\n        - /url: /?FORM=Z9FD1\\n        - heading \"Back to Bing search\" [level=1] [ref=s1e9]\\n      - search [ref=s1e10]:\\n        - link \"Search button\" [ref=s1e12]:\\n          - /url: javascript:void(0)\\n          - generic [ref=s1e13]:\\n            - button \"Search\" [ref=s1e15]\\n        - searchbox \"Enter your search here - Search suggestions will show as you type\" [ref=s1e16]: Microsoft Building 99 address\\n        - generic [ref=s1e17]\\n        - generic [ref=s1e21]:\\n          - button \"Search using an image\" [ref=s1e22]\\n      - link \"Chat with Copilot\" [ref=s1e25]:\\n        - /url: /chat?q=Microsoft+Building+99+address&sendquery=1&form=HECODX\\n        - button \"Chat with Copilot\" [ref=s1e26]\\n    - complementary \"Account Rewards and Preferences\" [ref=s1e28]:\\n      - link \"Sign in\" [ref=s1e29]:\\n        - /url: javascript:void(0)\\n        - generic [ref=s1e31]:\\n          - button \"Sign in\" [ref=s1e32]\\n      - button \"Microsoft Rewards\" [ref=s1e33]:\\n        - generic [ref=s1e35]:\\n          - text: Rewards\\n          - img [ref=s1e38]\\n      - button \"Mobile\" [ref=s1e42]:\\n        - text: Mobile\\n        - img [ref=s1e44]\\n      - button \"Settings and quick links\" [ref=s1e46]\\n    - navigation \"Search Filter\" [ref=s1e47]:\\n      - list [ref=s1e48]:\\n        - listitem [ref=s1e49]:\\n          - link \"All\" [ref=s1e50]:\\n            - /url: /?scope=web&FORM=HDRSC1\\n            - text: All\\n        - listitem [ref=s1e52]:\\n          - link \"Search\" [ref=s1e53]:\\n            - /url: /copilotsearch?q=Microsoft+Building+99+address&FORM=CSSCOP\\n            - img [ref=s1e54]\\n            - text: Search\\n        - listitem [ref=s1e56]:\\n          - link \"Copilot\" [ref=s1e57]:\\n            - /url: /chat?q=Microsoft+Building+99+address&sendquery=1&FORM=SCCODX\\n        - listitem [ref=s1e58]:\\n          - link \"Videos\" [ref=s1e59]:\\n            - /url: /videos/search?q=Microsoft+Building+99+address&FORM=HDRSC4\\n        - listitem [ref=s1e60]:\\n          - link \"Images\" [ref=s1e61]:\\n            - /url: /images/search?q=Microsoft+Building+99+address&FORM=HDRSC3\\n        - listitem [ref=s1e62]:\\n          - link \"Maps\" [ref=s1e63]:\\n            - /url: /maps?q=Microsoft+Building+99+address&FORM=HDRSC6\\n        - listitem [ref=s1e64]:\\n          - link \"News\" [ref=s1e65]:\\n            - /url: /news/search?q=Microsoft+Building+99+address&FORM=HDRSC7\\n        - listitem [ref=s1e66]:\\n          - button \"More\" [ref=s1e67]:\\n            - img [ref=s1e69]\\n            - text: More\\n        - listitem [ref=s1e72]:\\n          - link \"Tools\" [ref=s1e73]:\\n            - /url: javascript:void(0)\\n  - main \"Search Results\" [ref=s1e76]:\\n    - generic [ref=s1e77]:\\n      - list [ref=s1e78]:\\n        - listitem [ref=s1e79]:\\n          - generic [ref=s1e81]:\\n            - generic [ref=s1e83]:\\n              - generic [ref=s1e85]:\\n                - generic [ref=s1e87]:\\n                  - list \"Please use arrow keys to navigate\" [ref=s1e88]:\\n                    - listitem [ref=s1e89]:\\n                      - generic [ref=s1e90]:\\n                        - link \"campusbuilding.com\" [ref=s1e92]:\\n                          - /url: https://campusbuilding.com/b/microsoft-building-99/\\n                          - generic [ref=s1e94]:\\n                            - generic [ref=s1e96]\\n                          - generic [ref=s1e97]:\\n                            - text: campusbuilding.com\\n                            - generic [ref=s1e100]: https://campusbuilding.com\\n                        - heading \"Microsoft Building 99 Building Details\" [level=2] [ref=s1e102]:\\n                          - link \"Microsoft Building 99 Building Details\" [ref=s1e103]:\\n                            - /url: https://campusbuilding.com/b/microsoft-building-99/\\n                        - list [ref=s1e105]:\\n                          - listitem [ref=s1e106]:\\n                            - generic [ref=s1e107]:\\n                              - link \"The address of Microsoft Building 99 is 14820 NE 36th St, Redmond WA 98052. Microsoft Building 99 is near the intersection of Northeast 33rd Court and 143rd Place Northeast. Micr…\" [ref=s1e109]:\\n                                - /url: https://campusbuilding.com/b/microsoft-building-99/\\n                                - text: The address of Microsoft Building 99\\n                                - strong [ref=s1e110]: is\\n                                - strong [ref=s1e111]: \"14820\"\\n                                - strong [ref=s1e112]: NE\\n                                - strong [ref=s1e113]: 36th\\n                                - strong [ref=s1e114]: St\\n                                - text: \",\"\\n                                - strong [ref=s1e115]: Redmond\\n                                - strong [ref=s1e116]: WA\\n                                - strong [ref=s1e117]: \"98052\"\\n                                - text: . Microsoft Building 99 is near the intersection of Northeast 33rd Court\\n                                    and 143rd Place Northeast. Micr…\\n                              - generic [ref=s1e118]:\\n                                - generic [ref=s1e119]:\\n                                  - generic [ref=s1e120]:\\n                                    - link \"Microsoft The Commons Mixer\" [ref=s1e121]:\\n                                      - /url: https://campusbuilding.com/b/microsoft-the-commons-mixer/\\n                                      - text: Microsoft The Commons Mixer\\n                                    - text: This building has a Microsoft IT Tech Link. The Microsoft Techlink is a\\n                                        place f…\\n                                  - generic [ref=s1e124]:\\n                                    - link \"Microsoft Studio H\" [ref=s1e125]:\\n                                      - /url: https://campusbuilding.com/b/microsoft-studio-h/\\n                                      - text: Microsoft Studio H\\n                                    - text: Food, coffee, and restaurants close to Microsoft Studio H. Microsoft Cafe\\n                                        H is …\\n                                  - generic [ref=s1e128]:\\n                                    - link \"Microsoft Studio G\" [ref=s1e129]:\\n                                      - /url: https://campusbuilding.com/b/microsoft-studio-g/\\n                                      - text: Microsoft Studio G\\n                                    - text: Food, coffee, and restaurants close to Microsoft Studio G. Microsoft Cafe\\n                                        H is …\\n                                - generic [ref=s1e132]:\\n                                  - generic [ref=s1e133]:\\n                                    - link \"Microsoft Studio E\" [ref=s1e134]:\\n                                      - /url: https://campusbuilding.com/b/microsoft-studio-e/\\n                                      - text: Microsoft Studio E\\n                                    - text: Microsoft Building 123 0.12 miles; Microsoft The Commons Mixer 0.12 mil…\\n                                  - generic [ref=s1e137]:\\n                                    - link \"Microsoft Building 113\" [ref=s1e138]:\\n                                      - /url: https://campusbuilding.com/b/microsoft-building-113/\\n                                      - text: Microsoft Building 113\\n                                    - text: The address of Microsoft Building 113 is 14870 NE 31st Way, Redmond WA\\n                                        980…\\n                                  - generic [ref=s1e141]:\\n                                    - link \"Redmond Main Campus\" [ref=s1e142]:\\n                                      - /url: https://campusbuilding.com/c/microsoft-redmond-main-campus/\\n                                      - text: Redmond Main Campus\\n                                    - text: There are 95 buildings at the Microsoft Redmond Main Campus.\\n                    - listitem [ref=s1e145]:\\n                      - generic [ref=s1e147]:\\n                        - generic [ref=s1e148]:\\n                          - link \"Redmond, Washington - Wikipedia\" [ref=s1e149]:\\n                            - /url: https://en.wikipedia.org/wiki/Redmond,_Washington\\n                            - heading \"Redmond, Washington - Wikipedia\"\\n                              [level=1] [ref=s1e150]\\n                          - link \"Redmond, Washington - Wikipedia\" [ref=s1e151]:\\n                            - /url: https://en.wikipedia.org/wiki/Redmond,_Washington\\n                            - text: City in Washington\\n                            - text: Redmond is a city in King County, Washington, United States, located 15\\n                                miles east of Seattle. The population was 73,256\\n                                at the 2020 census. Redmond is best known as the\\n                                home of Microsoft and Nintendo of America. The\\n                                city has a large technology industry in addition\\n                                to being a...\\n                            - text: See more on Wikipedia\\n                        - link \"Redmond, Washington - Wikipedia\" [ref=s1e156]:\\n                          - /url: https://en.wikipedia.org/wiki/Redmond,_Washington\\n                          - generic [ref=s1e158]\\n                      - generic [ref=s1e160]:\\n                        - \\'link \"Microsoft\\'\\'s Building 99 from YouTube · Duration: 44 seconds · 28.7K views · uploaded on May 20, 2010 · uploaded by CNET · Click to play.\" [ref=s1e162]\\':\\n                          - /url: /videos/riverview/relatedvideo?q=Microsoft+Building+99+address&&mid=15C1FEC0FBDB1C2218B715C1FEC0FBDB1C2218B7&FORM=VAMGZC\\n                          - generic [ref=s1e163]:\\n                            - generic [ref=s1e164]:\\n                              - img \"Microsoft\\'s Building 99\" [ref=s1e166]\\n                              - generic [ref=s1e169]:\\n                                - generic [ref=s1e171]: 00:44\\n                            - generic [ref=s1e172]:\\n                              - generic [ref=s1e174]:\\n                                - generic [ref=s1e176]: YouTube\\n                                - text: › CNET\\n                                - text: · 28.7K views\\n                                - text: · May 20, 2010\\n                      - generic [ref=s1e181]:\\n                        - link \"Microsoft Studio H Building There are at least 441 amenities within 1 mile of Microsoft Studio H. Here\\'s a summary of th… campusbuilding.com\" [ref=s1e182]:\\n                          - /url: https://campusbuilding.com/b/microsoft-studio-h\\n                          - generic [ref=s1e183]:\\n                            - generic [ref=s1e184]:\\n                              - text: Microsoft Studio H Building\\n                              - contentinfo [ref=s1e186]: There are at least 441 amenities within 1 mile of\\n                                  Microsoft Studio H. Here\\'s a summary of th…\\n                            - generic [ref=s1e187]:\\n                              - generic [ref=s1e189]\\n                              - generic [ref=s1e192]: campusbuilding.com\\n              - generic [ref=s1e193]:\\n                - generic [ref=s1e195]:\\n                  - text: Feedback\\n                  - button \"Feedback Like\" [ref=s1e197]\\n                  - button \"Feedback Dislike\" [ref=s1e198]\\n      - list [ref=s1e199]:\\n        - listitem [ref=s1e200]:\\n          - link \"Microsoft\" [ref=s1e202]:\\n            - /url: https://www.microsoft.com/en-us/research/lab/microsoft-research-redmond/\\n            - generic [ref=s1e204]:\\n              - generic [ref=s1e206]\\n            - generic [ref=s1e207]:\\n              - text: Microsoft\\n              - generic [ref=s1e210]: https://www.microsoft.com › en-us › research › lab › ...\\n          - heading \"Microsoft Research Lab - Redmond - Microsoft Research\" [level=2] [ref=s1e212]:\\n            - link \"Microsoft Research Lab - Redmond - Microsoft Research\" [ref=s1e213]:\\n              - /url: https://www.microsoft.com/en-us/research/lab/microsoft-research-redmond/\\n          - generic [ref=s1e214]:\\n            - generic [ref=s1e216]:\\n              - list [ref=s1e218]:\\n                - listitem [ref=s1e219]:\\n                  - generic [ref=s1e221]:\\n                    - link \"Web page related images\" [ref=s1e222]:\\n                      - /url: /images/search?view=detailV2&ccid=wloBxYbF&id=2935EA20AFFDBD8BE5408325977F59B9C223BE65&thid=OIP.wloBxYbFlyxykavHItPjrwHaEK&mediaurl=https://www.microsoft.com/en-us/research/uploads/prod/2019/09/Jina-Shuh_Podcast_Site_09_2019_1400x788-1280x720.jpg&q=Microsoft\\n                          Building 99\\n                          address&ck=2A955D85573E45B5282A0D14F641B1D3&idpp=rc&idpview=singleimage&form=rc2idp\\n                - listitem [ref=s1e224]:\\n                  - generic [ref=s1e226]:\\n                    - link \"Web page related images\" [ref=s1e227]:\\n                      - /url: /images/search?view=detailV2&ccid=5umtXZtt&id=2935EA20AFFDBD8BE5409E4347300E10FF614100&thid=OIP.5umtXZttL9GCgDCHUvcXMAHaEK&mediaurl=https://www.microsoft.com/en-us/research/wp-content/uploads/2024/06/RF-Ep3-Recap-BlogHeroFeature-1400x788-1.jpg&q=Microsoft\\n                          Building 99\\n                          address&ck=200A413213746B198D7ECBAB99D78F6D&idpp=rc&idpview=singleimage&form=rc2idp\\n                - listitem [ref=s1e229]:\\n                  - generic [ref=s1e231]:\\n                    - link \"Web page related images\" [ref=s1e232]:\\n                      - /url: /images/search?view=detailV2&ccid=n+L4YRU1&id=2935EA20AFFDBD8BE5405B2B8CFB4C4B3275FBF5&thid=OIP.n-L4YRU1nH-mXzimQR4yiwHaEK&mediaurl=https://www.microsoft.com/en-us/research/uploads/prod/2023/10/Podcast_Insights_Madeline_Hero_Feature_No_Text_1400x788-960x540.png&q=Microsoft\\n                          Building 99\\n                          address&ck=EAED05EF011C3035948F31A2E52BDBF1&idpp=rc&idpview=singleimage&form=rc2idp\\n                - listitem [ref=s1e234]:\\n                  - generic [ref=s1e236]:\\n                    - link \"Web page related images\" [ref=s1e237]:\\n                      - /url: /images/search?view=detailV2&ccid=0j+0bcqx&id=2935EA20AFFDBD8BE5406AB81021186B77C5538D&thid=OIP.0j-0bcqxnBJ56WIEnV5eEgAAAA&mediaurl=https://www.microsoft.com/en-us/research/uploads/prod/2019/05/HUE_header_04_2019_1920x720-343x193.jpg&q=Microsoft\\n                          Building 99\\n                          address&ck=7DEDB051EC61BF82B729E3244983484A&idpp=rc&idpview=singleimage&form=rc2idp\\n            - paragraph [ref=s1e239]:\\n              - text: Mar 7, 2025\\n              - text: · Corporate Vice President and Managing Director, Microsoft Research\\n                  Redmond Address Microsoft Building 99, 14820 NE 36th Street,\\n                  Redmond, Washington, 98052 USA\\n        - listitem [ref=s1e241]:\\n          - link \"campusbuilding.com\" [ref=s1e243]:\\n            - /url: https://campusbuilding.com/c/microsoft-redmond-main-campus/\\n            - generic [ref=s1e245]:\\n              - generic [ref=s1e247]\\n            - generic [ref=s1e248]:\\n              - text: campusbuilding.com\\n              - generic [ref=s1e251]: https://campusbuilding.com › microsoft-re…\\n          - generic [ref=s1e254]:\\n            - generic [ref=s1e256]:\\n              - link \"/images/search?view=detailV2&ccid=cbwg8zEM&id=F7D283C10E5E7DCF7A1F79F6E84887B5FDD2AC1D&thid=OIP.cbwg8zEM_3qR98xadB9dSAHaHQ&mediaurl=https://campusbuilding.com/static/images/map_of_microsoft_redmond_main_campus_and_buildings.jpg&q=Microsoft+Building+99+address&ck=F76D45DFC43E694CFE9C00250746F505&idpp=rc&idpview=singleimage&form=rc2idp&mode=overlay\" [ref=s1e257]:\\n                - /url: javascript:void(0)\\n            - heading \"Microsoft Redmond Main Campus and Buildings\" [level=2] [ref=s1e259]:\\n              - link \"Microsoft Redmond Main Campus and Buildings\" [ref=s1e260]:\\n                - /url: https://campusbuilding.com/c/microsoft-redmond-main-campus/\\n            - paragraph [ref=s1e261]: There are 95 buildings at the Microsoft Redmond Main Campus.\\n        - listitem [ref=s1e263]:\\n          - link \"Mapcarta\" [ref=s1e265]:\\n            - /url: https://mapcarta.com/W93639217\\n            - generic [ref=s1e267]:\\n              - generic [ref=s1e269]\\n            - generic [ref=s1e270]:\\n              - text: Mapcarta\\n              - generic [ref=s1e273]: https://mapcarta.com\\n          - heading \"Building 99 Map - King County, Washington, USA - Mapcarta\" [level=2] [ref=s1e275]:\\n            - link \"Building 99 Map - King County, Washington, USA - Mapcarta\" [ref=s1e276]:\\n              - /url: https://mapcarta.com/W93639217\\n          - paragraph [ref=s1e278]: Building 99 is a building in King County, Puget Sound,\\n              Washington which is located on Northeast 36th Street. Building 99\\n              is situated nearby to the food court Microsoft Cafe 99 , as well\\n              as near …\\n        - listitem [ref=s1e279]:\\n          - link \"Microsoft\" [ref=s1e281]:\\n            - /url: https://www.microsoft.com/en-us/about/office-locations\\n            - generic [ref=s1e283]:\\n              - generic [ref=s1e285]\\n            - generic [ref=s1e286]:\\n              - text: Microsoft\\n              - generic [ref=s1e289]: https://www.microsoft.com › en-us › about …\\n          - generic [ref=s1e292]:\\n            - generic [ref=s1e294]:\\n              - link \"/images/search?view=detailV2&ccid=REaQfaXR&id=A8AF32107EC802C72A6A5E9DBAA47E48A3D34B0C&thid=OIP.REaQfaXRYleauLHxKbPYGQAAAA&mediaurl=https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/About-OfficeLocations–OutdoorRedmond30-32-6484x4323&q=Microsoft+Building+99+address&ck=6C580933DF439BBF1D848AAC10172FA1&idpp=rc&idpview=singleimage&form=rc2idp&mode=overlay\" [ref=s1e295]:\\n                - /url: javascript:void(0)\\n            - heading \"Microsoft Office Locations | About Microsoft\" [level=2] [ref=s1e297]:\\n              - link \"Microsoft Office Locations | About Microsoft\" [ref=s1e298]:\\n                - /url: https://www.microsoft.com/en-us/about/office-locations\\n            - paragraph [ref=s1e299]: Microsoft is based in Redmond, Washington with offices\\n                across the US. Learn more about these locations. Microsoft’s\\n                global headquarters are located on 500 acres in Redmond,\\n                Washington that includes public spaces, sports fields, …\\n        - generic [ref=s1e303]:\\n          - heading \"Videos of Microsoft Building 99 Address\" [level=2] [ref=s1e305]:\\n            - link \"Videos of Microsoft Building 99 Address\" [ref=s1e306]:\\n              - /url: /videos/search?q=Microsoft+Building+99+address&qpvt=Microsoft+Building+99+address&FORM=VDRE\\n          - generic [ref=s1e308]:\\n            - generic [ref=s1e310]: bing.com › videos\\n          - generic [ref=s1e312]:\\n            - generic [ref=s1e314]:\\n              - \\'link \"Interview and Q&A with Jenny Sabin, Creator of the Ada Installation in \\ue000Microsoft\\ue001 \\ue000Building\\ue001 \\ue00099\\ue001 from YouTube · Duration: 22 minutes 53 seconds · 1.4K views · uploaded on Oct 25, 2021 · uploaded by Microsoft Research · Click to play.\" [ref=s1e315]\\':\\n                - /url: https://www.youtube.com/watch?v=BtgiDwS7w84\\n                - generic [ref=s1e316]:\\n                  - generic [ref=s1e317]:\\n                    - img \"Interview and Q&A with Jenny Sabin, Creator of the\\n                      Ada Installation in Microsoft Building 99\" [ref=s1e319]\\n                    - generic [ref=s1e323]:\\n                      - generic [ref=s1e325]: 22:53\\n                  - generic [ref=s1e326]:\\n                    - generic \"Interview and Q&A with Jenny Sabin, Creator of the Ada Installation in Microsoft Building 99\" [ref=s1e327]:\\n                      - text: Interview and Q&A with Jenny Sabin, Creator of the Ada Installation in\\n                      - strong [ref=s1e328]: Microsoft\\n                      - strong [ref=s1e329]: Building\\n                      - strong [ref=s1e330]: \"99\"\\n                    - generic [ref=s1e331]:\\n                      - generic [ref=s1e332]:\\n                        - text: 1.4K views\\n                        - text: · Oct 25, 2021\\n                      - generic [ref=s1e335]:\\n                        - text: YouTube\\n                        - text: › Microsoft Research\\n            - generic [ref=s1e339]:\\n              - \\'link \"Inside \\ue000Microsoft\\ue001\\'\\'s Multi-Billion Dollar Headquarter from YouTube · Duration: 9 minutes 5 seconds · 3.9K views · uploaded on Jul 24, 2023 · uploaded by Lavish Woo · Click to play.\" [ref=s1e340]\\':\\n                - /url: /videos/riverview/relatedvideo?q=Microsoft+Building+99+address&mid=609C51D0DBD3F35EA148609C51D0DBD3F35EA148&FORM=VIRE\\n                - generic [ref=s1e341]:\\n                  - generic [ref=s1e342]:\\n                    - img \"Inside Microsoft\\'s Multi-Billion Dollar Headquarter\"\\n                      [ref=s1e344]\\n                    - generic [ref=s1e348]:\\n                      - generic [ref=s1e350]: 9:05\\n                  - generic [ref=s1e351]:\\n                    - generic \"Inside Microsoft\\'s Multi-Billion Dollar Headquarter\" [ref=s1e352]:\\n                      - text: Inside\\n                      - strong [ref=s1e353]: Microsoft\\n                      - text: \"\\'s Multi-Billion Dollar Headquarter\"\\n                    - generic [ref=s1e354]:\\n                      - generic [ref=s1e355]:\\n                        - text: 3.9K views\\n                        - text: · Jul 24, 2023\\n                      - generic [ref=s1e358]:\\n                        - text: YouTube\\n                        - text: › Lavish Woo\\n            - generic [ref=s1e362]:\\n              - \\'link \"Inside \\ue000Microsoft\\ue001\\'\\'s Insane Headquarters from YouTube · Duration: 10 minutes 15 seconds · 9K views · uploaded on Feb 27, 2022 · uploaded by Simply Tech · Click to play.\" [ref=s1e363]\\':\\n                - /url: /videos/riverview/relatedvideo?q=Microsoft+Building+99+address&mid=B2D9E016C5679BA025F2B2D9E016C5679BA025F2&FORM=VIRE\\n                - generic [ref=s1e364]:\\n                  - generic [ref=s1e365]:\\n                    - img \"Inside Microsoft\\'s Insane Headquarters\" [ref=s1e367]\\n                    - generic [ref=s1e371]:\\n                      - generic [ref=s1e373]: 10:15\\n                  - generic [ref=s1e374]:\\n                    - generic \"Inside Microsoft\\'s Insane Headquarters\" [ref=s1e375]:\\n                      - text: Inside\\n                      - strong [ref=s1e376]: Microsoft\\n                      - text: \"\\'s Insane Headquarters\"\\n                    - generic [ref=s1e377]:\\n                      - generic [ref=s1e378]:\\n                        - text: 9K views\\n                        - text: · Feb 27, 2022\\n                      - generic [ref=s1e381]:\\n                        - text: YouTube\\n                        - text: › Simply Tech\\n            - generic [ref=s1e385]:\\n              - \\'link \"Look Inside \\ue000Microsoft\\ue001\\'\\'s Massive Headquarters from YouTube · Duration: 4 minutes 20 seconds · 1.3K views · uploaded on Nov 27, 2022 · uploaded by Futurostructure - Infrastructure Of The Future · Click to play.\" [ref=s1e386]\\':\\n                - /url: /videos/riverview/relatedvideo?q=Microsoft+Building+99+address&mid=761115F09388C2C34DB2761115F09388C2C34DB2&FORM=VIRE\\n                - generic [ref=s1e387]:\\n                  - generic [ref=s1e388]:\\n                    - generic [ref=s1e390]\\n                    - generic [ref=s1e394]:\\n                      - generic [ref=s1e396]: 4:20\\n                  - generic [ref=s1e397]:\\n                    - generic \"Look Inside Microsoft\\'s Massive Headquarters\" [ref=s1e398]:\\n                      - text: Look Inside\\n                      - strong [ref=s1e399]: Microsoft\\n                      - text: \"\\'s Massive Headquarters\"\\n                    - generic [ref=s1e400]:\\n                      - generic [ref=s1e401]:\\n                        - text: 1.3K views\\n                        - text: · Nov 27, 2022\\n                      - generic [ref=s1e404]:\\n                        - text: YouTube\\n                        - text: › Futurostructure - Infrastructure Of The Future\\n        - listitem [ref=s1e407]:\\n          - link \"AES | Audio Engineering Society\" [ref=s1e409]:\\n            - /url: https://www.aes.org/sections/pnw/direct/ms_rsch.htm\\n            - generic [ref=s1e411]:\\n              - generic [ref=s1e413]\\n            - generic [ref=s1e414]:\\n              - text: AES | Audio Engineering Society\\n              - generic [ref=s1e417]: https://www.aes.org › sections › pnw › direct › ms_rsch.htm\\n          - heading \"Directions to Microsoft Research\" [level=2] [ref=s1e419]:\\n            - link \"Directions to Microsoft Research\":\\n              - /url: https://www.aes.org/sections/pnw/direct/ms_rsch.htm\\n          - paragraph [ref=s1e422]:\\n            - text: Oct 9, 2018\\n            - text: · Microsoft Research is located in Redmond, at the intersection of NE 36th\\n                Street and 148th Avenue NE. This is south of where Microsoft\\n                Studios are located. Microsoft Building 99\\n        - listitem [ref=s1e424]:\\n          - link \"campusbuilding.com\" [ref=s1e426]:\\n            - /url: https://campusbuilding.com/company/microsoft/\\n            - generic [ref=s1e428]:\\n              - generic [ref=s1e430]\\n            - generic [ref=s1e431]:\\n              - text: campusbuilding.com\\n              - generic [ref=s1e434]: https://campusbuilding.com › company › m…\\n          - generic [ref=s1e437]:\\n            - generic [ref=s1e439]:\\n              - link \"/images/search?view=detailV2&ccid=X/EPBTCi&id=CF9C24AC67A42A5C771971C522BF3F3A97CC6655&thid=OIP.X_EPBTCi30m94XHqT52lnwHaH3&mediaurl=https://campusbuilding.com/static/images/seattle_area_microsoft_buildings_map.jpg&q=Microsoft+Building+99+address&ck=B3AEC9A066B8C9D9A861D34C2D56B0FD&idpp=rc&idpview=singleimage&form=rc2idp&mode=overlay\" [ref=s1e440]:\\n                - /url: javascript:void(0)\\n            - heading \"Microsoft Corporate Locations and Headquarters\" [level=2] [ref=s1e442]:\\n              - link \"Microsoft Corporate Locations and Headquarters\" [ref=s1e443]:\\n                - /url: https://campusbuilding.com/company/microsoft/\\n            - paragraph [ref=s1e444]: Microsoft Corporate Locations and Headquarters In the\\n                Seattle Area, Microsoft has 6 campuses and 132 buildings. There\\n                have been 49 jobs posted in the last week.\\n        - listitem [ref=s1e446]:\\n          - link \"MapQuest\" [ref=s1e448]:\\n            - /url: https://www.mapquest.com/us/washington/microsoft-building-99-parking-garage-*********\\n            - generic [ref=s1e450]:\\n              - generic [ref=s1e452]\\n            - generic [ref=s1e453]:\\n              - text: MapQuest\\n              - generic [ref=s1e456]: https://www.mapquest.com › us › washington\\n          - heading \"Microsoft Building 99 Parking Garage - Official MapQuest\" [level=2] [ref=s1e458]:\\n            - link \"Microsoft Building 99 Parking Garage - Official MapQuest\":\\n              - /url: https://www.mapquest.com/us/washington/microsoft-building-99-parking-garage-*********\\n          - paragraph [ref=s1e461]: Microsoft Building 99 Parking Garage in Redmond, WA\\n              offers convenient parking services for employees and visitors of\\n              the company. The facility provides a secure and accessible\\n              location …\\n        - listitem [ref=s1e462]:\\n          - link \"Place Digger\" [ref=s1e464]:\\n            - /url: https://us.placedigger.com/microsoft-headquarters-and-visitor-center-redmon-seattle---usa27266365.html\\n            - generic [ref=s1e466]:\\n              - generic [ref=s1e468]\\n            - generic [ref=s1e469]:\\n              - text: Place Digger\\n              - generic [ref=s1e472]: https://us.placedigger.com › microsoft-headquarters...\\n          - heading \"Microsoft Headquarters & Visitor Center, Redmon (Seattle - U.S.A)\" [level=2] [ref=s1e474]:\\n            - link \"Microsoft Headquarters & Visitor Center, Redmon (Seattle - U.S.A)\":\\n              - /url: https://us.placedigger.com/microsoft-headquarters-and-visitor-center-redmon-seattle---usa27266365.html\\n          - paragraph [ref=s1e477]: Microsoft Headquarters & Visitor Center, Redmon (Seattle\\n              - U.S.A) is one of the popular Shopping & Retail located in 15010\\n              NE 36th Street, Building 92 ,Redmond listed under Corporate Office\\n              …\\n        - listitem [ref=s1e478]:\\n          - link \"cityseeker\" [ref=s1e480]:\\n            - /url: https://cityseeker.com/redmond-wa/735585-microsoft-building-99\\n            - generic [ref=s1e482]:\\n              - generic [ref=s1e484]\\n            - generic [ref=s1e485]:\\n              - text: cityseeker\\n              - generic [ref=s1e488]: https://cityseeker.com › redmond-wa\\n          - heading \"Microsoft Building 99, Redmond - cityseeker\" [level=2] [ref=s1e490]:\\n            - link \"Microsoft Building 99, Redmond - cityseeker\":\\n              - /url: https://cityseeker.com/redmond-wa/735585-microsoft-building-99\\n          - paragraph [ref=s1e493]: 14820 North East 36th Street, Microsoft Research Campus,\\n              Redmond, WA, United States, 98052\\n        - listitem [ref=s1e494]:\\n          - generic [ref=s1e495]:\\n            - heading \"Related searches for Microsoft Building 99 address\" [level=2] [ref=s1e496]:\\n              - text: Related searches for\\n              - strong [ref=s1e497]: Microsoft Building 99 address\\n            - list [ref=s1e498]:\\n              - listitem [ref=s1e499]:\\n                - link \"microsoft 99 redmond\" [ref=s1e500]:\\n                  - /url: /search?q=microsoft+99+redmond&FORM=QSRE1\\n                  - generic [ref=s1e502]:\\n                    - text: microsoft 99\\n                    - strong [ref=s1e503]: redmond\\n              - listitem [ref=s1e504]:\\n                - link \"microsoft building 99 parking garage\" [ref=s1e505]:\\n                  - /url: /search?q=microsoft+building+99+parking+garage&FORM=QSRE2\\n                  - generic [ref=s1e507]:\\n                    - text: microsoft building 99\\n                    - strong [ref=s1e508]: parking garage\\n              - listitem [ref=s1e509]:\\n                - link \"inside microsoft headquarters\" [ref=s1e510]:\\n                  - /url: /search?q=inside+microsoft+headquarters&FORM=QSRE3\\n                  - generic [ref=s1e512]:\\n                    - strong [ref=s1e513]: inside\\n                    - text: microsoft\\n                    - strong [ref=s1e514]: headquarters\\n              - listitem [ref=s1e515]:\\n                - link \"microsoft anechoic chamber visit\" [ref=s1e516]:\\n                  - /url: /search?q=microsoft+anechoic+chamber+visit&FORM=QSRE4\\n                  - generic [ref=s1e518]:\\n                    - text: microsoft\\n                    - strong [ref=s1e519]: anechoic chamber visit\\n              - listitem [ref=s1e520]:\\n                - link \"microsoft redmond wa 98052\" [ref=s1e521]:\\n                  - /url: /search?q=microsoft+redmond+wa+98052&FORM=QSRE5\\n                  - generic [ref=s1e523]:\\n                    - text: microsoft\\n                    - strong [ref=s1e524]: redmond wa 98052\\n              - listitem [ref=s1e525]:\\n                - link \"microsoft anechoic chamber\" [ref=s1e526]:\\n                  - /url: /search?q=microsoft+anechoic+chamber&FORM=QSRE6\\n                  - generic [ref=s1e528]:\\n                    - text: microsoft\\n                    - strong [ref=s1e529]: anechoic chamber\\n              - listitem [ref=s1e530]:\\n                - link \"microsoft research redmond\" [ref=s1e531]:\\n                  - /url: /search?q=microsoft+research+redmond&FORM=QSRE7\\n                  - generic [ref=s1e533]:\\n                    - text: microsoft\\n                    - strong [ref=s1e534]: research redmond\\n              - listitem [ref=s1e535]:\\n                - link \"main microsoft campus\" [ref=s1e536]:\\n                  - /url: /search?q=main+microsoft+campus&FORM=QSRE8\\n                  - generic [ref=s1e538]:\\n                    - strong [ref=s1e539]: main\\n                    - text: microsoft\\n                    - strong [ref=s1e540]: campus\\n        - listitem [ref=s1e541]:\\n          - navigation \"More results for Microsoft Building 99 address\":\\n            - list:\\n              - listitem [ref=s1e544]: \"1\"\\n              - listitem [ref=s1e546]:\\n                - link \"Page 2\" [ref=s1e547]:\\n                  - /url: /search?q=Microsoft+Building+99+address&FPIG=629D5CE705334C83937EBCDDD6C544D1&first=11&FORM=PERE\\n                  - text: \"2\"\\n              - listitem [ref=s1e548]:\\n                - link \"Page 3\" [ref=s1e549]:\\n                  - /url: /search?q=Microsoft+Building+99+address&FPIG=629D5CE705334C83937EBCDDD6C544D1&first=21&FORM=PERE1\\n                  - text: \"3\"\\n              - listitem [ref=s1e550]:\\n                - link \"Page 4\" [ref=s1e551]:\\n                  - /url: /search?q=Microsoft+Building+99+address&FPIG=629D5CE705334C83937EBCDDD6C544D1&first=31&FORM=PERE2\\n                  - text: \"4\"\\n              - listitem [ref=s1e552]:\\n                - link \"Next page\" [ref=s1e553]:\\n                  - /url: /search?q=Microsoft+Building+99+address&FPIG=629D5CE705334C83937EBCDDD6C544D1&first=11&FORM=PORE\\n      - complementary \"Additional Results\" [ref=s1e554]:\\n        - list [ref=s1e555]:\\n          - listitem [ref=s1e556]:\\n            - generic [ref=s1e557]:\\n              - generic [ref=s1e559]:\\n                - generic [ref=s1e560]:\\n                  - generic [ref=s1e562]:\\n                    - heading \"Microsoft Building 99\" [level=2] [ref=s1e564]:\\n                      - link \"Microsoft Building 99\" [ref=s1e565]:\\n                        - /url: https://www.bing.com/alink/link?url=https%3a%2f%2fwww.microsoft.com%2f&source=serp-local&h=k6XBdzEhm26dMOehxO4ANkPmLgfNzfJEHe2c3sGHZUI%3d&p=lw_tpt&ig=629D5CE705334C83937EBCDDD6C544D1&ypid=YN873x101353856\\n                    - generic [ref=s1e566]:\\n                      - button \"Save\" [ref=s1e568]\\n                      - generic [ref=s1e570]:\\n                        - button \"Share\" [ref=s1e571]\\n                  - generic [ref=s1e574]: Software development in Redmond, Wa\\n                  - generic [ref=s1e576]:\\n                    - generic [ref=s1e578]:\\n                      - generic [ref=s1e579]:\\n                        - link \"Website\" [ref=s1e580]:\\n                          - /url: https://www.bing.com/alink/link?url=https%3a%2f%2fwww.microsoft.com%2f&source=serp-local&h=k6XBdzEhm26dMOehxO4ANkPmLgfNzfJEHe2c3sGHZUI%3d&p=lw_tp&ig=629D5CE705334C83937EBCDDD6C544D1&ypid=YN873x101353856\\n                          - img [ref=s1e582]\\n                        - link \"Directions\" [ref=s1e586]:\\n                          - /url: /maps?&mepi=127~Directions~Unknown~Direction_Button&ty=0&rtp=pos.47.64213943481445_-122.14218139648438__Microsoft%20Building%2099__e_~&mode=d&v=2&sV=1\\n                          - img [ref=s1e588]\\n                    - list [ref=s1e592]:\\n                      - listitem [ref=s1e593]:\\n                        - button \"Prices\" [ref=s1e594]\\n                  - generic [ref=s1e596]:\\n                    - group \"Address\" [ref=s1e597]:\\n                      - img [ref=s1e598]\\n                      - generic [ref=s1e602]:\\n                        - link \"14820 NE 36th St, Redmond, Wa 98052\" [ref=s1e604]:\\n                          - /url: /maps?&mepi=127~~Unknown~Address_Link&ty=18&q=Microsoft%20Building%2099&ss=ypid.YN873x101353856&ppois=47.64213943481445_-122.14218139648438_Microsoft%20Building%2099_YN873x101353856~&cp=47.642139~-122.142181&v=2&sV=1&FORM=MPSRPL\\n                        - text: · 1.1 mi\\n                    - group \"Phone\" [ref=s1e605]:\\n                      - img [ref=s1e606]\\n                      - link \"Phone (*************\" [ref=s1e610]:\\n                        - /url: tel:4258828080\\n                        - text: (*************\\n                    - generic [ref=s1e611]:\\n                      - img [ref=s1e612]\\n                      - generic [ref=s1e615]:\\n                        - button \"Suggest an edit\" [ref=s1e616]\\n                        - text: ·\\n                        - text: Your business?\\n                        - link \"Claim now\" [ref=s1e618]:\\n                          - /url: https://www.bingplaces.com/DashBoard/Edit?Id=YN873x101353856&market=en-US&src=SERPIC\\n                  - generic [ref=s1e620]:\\n                    - heading \"Add more information\" [level=2] [ref=s1e621]\\n                    - generic [ref=s1e623]:\\n                      - generic [ref=s1e624]:\\n                        - img [ref=s1e625]\\n                        - button \"Add hours\" [ref=s1e629]\\n                    - generic [ref=s1e631]:\\n                      - generic [ref=s1e633]:\\n                        - button \"Add photos\" [ref=s1e634]:\\n                          - img [ref=s1e635]\\n                          - text: Add photos\\n                  - generic [ref=s1e640]:\\n                    - text: Microsoft creates platforms and tools powered by AI to deliver innovative\\n                        solutions that meet the evolving needs of our customers.\\n                        The technology …\\n                    - link \"See more See more\" [ref=s1e642]:\\n                      - /url: https://news.microsoft.com/facts-about-microsoft\\n                      - text: See more\\n              - generic [ref=s1e644]:\\n                - heading \"Frequently asked questions\" [level=2] [ref=s1e645]\\n                - generic [ref=s1e646]:\\n                  - generic [ref=s1e647]:\\n                    - generic [ref=s1e649]:\\n                      - text: \"Q:\"\\n                      - generic [ref=s1e652]: What is the difference between Microsoft 365\\n                          (subscription) and Office 2024 (one-time purchase)?\\n                    - generic [ref=s1e654]:\\n                      - generic [ref=s1e655]:\\n                        - text: \"A:\"\\n                        - generic [ref=s1e658]:\\n                          - text: Microsoft 365 is a subscription that includes the most collaborative,\\n                              up-to-date features in one seamless, integrated\\n                              experience. Microsoft 365 includes the …\\n                          - button \"Show more\" [ref=s1e660]\\n                      - generic [ref=s1e663]:\\n                        - link \"Read more\" [ref=s1e665]:\\n                          - /url: https://microsoft.com/en-us/microsoft-365/microsoft-365-for-home-and-school-faq\\n                  - link \"See all 50 questions\" [ref=s1e667]:\\n                    - /url: \"#\"\\n                  - generic [ref=s1e668]:\\n                    - text: \"Data from:\"\\n                    - link \"BusinessWebsite\" [ref=s1e669]:\\n                      - /url: https://microsoft.com/en-us/microsoft-365/microsoft-365-for-home-and-school-faq\\n              - generic [ref=s1e672]:\\n                - heading \"Social profiles\" [level=2] [ref=s1e673]\\n                - group \"Social profiles\" [ref=s1e675]:\\n                  - list [ref=s1e676]:\\n                    - listitem [ref=s1e677]:\\n                      - link \"Facebook icon Facebook\" [ref=s1e678]:\\n                        - /url: https://www.facebook.com/Microsoft\\n                        - img \"Facebook icon\" [ref=s1e680]\\n                        - text: Facebook\\n                    - listitem [ref=s1e682]:\\n                      - link \"X icon X\" [ref=s1e683]:\\n                        - /url: https://twitter.com/microsoft\\n                        - img \"X icon\" [ref=s1e685]\\n                        - text: X\\n                    - listitem [ref=s1e687]:\\n                      - link \"LinkedIn icon LinkedIn\" [ref=s1e688]:\\n                        - /url: https://www.linkedin.com/company/microsoft\\n                        - img \"LinkedIn icon\" [ref=s1e690]\\n                        - text: LinkedIn\\n              - generic [ref=s1e693]:\\n                - heading \"People also search for\" [level=2] [ref=s1e694]\\n                - generic [ref=s1e695]:\\n                  - text: Software development\\n                  - generic [ref=s1e698]:\\n                    - generic [ref=s1e700]:\\n                      - generic [ref=s1e702]:\\n                        - list \"Please use arrow keys to navigate\" [ref=s1e703]:\\n                          - listitem [ref=s1e704]:\\n                            - link \"Acumatica Cloud ERP Acumatica Cloud ERP\" [ref=s1e705]:\\n                              - /url: /search?q=Acumatica+Cloud+ERP&filters=local_ypid%3a%22873x11271913447243333430%22&FORM=SNAPST\\n                              - generic [ref=s1e706]:\\n                                - img \"Acumatica Cloud ERP\" [ref=s1e708]\\n                                - generic [ref=s1e710]: Acumatica Cloud ERP\\n                          - listitem [ref=s1e711]:\\n                            - link \"AscendoSoft Inc. AscendoSoft Inc.\" [ref=s1e712]:\\n                              - /url: /search?q=AscendoSoft+Inc.&filters=local_ypid%3a%22873x109094970%22&FORM=SNAPST\\n                              - generic [ref=s1e713]:\\n                                - img \"AscendoSoft Inc.\" [ref=s1e715]\\n                                - generic [ref=s1e717]: AscendoSoft Inc.\\n                          - listitem [ref=s1e718]:\\n                            - link \"TecAce Software, Ltd TecAce Software, Ltd\" [ref=s1e719]:\\n                              - /url: /search?q=TecAce+Software%2c+Ltd&filters=local_ypid%3a%22873x100939365%22&FORM=SNAPST\\n                              - generic [ref=s1e720]:\\n                                - img \"TecAce Software, Ltd\" [ref=s1e722]\\n                                - generic [ref=s1e724]: TecAce Software, Ltd\\n                          - listitem [ref=s1e725]:\\n                            - link \"Cirkled In\" [ref=s1e726]:\\n                              - /url: /search?q=Cirkled+In&filters=local_ypid%3a%22873x10666105865648718724%22&FORM=SNAPST\\n                              - generic [ref=s1e727]:\\n                                - generic [ref=s1e729]\\n                                - generic [ref=s1e731]: Cirkled In\\n                          - listitem [ref=s1e732]:\\n                            - link \"Vishwak Solutions Inc\" [ref=s1e733]:\\n                              - /url: /search?q=Vishwak+Solutions+Inc&filters=local_ypid%3a%22873x110407396%22&FORM=SNAPST\\n                              - generic [ref=s1e734]:\\n                                - generic [ref=s1e736]\\n                                - generic [ref=s1e738]: Vishwak Solutions Inc\\n                - generic [ref=s1e739]:\\n                  - text: IT service & computer repair\\n                  - generic [ref=s1e742]:\\n                    - generic [ref=s1e744]:\\n                      - generic [ref=s1e746]:\\n                        - list \"Please use arrow keys to navigate\" [ref=s1e747]:\\n                          - listitem [ref=s1e748]:\\n                            - link \"Kirwan Computer\" [ref=s1e749]:\\n                              - /url: /search?q=Kirwan+Computer&filters=local_ypid%3a%22873x114284629%22&FORM=SNAPST\\n                              - generic [ref=s1e750]:\\n                                - generic [ref=s1e752]\\n                                - generic [ref=s1e754]: Kirwan Computer\\n                          - listitem [ref=s1e755]:\\n                            - link \"Digital forensics\" [ref=s1e756]:\\n                              - /url: /search?q=Digital+forensics&filters=local_ypid%3a%22873x13044985277069239607%22&FORM=SNAPST\\n                              - generic [ref=s1e757]:\\n                                - generic [ref=s1e759]\\n                                - generic [ref=s1e761]: Digital forensics\\n          - listitem [ref=s1e763]:\\n            - generic [ref=s1e765]:\\n              - generic [ref=s1e766]:\\n                - heading \"Related searches for Microsoft Building 99 address\" [level=2] [ref=s1e768]:\\n                  - text: Related searches for\\n                  - strong [ref=s1e769]: Microsoft Building 99 address\\n                - generic [ref=s1e770]:\\n                  - link \"microsoft 99 redmond\" [ref=s1e772]:\\n                    - /url: /search?q=microsoft+99+redmond&FORM=R5FD\\n                    - generic [ref=s1e774]:\\n                      - text: microsoft 99\\n                      - strong [ref=s1e775]: redmond\\n                  - link \"microsoft building 99 parking garage\" [ref=s1e777]:\\n                    - /url: /search?q=microsoft+building+99+parking+garage&FORM=R5FD1\\n                    - generic [ref=s1e779]:\\n                      - text: microsoft building 99\\n                      - strong [ref=s1e780]: parking garage\\n                  - link \"inside microsoft headquarters\" [ref=s1e782]:\\n                    - /url: /search?q=inside+microsoft+headquarters&FORM=R5FD2\\n                    - generic [ref=s1e784]:\\n                      - strong [ref=s1e785]: inside\\n                      - text: microsoft\\n                      - strong [ref=s1e786]: headquarters\\n                  - link \"microsoft anechoic chamber visit\" [ref=s1e788]:\\n                    - /url: /search?q=microsoft+anechoic+chamber+visit&FORM=R5FD3\\n                    - generic [ref=s1e790]:\\n                      - text: microsoft\\n                      - strong [ref=s1e791]: anechoic chamber visit\\n                  - link \"microsoft redmond wa 98052\" [ref=s1e793]:\\n                    - /url: /search?q=microsoft+redmond+wa+98052&FORM=R5FD4\\n                    - generic [ref=s1e795]:\\n                      - text: microsoft\\n                      - strong [ref=s1e796]: redmond wa 98052\\n                  - link \"microsoft anechoic chamber\" [ref=s1e798]:\\n                    - /url: /search?q=microsoft+anechoic+chamber&FORM=R5FD5\\n                    - generic [ref=s1e800]:\\n                      - text: microsoft\\n                      - strong [ref=s1e801]: anechoic chamber\\n                  - link \"microsoft research redmond\" [ref=s1e803]:\\n                    - /url: /search?q=microsoft+research+redmond&FORM=R5FD6\\n                    - generic [ref=s1e805]:\\n                      - text: microsoft\\n                      - strong [ref=s1e806]: research redmond\\n                  - link \"main microsoft campus\" [ref=s1e808]:\\n                    - /url: /search?q=main+microsoft+campus&FORM=R5FD7\\n                    - generic [ref=s1e810]:\\n                      - strong [ref=s1e811]: main\\n                      - text: microsoft\\n                      - strong [ref=s1e812]: campus\\n  - button \"Feedback\" [ref=s1e813]\\n  - contentinfo \"Footer\" [ref=s1e815]:\\n    - generic [ref=s1e816]:\\n      - text: © 2025 Microsoft\\n      - list:\\n        - listitem [ref=s1e819]:\\n          - link \"Privacy and Cookies\" [ref=s1e820]:\\n            - /url: http://go.microsoft.com/fwlink/?LinkId=521839\\n        - listitem [ref=s1e821]:\\n          - link \"Legal\" [ref=s1e822]:\\n            - /url: http://go.microsoft.com/fwlink/?LinkID=246338\\n        - listitem [ref=s1e823]:\\n          - link \"Advertise\" [ref=s1e824]:\\n            - /url: https://go.microsoft.com/fwlink/?linkid=868922\\n        - listitem [ref=s1e825]:\\n          - link \"About our ads\" [ref=s1e826]:\\n            - /url: http://go.microsoft.com/fwlink/?LinkID=286759\\n        - listitem [ref=s1e827]:\\n          - link \"Help\" [ref=s1e828]:\\n            - /url: https://support.microsoft.com/topic/82d20721-2d6f-4012-a13d-d1910ccf203f\\n        - listitem [ref=s1e829]:\\n          - button \"Feedback\" [ref=s1e830]\\n        - listitem [ref=s1e831]:\\n          - link \"Your Privacy Choices\" [ref=s1e832]:\\n            - /url: https://go.microsoft.com/fwlink/?linkid=2214802\\n        - listitem [ref=s1e833]:\\n          - link \"Consumer Health Privacy\" [ref=s1e834]:\\n            - /url: https://go.microsoft.com/fwlink/?linkid=2259814\\n    - link \"🐞\" [ref=s1e835]:\\n      - /url: javascript:void(0)\\n```')] is_error=False\n", "---------Final Response-----------\n", "The address of Microsoft Building 99 is 14820 NE 36th Street, Redmond, WA 98052, United States.\n"]}], "source": ["from autogen_core import AgentId, SingleThreadedAgentRuntime\n", "from autogen_core.model_context import BufferedChatCompletionContext\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "from autogen_ext.tools.mcp import McpWorkbench, SseServerParams\n", "\n", "playwright_server_params = SseServerParams(\n", "    url=\"http://localhost:8931/sse\",\n", ")\n", "\n", "# Start the workbench in a context manager.\n", "# You can also start and stop the workbench using `workbench.start()` and `workbench.stop()`.\n", "async with <PERSON><PERSON>pWorkbench(playwright_server_params) as workbench:  # type: ignore\n", "    # Create a single-threaded agent runtime.\n", "    runtime = SingleThreadedAgentRuntime()\n", "\n", "    # Register the agent with the runtime.\n", "    await WorkbenchAgent.register(\n", "        runtime=runtime,\n", "        type=\"WebAgent\",\n", "        factory=lambda: WorkbenchAgent(\n", "            model_client=OpenAIChatCompletionClient(model=\"gpt-4.1-nano\"),\n", "            model_context=BufferedChatCompletionContext(buffer_size=10),\n", "            workbench=workbench,\n", "        ),\n", "    )\n", "\n", "    # Start the runtime.\n", "    runtime.start()\n", "\n", "    # Send a message to the agent.\n", "    await runtime.send_message(\n", "        Message(content=\"Use Bing to find out the address of Microsoft Building 99\"),\n", "        recipient=AgentId(\"WebAgent\", \"default\"),\n", "    )\n", "\n", "    # Stop the runtime.\n", "    await runtime.stop()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}