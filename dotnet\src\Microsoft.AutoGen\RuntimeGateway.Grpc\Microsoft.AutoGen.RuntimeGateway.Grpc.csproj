<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <ItemGroup>
    <ProjectReference Include="..\Contracts\Microsoft.AutoGen.Contracts.csproj" />
    <ProjectReference Include="..\Core\Microsoft.AutoGen.Core.csproj" />
    <ProjectReference Include="..\Core.Grpc\Microsoft.AutoGen.Core.Grpc.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Reminders" />
    <PackageReference Include="Microsoft.Orleans.Persistence.Memory" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
	  <PackageReference Include="Microsoft.Orleans.CodeGenerator">
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	    <PrivateAssets>all</PrivateAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.Orleans.Serialization" />
		<PackageReference Include="Microsoft.Orleans.Serialization.Protobuf" />
		<PackageReference Include="Microsoft.Orleans.Server" />
		<PackageReference Include="Microsoft.Orleans.Streaming" />
		<PackageReference Include="Microsoft.Orleans.Sdk" />
		<PackageReference Include="Microsoft.Orleans.Runtime" />
		<PackageReference Include="Microsoft.Orleans.Persistence.Cosmos" />
		<PackageReference Include="Microsoft.Orleans.Clustering.Cosmos" />
		<PackageReference Include="Microsoft.Orleans.Reminders.Cosmos" />
		<PackageReference Include="Microsoft.Orleans.Streaming.EventHubs" />
		<PackageReference Include="OrleansDashboard" />
	</ItemGroup>

	<ItemGroup>
    	<PackageReference Include="Grpc.AspNetCore" />
    	<PackageReference Include="Grpc.Net.ClientFactory" />
    	<PackageReference Include="Grpc.Tools" PrivateAssets="All" />
	</ItemGroup>
</Project>