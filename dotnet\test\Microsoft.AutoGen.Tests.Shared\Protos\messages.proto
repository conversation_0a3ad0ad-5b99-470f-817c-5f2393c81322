syntax = "proto3";

package tests;

option csharp_namespace = "Tests.Events";
message TextMessage {
    string message = 1;
    string source = 2;
}
message Hello {
      string message = 1;
}
message InputProcessed {
      string route = 1;
}
message Output {
      string message = 1;
}
message OutputWritten {
      string route = 1;
}
message IOError {
      string message = 1;
}
message NewMessageReceived {
      string message = 1;
}
message ResponseGenerated {
      string response = 1;
}
message GoodBye {
      string message = 1;
}
message MessageStored {
      string message = 1;
}
message ConversationClosed {
  string user_id = 1;
  string user_message = 2;
}
message Shutdown {
      string message = 1;
}
