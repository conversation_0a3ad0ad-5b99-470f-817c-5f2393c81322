<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

	<ItemGroup>
		<ProjectReference Include="..\Contracts\Microsoft.AutoGen.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="Microsoft.Extensions.Logging" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
	</ItemGroup>
</Project>
