<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/nuget/nuget-package.props" />

  <ItemGroup>
	  <PackageReference Include="Azure.AI.OpenAI" />
    <PackageReference Include="Microsoft.Extensions.Options.DataAnnotations" />
	  <PackageReference Include="Microsoft.SemanticKernel" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Qdrant" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Memory" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="../../Core/Microsoft.AutoGen.Core.csproj" />
    <ProjectReference Include="../MEAI/Microsoft.AutoGen.Extensions.MEAI.csproj" />
  </ItemGroup>

</Project>
